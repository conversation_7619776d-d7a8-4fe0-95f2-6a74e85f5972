package com.sinoair.core.webservice.hongkongZTO;

import com.alibaba.druid.sql.visitor.SQLASTOutputVisitor;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.FastJsonUtils;
import com.sinoair.core.webservice.hongkongZTO.requestpo.*;
import com.sinoair.core.webservice.hongkongZTO.responsepo.HKZTOAddOrderResponsePO;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class HongKongAPIUtilTest extends CommonTestCase {

    @Before
    public void setUp() throws Exception {

    }

    @Test
    public void getSelfExtractingPoint() {
        String data = "";
        String actualResult = HongKongAPIUtil.getSelfExtractingPoint(data);
        System.out.println(actualResult);
    }

    @Test
    public void addOrder(){
        HKZTOAddOrderRequestPO hkztoAddOrderRequestPO=new HKZTOAddOrderRequestPO();
        Order_1 order_1 = hkztoAddOrderRequestPO.getOrder().getOrder_1();
        order_1.setWoid("egabcd-1sdfa");
        order_1.setCustomer_name("測試啊測試1");
        order_1.setCustomer_phone_area_code("852");
        order_1.setCustomer_phone("12345678");
        order_1.setCustomer_address("銀河系太陽系地球亞洲中國香港沙田發達路發達大廈地下18層18號");
        order_1.setCustomer_email("");
        order_1.setPrice(88);
        order_1.setType(1001);
//        order_1.setPickup_password("EG12345A");    //采用派送方式，不需要自取密码
        Parcel_1 parcel_1 = order_1.getParcel().getParcel_1();
        parcel_1.setWeight(10);
        parcel_1.setTrack_no( "egabcd-1sdfa");

        HKZTOAddOrderResponsePO hkztoAddOrderResponsePO=HongKongAPIUtil.addOrder(hkztoAddOrderRequestPO);
        String actualResponseData=HongKongAPIUtil.string2Unicode(FastJsonUtils.toJSONNoFeatures(hkztoAddOrderResponsePO));
        System.out.println(actualResponseData);
    }

/*

    @Test
    public void addOrder1(){
        HKZTOAddOrderRequestPO hkztoAddOrderRequestPO=new HKZTOAddOrderRequestPO();
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setWoid("WCL0422012");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setCustomer_name("KELLIE PAPWORTH");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setCustomer_phone_area_code("852");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setCustomer_phone("412482655");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setCustomer_address("30 COWRIE ROAD TORQUAY VICTORIA AU BELLBRAE");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setCustomer_email("<EMAIL>");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setPrice(88);
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setType(1001);
        hkztoAddOrderRequestPO.getOrder().getOrder_1().setPickup_password("EG12345A");
        hkztoAddOrderRequestPO.getOrder().getOrder_1().getParcel().getParcel_1().setWeight(1.0);
        hkztoAddOrderRequestPO.getOrder().getOrder_1().getParcel().getParcel_1().setTrack_no("AAAAAAAA");
        HKZTOAddOrderResponsePO hkztoAddOrderResponsePO=HongKongAPIUtil.addOrder(hkztoAddOrderRequestPO);
        String actualResponseData=HongKongAPIUtil.string2Unicode(FastJsonUtils.toJSONNoFeatures(hkztoAddOrderResponsePO));
        System.out.println(actualResponseData);
    }
*/


/*

    @Test
    public void addOrder2(){
       String requestData="  {\n" +
                "\t\"order\": {\n" +
                "\t\t\"order_1\": {\n" +
                "\t\t\t\"woid\": \"egabcd-2\",\n" +
                "\t\t\t\"customer_name\": \"\\u6e2c\\u8a66\\u554a\\u6e2c\\u8a661\",\n" +
                "\t\t\t\"customer_phone_area_code\": \"852\",\n" +
                "\t\t\t\"customer_phone\": \"12345678\",\n" +
                "\t\t\t\"customer_address\": \"\\u9280\\u6cb3\\u7cfb\\u592a\\u967d\\u7cfb\\u5730\\u7403\\u4e9e\\u6d32\\u4e2d\\u570b\\u9999\\u6e2f\\u6c99\\u7530\\u767c\\u9054\\u8def\\u767c\\u9054\\u5927\\u5ec8\\u5730\\u4e0b18\\u5c6418\\u865f\",\n" +
                "\t\t\t\"customer_email\": \"\",\n" +
                "\t\t\t\"price\": 88,\n" +
                "\t\t\t\"type\":1001 ,\n" +
                "\t\t\t\"pickup_password\": \"TT12345A\",\n" +
                "\t\t\t\"parcel\": {\n" +
                "\t\t\t\t\"parcel_1\": {\n" +
                "\t\t\t\t\t\"weight\": \"10\",\n" +
                "\t\t\t\t\t\"track_no\": \"AAAAAAAB\"\n" +
                "\t\t\t\t}\n" +
                "\t\t\t}\n" +
                "\t\t}\n" +
                "\t}\n" +
                "}";
        HKZTOAddOrderRequestPO hkztoAddOrderRequestPO=(HKZTOAddOrderRequestPO)FastJsonUtils.toBean(requestData,HKZTOAddOrderRequestPO.class);
        HKZTOAddOrderResponsePO hkztoAddOrderResponsePO=HongKongAPIUtil.addOrder(hkztoAddOrderRequestPO);
        String actualResponseData=HongKongAPIUtil.string2Unicode(FastJsonUtils.toJSONNoFeatures(hkztoAddOrderResponsePO));
        System.out.println(actualResponseData);
    }

*/


    @Test
    public void generateSign() {
        String data = "{\"order\":{\"order_1\":{\"woid\":\"egabcd-1\",\"customer_name\":\"\\u6e2c\\u8a66\\u554a\\u6e2c\\u8a661\",\"customer_phone_area_code\":\"852\",\"customer_phone\":\"12345678\",\"customer_address\":\"\\u9280\\u6cb3\\u7cfb\\u592a\\u967d\\u7cfb\\u5730\\u7403\\u4e9e\\u6d32\\u4e2d\\u570b\\u9999\\u6e2f\\u6c99\\u7530\\u767c\\u9054\\u8def\\u767c\\u9054\\u5927\\u5ec8\\u5730\\u4e0b18\\u5c6418\\u865f\",\"customer_email\":\"\",\"price\":88,\"type\":104299,\"pickup_password\":\"EG12345A\",\"parcel\":{\"parcel_1\":{\"weight\":\"10\",\"track_no\":\"AAAAAAAA\"},\"parcel_2\":{\"weight\":\"9\",\"track_no\":\"BBBBBBBB\"}}},\"order_2\":{\"woid\":\"egabcd-2\",\"customer_name\":\"\\u6e2c\\u8a66\\u554a\\u6e2c\\u8a662\",\"customer_phone_area_code\":\"852\",\"customer_phone\":\"12345678\",\"customer_address\":\"\\u9280\\u6cb3\\u7cfb\\u592a\\u967d\\u7cfb\\u5730\\u7403\\u4e9e\\u6d32\\u4e2d\\u570b\\u9999\\u6e2f\\u6c99\\u7530\\u767c\\u9054\\u8def\\u767c\\u9054\\u5927\\u5ec8\\u5730\\u4e0b18\\u5c6418\\u865f\",\"customer_email\":\"\",\"price\":88,\"type\":104299,\"pickup_password\":\"EG654322\",\"parcel\":{\"parcel_1\":{\"weight\":\"10\",\"track_no\":\"CCCCCCCC\"},\"parcel_2\":{\"weight\":\"9\",\"track_no\":\"DDDDDDDD\"}}}}}";
        String actualResult = HongKongAPIUtil.generateSign(data);
        String expectResult = "00a5e04b8044718bc897f1774fe157ff16d87195707ac9db849979d22b3dd5d6";
        assertEquals(expectResult, actualResult);
    }

    @Test
    public void generateSignTest() {
        String data = "{\"order\":{\"order_1\":{\"woid\":\"WCL0422082\",\"latest\":\"\"}}}";
        String actualResult = HongKongAPIUtil.generateSign(data);
        System.out.println("actualResult = " + actualResult);
    }

}