package com.sinoair.core.utils;

import com.sinoair.ceop.domain.model.Expressassignment;
import org.junit.Test;

import java.util.Date;

/**
 * Created by XueQB on 2016/6/29.
 */
public class DateUtilTest {

    @Test
    public void parseDate() throws Exception {
        System.out.println(DateUtil.parseDate("yyyy-MM-dd HH:mm:ss", "2016-06-29 10:22:33"));
    }

    @Test
    public void convertDate2String() throws Exception {
        Date date1 = DateUtil.parseDate("yyyy-MM-dd", "2020-06-17");
        Expressassignment eam = new Expressassignment();
        eam.setEta(date1);
        //Date date1 = DateUtil.parseDate("yyyy-MM-dd HH:mm:ss", "2020-06-17 12:13:55");
        String dateS = DateUtil.convertDate2String(eam.getEta(), "yyyy-MM-dd'T'HH:mm:ss");
        System.out.println(dateS);
        String dateS2 = DateUtil.convertDate2String(new Date(), "yyyyMMdd-HHmmss.SSS");
        System.out.println(dateS2);
        System.out.println(DateUtil.getFirstDateOfMonth());
    }

}