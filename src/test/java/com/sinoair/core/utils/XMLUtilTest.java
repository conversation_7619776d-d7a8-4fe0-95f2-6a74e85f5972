package com.sinoair.core.utils;

import com.sinoair.ceop.service.ups.shipAccept.requestVO.ShipmentAcceptRequest;
import com.sinoair.ceop.service.ups.shipConfirm.requestVO.AccessRequest;
import com.sinoair.ceop.service.ups.shipConfirm.requestVO.ShipmentConfirmRequest;
import com.sinoair.ceop.service.ups.shipConfirm.responseVO.ShipmentConfirmResponse;
import com.sinoair.ceop.service.ups.voidShipment.requestVO.VoidShipmentRequest;
import org.junit.Test;

import javax.xml.bind.JAXBException;
import java.io.FileNotFoundException;

import static org.junit.Assert.*;

public class XMLUtilTest {

    @Test
    public void beanToXml() throws JAXBException, FileNotFoundException {
        System.out.println("------------------------------------访问权限-------------------------------------------");
        AccessRequest accessRequest=new AccessRequest();
        String str1=XMLUtil.beanToXml(accessRequest);
        System.out.println(str1);
        System.out.println();

        System.out.println("------------------------------------shipConfirm-------------------------------------------");
        ShipmentConfirmRequest shipmentConfirmRequest=new ShipmentConfirmRequest();
        String str2=XMLUtil.beanToXml(shipmentConfirmRequest);
        System.out.println(str2);
        System.out.println();

        System.out.println("------------------------------------shipAccept-------------------------------------------");
        ShipmentAcceptRequest shipmentAcceptRequest=new ShipmentAcceptRequest();
        String str3=XMLUtil.beanToXml(shipmentAcceptRequest);
        System.out.println(str3);
        System.out.println();

        System.out.println("------------------------------------voidShipment-------------------------------------------");
        VoidShipmentRequest voidShipmentRequest=new VoidShipmentRequest();
        String str4=XMLUtil.beanToXml(voidShipmentRequest);
        System.out.println(str4);
        System.out.println();

    }

    @Test
    public void xmlToBean() throws JAXBException {
        String s="<?xml version=\"1.0\"?>\n" +
                "<ShipmentConfirmResponse>\n" +
                "    <Response>\n" +
                "        <TransactionReference>\n" +
                "            <CustomerContext>Customer Comment</CustomerContext>\n" +
                "        </TransactionReference>\n" +
                "        <ResponseStatusCode>1</ResponseStatusCode>\n" +
                "        <ResponseStatusDescription>Success</ResponseStatusDescription>\n" +
                "    </Response>\n" +
                "    <ShipmentCharges>\n" +
                "        <TransportationCharges>\n" +
                "            <CurrencyCode>EUR</CurrencyCode>\n" +
                "            <MonetaryValue>46.87</MonetaryValue>\n" +
                "        </TransportationCharges>\n" +
                "        <ServiceOptionsCharges>\n" +
                "            <CurrencyCode>EUR</CurrencyCode>\n" +
                "            <MonetaryValue>0.00</MonetaryValue>\n" +
                "        </ServiceOptionsCharges>\n" +
                "        <TotalCharges>\n" +
                "            <CurrencyCode>EUR</CurrencyCode>\n" +
                "            <MonetaryValue>46.87</MonetaryValue>\n" +
                "        </TotalCharges>\n" +
                "    </ShipmentCharges>\n" +
                "    <BillingWeight>\n" +
                "        <UnitOfMeasurement>\n" +
                "            <Code>KGS</Code>\n" +
                "        </UnitOfMeasurement>\n" +
                "        <Weight>15.0</Weight>\n" +
                "    </BillingWeight>\n" +
                "    <ShipmentIdentificationNumber>1Z6545E20497279139</ShipmentIdentificationNumber>\n" +
                "    <ShipmentDigest>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</ShipmentDigest>\n" +
                "</ShipmentConfirmResponse>";
        ShipmentConfirmResponse shipmentConfirmResponse=XMLUtil.xmlToBean(s,new ShipmentConfirmResponse());
        System.out.println();
    }
}