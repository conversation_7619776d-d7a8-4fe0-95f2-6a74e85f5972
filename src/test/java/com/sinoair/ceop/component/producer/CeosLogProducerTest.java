package com.sinoair.ceop.component.producer;

import com.sinoair.ceop.domain.dto.sys.AppLogSaveParamDto;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.plugin.apipost.api.tiktok.logistic.TiktokApiUtil;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

/**
 * @Author: 大雄
 * @Date: 2023/9/18 10:06
 * @Description:
 */
public class CeosLogProducerTest extends CommonTestCase {

    @Autowired
    private CeosLogProducer logProducer;

    @Test
    public void testSaveLog() {

        AppLogSaveParamDto paramDto = new AppLogSaveParamDto();
        paramDto.setSysId(TiktokApiUtil.createRequestID());
        paramDto.setAppCode("ceos");
        paramDto.setAppName("ceos");
        paramDto.setBizCode("test-20230918");
        paramDto.setLogStatus("INFO");
        paramDto.setRequest("test abc");
        paramDto.setOperationTime(System.currentTimeMillis());
        logProducer.saveLog(paramDto);
        System.out.println(paramDto.getSysId());
    }
}