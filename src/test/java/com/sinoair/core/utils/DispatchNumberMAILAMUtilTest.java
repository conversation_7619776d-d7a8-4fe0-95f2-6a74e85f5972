package com.sinoair.core.utils;

import org.junit.Test;

import java.util.HashMap;
import java.util.Map;

/**
 * Created by WangXF on 2017/8/22 0022.
 */
public class DispatchNumberMAILAMUtilTest {

    @Test
    public void getDispatchNumber() throws Exception {
        Map<String, String> MailamMap = new HashMap();
        MailamMap.put("to", "Serpost");//大包签上 to
        MailamMap.put("flightNo", "22");//大包签上 Flight
        MailamMap.put("offloadingAirport", "LIM");// 大包签上的 Offloading Airport
        MailamMap.put("RorN", "UN");//R标准；""非标准；大包标签上的右上角下面
        MailamMap.put("destination", "PELIMA");//访问API参数
        MailamMap.put("destination_airport", "LIM");//访问API参数
        MailamMap.put("priority", "true");//访问API参数
        MailamMap.put("registered", "false");//访问API参数
        MailamMap.put("priorityBagLabel", "Priority");//大包签上 Priority
        MailamMap.put("cnxx", "CN35");//大包签上 右上角，可能代表大包类型
        MailamMap.put("loading_airport", "LHR");//访问API参数
        MailamMap.put("service", "Printed Matter");//访问API参数
        String number = DispatchNumberMAILAMUtil.getDispatchNumber(MailamMap);
        System.out.println(number);
    }
}