package com.sinoair;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import com.sinoair.ceop.domain.vo.innerapi.standard.wsget.B2C_CREATE_BAGWSGET;
import com.sinoair.ceop.service.hyNoCJndACT.HyUtil;
import com.sinoair.core.utils.FastJsonUtils;
import com.sinoair.core.utils.FileUtil;
import com.sinoair.core.utils.StringUtil;
import java.util.Map;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.io.File;

//校验通关码
public class TongguanmaTest {

    public static void main(String[] args) {
        String filePath = "D:\\360MoveData\\Users\\JuanFei\\Desktop\\1.txt";
        File file = new File(filePath);
        String fileContent = FileUtil.readFileContent(file);
        List<String> fileContentList = Lists.newArrayList(fileContent.split("\n"));
        List<B2C_CREATE_BAGWSGET> B2C_CREATE_BAGWSGET = new ArrayList<>();
        List<String> packageCodeArray = new ArrayList<>();
        for(String line:fileContentList){
            String[] lineArray = line.split("\t");
            String trackingNumber = lineArray[0];
            String reference2 = lineArray[2];
            String packageCode = lineArray[1];
            String singleWeight = lineArray[3];
            String totalWeight = lineArray[4];
            String totalCount = lineArray[5];
            if(!packageCodeArray.contains(packageCode)){
                packageCodeArray.add(packageCode);
            }
            }
        List<Map> m = new ArrayList<>();
        for(String pkgCode:packageCodeArray){
            Map<String,Object> map = new HashMap<>();
            map.put("pkgCode",pkgCode);
            List<Map<String,String>> maps = new ArrayList<>();
            for(String line:fileContentList){
                String[] lineArray = line.split("\t");
                String trackingNumber = lineArray[0];
                String reference2 = lineArray[2];
                String packageCode = lineArray[1];
                String singleWeight = lineArray[3];
                String totalWeight = lineArray[4];
                String totalCount = lineArray[5];
                if(pkgCode.equalsIgnoreCase(packageCode)){
                    Map<String,String> keyMap = new HashMap<>();
                    keyMap.put("trackingNumber",trackingNumber);
                    keyMap.put("reference2",reference2);
                    maps.add(keyMap);
                    map.put("totalWeight",totalWeight);
                    map.put("totalCount",totalCount);
                }
            }
            map.put("keyMap",maps);
            m.add(map);
        }


        for(int i=0;i<m.size();i++){
            Map<String,Object> map = m.get(i);
            JSONObject jsonObject = new JSONObject();
            JSONObject accessRequest = new JSONObject();
            JSONObject packageJSON = new JSONObject();
            accessRequest.put("password","HGH1000417636T74");
            accessRequest.put("platform","SINOAIR");
            accessRequest.put("username","HGH1000417636");
            jsonObject.put("accessRequest",accessRequest);

            packageJSON.put("pkgWeight",map.get("totalWeight"));
            packageJSON.put("pkgOrdersAmount",map.get("totalCount"));
            packageJSON.put("pkgCode",map.get("pkgCode"));
            jsonObject.put("package",packageJSON);

            JSONArray orders = new JSONArray();
            List<Map<String,String>> maps = (List<Map<String,String>>)map.get("keyMap");
            for(Map<String,String> map1:maps){
                String trackingNumber = map1.get("trackingNumber");
                String reference2 = map1.get("reference2");
                JSONObject singleOrder = new JSONObject();
                singleOrder.put("trackingno",trackingNumber);
                singleOrder.put("orderno",reference2);
                orders.add(singleOrder);
        }
            jsonObject.put("orders",orders);
            System.out.println(FastJsonUtils.toJSONString(jsonObject));
        }

    }

}
