# 大包扣货查询功能使用说明

## 1. 页面概述
大包扣货查询页面用于查询和管理大包扣货的相关信息，包括提单号、大箱号、包裹号/运单号等信息。

## 2. 功能说明

### 2.1 查询功能
在搜索表单中，用户可以填写以下字段进行查询：
- **提单号**：输入提单号进行查询。
- **大箱号**：输入大箱号进行查询。
- **包裹号/运单号**：输入包裹号或运单号进行查询。
- **包裹类型**：选择包裹类型进行过滤。
- **创建时间**：选择创建时间范围进行查询，支持清空已选时间。

点击“查询”按钮即可根据输入的条件进行筛选。

### 2.2 数据展示
查询结果会以表格的形式展示，包含以下字段：
- **序号**：记录的序号。
- **提单号**：显示提单号。
- **大箱号**：显示大箱号。
- **包裹号**：显示包裹号。
- **运单号**：显示运单号。
- **散件SKC号**：显示散件的SKC号。
- **散件商品数量**：显示散件商品的数量。
- **包裹类型**：显示包裹的类型（整包、散件、待处理）。
- **创建人**：显示该记录的创建人。
- **创建时间**：显示记录的创建时间。
- **操作**：提供图片预览和回填操作。

### 2.3 补录信息功能
点击“信息补录”按钮，弹出补录信息模态框，用户可以填写以下信息：
- **包裹类型**：选择包裹类型（包裹/运单、散件）。
- **包裹号**：输入包裹号（必填）。
- **散件SKC号**：输入散件的SKC号。
- **数量**：输入散件商品的数量。
- **提单号**：输入提单号。
- **大箱号**：输入大箱号。
- **运单号**：输入运单号。

填写完成后，点击“提交”按钮保存信息。

### 2.4 编辑补录信息功能
在表格中点击“回填”按钮，弹出编辑补录信息模态框，用户可以修改以下信息：
- **包裹类型**：选择包裹类型。
- **包裹号**：输入包裹号（必填）。
- **散件SKC号**：输入散件的SKC号。
- **数量**：输入散件商品的数量。
- **提单号**：输入提单号。
- **大箱号**：输入大箱号。
- **运单号**：输入运单号。

修改完成后，点击“提交”按钮保存更改。

### 2.5 导出数据
点击“导出”按钮，系统会根据当前查询条件导出匹配的数据。如果未输入任何查询条件，系统会提示用户输入导出条件。

## 3. 注意事项
- 在补录信息时，包裹号为必填项，其他字段根据包裹类型的不同可能为必填。
- 在编辑补录信息时，系统会自动填充原有数据。
- 如果需要导出数据，请确保已经填写了至少一个查询条件。
- 时间选择控件支持清空已选时间。

## 4. 常见问题
- **Q**: 提交补录信息时出现错误提示？
  **A**: 请检查是否填写了所有必填字段。
- **Q**: 如何查看图片预览？
  **A**: 在编辑补录信息模态框中，系统会自动加载图片预览。
- **Q**: 导出数据失败？
  **A**: 请检查是否填写了至少一个查询条件。

## 5. 版本历史
- **v1.0**：初始版本，包含基本的查询、补录、编辑补录和导出功能。
- **v1.1**：新增运单号字段，并优化包裹号和运单号的显示。
- **v1.2**：补充补录和编辑补录功能中的包裹号必填逻辑说明。
- **v1.3**：修改时间选择控件，支持清空已选时间。

## 6. 联系方式
如有任何问题，请联系开发团队。

---

*文档更新日期: 2025-04-05*