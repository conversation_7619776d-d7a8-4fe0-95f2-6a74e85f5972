-- Create table 菜单列表
create table DB_MENU
(
  ID          NUMBER(9) not null,
  SORT_NUM    NUMBER(9),
  PARENT_ID   NUMBER(9) not null,
  NAME_ZH     VARCHAR2(64),
  NAME_EN     VARCHAR2(64),
  URL         VARCHAR2(255),
  DESCRIPTION VARCHAR2(255),
  IMAGE       VARCHAR2(32),
  STATUS      VARCHAR2(4) default 'Y' not null
)
tablespace USERS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the columns
comment on column DB_MENU.ID
  is '主键';
comment on column DB_MENU.SORT_NUM
  is '同级菜单排序依据';
comment on column DB_MENU.PARENT_ID
  is '父级id，顶级为0';
comment on column DB_MENU.NAME_ZH
  is '菜单中文名称';
comment on column DB_MENU.NAME_EN
  is '菜单英文名称';
comment on column DB_MENU.URL
  is 'url地址';
comment on column DB_MENU.DESCRIPTION
  is '描述';
comment on column DB_MENU.IMAGE
  is '图片名称';
comment on column DB_MENU.STATUS
  is '是否有效 ： Y 有效   N 无效';
-- Create/Recreate primary, unique and foreign key constraints
alter table DB_MENU
  add constraint PK_MENU primary key (ID)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );


-- Create table 权限表
create table DB_ROLE
(
  ID           NUMBER(9) not null,
  CODE         VARCHAR2(32),
  NAME         VARCHAR2(32) not null,
  CREATE_TIME  DATE,
  EDIT_TIME    DATE,
  EDIT_USER_ID NUMBER(9) not null,
  DESCRIPTION  VARCHAR2(255)
)
tablespace USERS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the columns
comment on column DB_ROLE.ID
  is '主键';
comment on column DB_ROLE.CODE
  is '编号';
comment on column DB_ROLE.NAME
  is '权限名称';
comment on column DB_ROLE.CREATE_TIME
  is '创建时间';
comment on column DB_ROLE.EDIT_TIME
  is '最后编辑时间';
comment on column DB_ROLE.EDIT_USER_ID
  is '最后编辑人id  指向 db_user主键';
comment on column DB_ROLE.DESCRIPTION
  is '描述';
-- Create/Recreate primary, unique and foreign key constraints
alter table DB_ROLE
  add constraint PK_ROLE primary key (ID)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
alter table DB_ROLE
  add constraint UN_CODE unique (CODE)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );


-- Create table 角色菜单中间表
create table DB_ROLE_MENU
(
  ROLE_ID NUMBER(9) not null,
  MENU_ID NUMBER(9) not null
)
tablespace USERS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the columns
comment on column DB_ROLE_MENU.ROLE_ID
  is '权限id    role';
comment on column DB_ROLE_MENU.MENU_ID
  is '菜单id    menu (仅主页)';
-- Create/Recreate primary, unique and foreign key constraints
alter table DB_ROLE_MENU
  add constraint PK_ROLE_MENU primary key (ROLE_ID, MENU_ID)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );


-- Create table 用户表
create table DB_USER
(
  ID              NUMBER(9) not null,
  USERNAME        VARCHAR2(255) not null,
  PASSWORD        VARCHAR2(255) not null,
  REALNAME        VARCHAR2(255),
  SEX             NUMBER(1),
  BIRTHDAY        DATE,
  DESCRIPTION     VARCHAR2(255),
  STATUS          VARCHAR2(4) default 'Y' not null,
  CREATE_TIME     DATE,
  LAST_LOGIN_TIME DATE
)
tablespace USERS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the columns
comment on column DB_USER.ID
  is '用户主键';
comment on column DB_USER.USERNAME
  is '用户名';
comment on column DB_USER.PASSWORD
  is '密码';
comment on column DB_USER.REALNAME
  is '真实姓名';
comment on column DB_USER.SEX
  is '性别： 0：男  1：女';
comment on column DB_USER.BIRTHDAY
  is '出生日期';
comment on column DB_USER.DESCRIPTION
  is '描述';
comment on column DB_USER.STATUS
  is '是否有效：Y:是 N:否';
comment on column DB_USER.CREATE_TIME
  is '创建时间';
comment on column DB_USER.LAST_LOGIN_TIME
  is '最后登录时间';
-- Create/Recreate primary, unique and foreign key constraints
alter table DB_USER
  add constraint PK_USER primary key (ID)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
alter table DB_USER
  add constraint UN_USERNAME unique (USERNAME)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );


-- Create table 用户角色中间表
create table DB_USER_ROLE
(
  USER_ID NUMBER(9) not null,
  ROLE_ID NUMBER(9) not null
)
tablespace USERS
  pctfree 10
  initrans 1
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );
-- Add comments to the columns
comment on column DB_USER_ROLE.USER_ID
  is '用户id    user';
comment on column DB_USER_ROLE.ROLE_ID
  is '权限id    rold';
-- Create/Recreate primary, unique and foreign key constraints
alter table DB_USER_ROLE
  add constraint PK_USER_ROLD primary key (USER_ID, ROLE_ID)
  using index
  tablespace USERS
  pctfree 10
  initrans 2
  maxtrans 255
  storage
  (
    initial 64K
    next 1M
    minextents 1
    maxextents unlimited
  );


-- Create sequence  菜单序列
create sequence SEQ_MENU
minvalue 1
maxvalue 9999999999999999999999999999
start with 114
increment by 1
cache 10;

-- Create sequence  角色序列
create sequence SEQ_ROLE
minvalue 1
maxvalue 9999999999999999999999999999
start with 2
increment by 1
cache 10;

-- Create sequence  用户序列
create sequence SEQ_USER
minvalue 1
maxvalue 9999999999999999999999999999
start with 2
increment by 1
nocache;


insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (4, 0, 3, '菜单管理', 'menu', 'system/menu', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (5, 1, 3, '角色管理', 'role', 'system/role', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (13, 4, 8, 'Sparkline', 'Sparkline', '/tools/graph_sparkline', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (15, 4, 0, '信箱', 'mail', null, null, 'fa fa-envelope', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (16, 0, 15, '收件箱', 'mailbox', '/tools/mailbox', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (37, 6, 0, '表单', 'form', '#', null, 'fa fa-edit', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (38, 0, 37, '基本表单', 'form_basic', '/tools/form_basic', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (39, 1, 37, '表单验证', 'form_validate', '/tools/form_validate', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (40, 2, 37, '高级插件', 'form_advanced', '/tools/form_advanced', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (41, 3, 37, '表单向导', 'form_wizard', '/tools/form_wizard', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (42, 4, 37, '百度WebUploader', 'form_webuploader', '/tools/form_webuploader', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (43, 5, 37, 'DropzoneJS', 'form_file_upload', '/tools/form_file_upload', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (44, 6, 37, '头像裁剪上传', 'form_avatar', '/tools/form_avatar', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (45, 7, 37, '富文本编辑器', 'form_editors', '/tools/form_editors', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (46, 8, 37, 'simditor', 'form_simditor', '/tools/form_simditor', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (47, 9, 37, 'MarkDown编辑器', 'form_markdown', '/tools/form_markdown', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (48, 10, 37, '代码编辑器', 'code_editor', '/tools/code_editor', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (49, 11, 37, '搜索自动补全', 'suggest', '/tools/suggest', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (50, 12, 37, '日期选择器layerDate', 'layerdate', '/tools/layerdate', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (3, 1, 0, '设置', 'settings', '#', null, 'fa fa-wrench', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (6, 2, 0, '工具', 'tools', '#', null, 'fa fa-cutlery', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (7, 0, 6, '表单构建器', 'form_builder', '/tools/form_builder', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (8, 3, 0, '统计图表', 'statisticsIcon', '#', null, 'fa fa fa-bar-chart-o', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (9, 0, 8, '百度ECharts', 'echarts', '/tools/echarts', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (11, 6, 8, 'Rickshaw', 'Rickshaw', '/tools/Rickshaw', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (12, 3, 8, 'Peity', 'Peity', '/tools/graph_peity', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (14, 5, 8, '图表组合', 'graph_metrics', '/tools/graph_metrics', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (17, 1, 15, '查看邮件', 'mail_detail', '/tools/mail_detail', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (18, 2, 15, '写信', 'mail_compose', '/tools/mail_compose', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (51, 1, 6, 'CSS动画', 'css_animation', '/tools/css_animation', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (52, 7, 0, 'UI元素', 'ui', '#', null, 'fa fa-flask', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (53, 8, 0, '页面', 'page', '#', null, 'fa fa-desktop', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (54, 9, 0, '表格', 'table', '#', null, 'fa fa-table', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (55, 10, 0, '相册', 'picture', '#', null, 'fa fa-picture-o', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (56, 0, 52, '排版', 'typography', '/tools/typography', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (57, 1, 52, 'Font Awesome', 'fontawesome', '/tools/fontawesome', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (58, 2, 52, 'Glyphicon', 'glyphicons', '/tools/glyphicons', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (59, 3, 52, '阿里巴巴矢量图标库', 'iconfont', '/tools/iconfont', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (60, 4, 52, '拖动面板', 'draggable_panels', '/tools/draggable_panels', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (61, 5, 52, '任务清单', 'agile_board', '/tools/agile_board', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (62, 6, 52, '按钮', 'buttons', '/tools/buttons', null, 'buttons', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (63, 7, 52, '选项卡 & 面板', 'tabs_panels', '/tools/tabs_panels', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (64, 8, 52, '通知 & 提示', 'notifications', '/tools/notifications', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (65, 9, 52, '徽章，标签，进度条', 'badges_labels', '/tools/badges_labels', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (66, 10, 52, '栅格', 'grid_options', '/tools/grid_options', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (67, 11, 52, '视频、音频', 'plyr', '/tools/plyr', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (68, 12, 52, 'Web弹层组件layer', 'layer', '/tools/layer', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (69, 13, 52, '模态窗口', 'modal_window', '/tools/modal_window', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (70, 14, 52, 'SweetAlert', 'sweetalert', '/tools/sweetalert', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (71, 15, 52, 'jsTree', 'jstree', '/tools/jstree', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (72, 16, 52, 'Bootstrap Tree View', 'tree_view', '/tools/tree_view', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (73, 17, 52, 'nestable', 'nestable_list', '/tools/nestable_list', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (74, 18, 52, 'Toastr通知', 'toastr_notifications', '/tools/toastr_notifications', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (75, 19, 52, '文本对比', 'diff', '/tools/diff', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (76, 20, 52, '加载动画', 'spinners', '/tools/spinners', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (77, 21, 52, '小部件', 'widgets', '/tools/widgets', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (1, 0, 0, '主页', 'index', '#', null, 'fa fa-home', 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (10, 1, 8, 'Flot', 'flot', '/tools/graph_flot', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (27, 3, 3, '用户管理', 'user', '/system/userList', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (2, 0, 1, '首页', 'home', 'home', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (19, 2, 3, '权限分配', 'auth', '/system/authInit', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (78, 0, 54, '基本表格', 'table_basic', '/tools/table_basic', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (79, 1, 54, 'DataTables', 'table_data_tables', '/tools/table_data_tables', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (81, 3, 54, 'Bootstrap Table', 'table_bootstrap', '/tools/table_bootstrap', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (82, 2, 54, 'Foo Tables', 'table_foo_table', '/tools/table_foo_table', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (83, 0, 55, '基本图库', 'basic_gallery', '/tools/basic_gallery', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (84, 1, 55, '图片切换', 'carousel', '/tools/carousel', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (85, 2, 55, 'Blueimp相册', 'blueimp', '/tools/blueimp', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (86, 0, 53, '联系人', 'contacts', '/tools/contacts', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (87, 1, 53, '个人资料', 'profile', '/tools/profile', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (88, 2, 53, '项目', 'projects', '/tools/projects', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (89, 3, 53, '项目详情', 'project_detail', '/tools/project_detail', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (90, 4, 53, '团队管理', 'teams_board', '/tools/teams_board', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (91, 5, 53, '信息流', 'social_feed', '/tools/social_feed', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (92, 6, 53, '客户管理', 'clients', '/tools/clients', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (93, 7, 53, '文件管理器', 'file_manager', '/tools/file_manager', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (94, 8, 53, '日历', 'calendar', '/tools/calendar', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (95, 9, 53, '文章列表', 'blog', '/tools/blog', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (96, 10, 53, '文章详情', 'article', '/tools/article', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (97, 11, 53, 'FAQ', 'faq', '/tools/faq', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (98, 12, 53, '时间轴', 'timeline', '/tools/timeline', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (99, 13, 53, '时间轴v2', 'timeline_v2', '/tools/timeline_v2', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (100, 14, 53, '标签墙', 'pin_board', '/tools/pin_board', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (101, 15, 53, '单据', 'invoice', '/tools/invoice', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (102, 16, 53, '单据打印', 'invoice_print', '/tools/invoice_print', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (103, 17, 53, '搜索结果', 'search_results', '/tools/search_results', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (104, 18, 53, '论坛', 'forum_main', '/tools/forum_main', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (105, 19, 53, '聊天窗口', 'chat_view', '/tools/chat_view', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (106, 20, 53, 'layIM', 'webim', '/tools/webim', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (107, 21, 53, '登录页面', 'login_v1', '/tools/login', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (108, 22, 53, '登录页面v2', 'login_v2', '/tools/login_v2', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (109, 23, 53, '注册页面', 'register_page', '/tools/register', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (110, 24, 53, '登录超时', 'lockscreen', '/tools/lockscreen', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (111, 27, 53, '404页面', '404_Page', '/tools/404', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (112, 25, 53, '500页面', '500_Page', '/tools/500', null, null, 'Y');
insert into DB_MENU (ID, SORT_NUM, PARENT_ID, NAME_ZH, NAME_EN, URL, DESCRIPTION, IMAGE, STATUS)
values (113, 26, 53, '空白页', 'empty_page', '/tools/empty_page', null, null, 'Y');

insert into DB_ROLE (ID, CODE, NAME, CREATE_TIME, EDIT_TIME, EDIT_USER_ID, DESCRIPTION)
values (1, 'admin', '管理员', to_date('09-02-2016', 'dd-mm-yyyy'), to_date('09-02-2016', 'dd-mm-yyyy'), 1, null);

insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 1);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 2);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 3);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 4);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 5);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 6);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 7);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 8);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 9);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 10);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 11);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 12);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 13);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 14);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 15);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 16);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 17);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 18);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 19);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 27);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 37);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 38);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 39);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 40);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 41);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 42);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 43);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 44);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 45);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 46);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 47);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 48);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 49);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 50);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 51);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 52);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 53);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 54);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 55);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 56);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 57);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 58);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 59);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 60);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 61);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 62);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 63);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 64);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 65);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 66);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 67);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 68);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 69);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 70);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 71);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 72);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 73);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 74);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 75);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 76);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 77);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 78);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 79);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 81);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 82);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 83);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 84);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 85);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 86);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 87);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 88);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 89);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 90);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 91);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 92);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 93);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 94);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 95);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 96);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 97);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 98);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 99);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 100);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 101);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 102);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 103);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 104);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 105);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 106);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 107);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 108);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 109);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 110);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 111);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 112);
insert into DB_ROLE_MENU (ROLE_ID, MENU_ID)
values (1, 113);


insert into DB_USER (ID, USERNAME, PASSWORD, REALNAME, SEX, BIRTHDAY, DESCRIPTION, STATUS, CREATE_TIME, LAST_LOGIN_TIME)
values (1, 'admin', 'aba5fddbeae6dbaea906f592a9ee270f', '大雄', 0, to_date('26-02-1992', 'dd-mm-yyyy'), '系统超级管理员', 'Y', to_date('08-02-2016', 'dd-mm-yyyy'), to_date('08-02-2016', 'dd-mm-yyyy'));

insert into DB_USER_ROLE (USER_ID, ROLE_ID)
values (1, 1);
