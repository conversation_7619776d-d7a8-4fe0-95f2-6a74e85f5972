package com.sinoair.core.utils;

import org.junit.Before;
import org.junit.Test;

public class HttpRequestUtilTest {
    public HttpRequestUtil httpRequestUtil;

    @Before
    public void init() {
        httpRequestUtil = new HttpRequestUtil();
    }

    @Test
    public void sendPost() throws Exception {
        String url = "http://139.162.37.173:10003/index.php/I/Warehouse/pickup";
        String param = "sign=13003ae0b77dad0ba0f3618ce7384eae16d87195707ac9db849979d22b3dd5d6&type=json&data=";
        int timeout = 50000;
        int readTimeout = 50000;

        String result = httpRequestUtil.sendPost(url, param, timeout, readTimeout);
        System.out.println(result);
    }
}