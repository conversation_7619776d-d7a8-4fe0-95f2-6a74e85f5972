package com.sinoair.ceop.service;

import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.service.transmode.TransMode;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.DateUtil;
import com.sinoair.core.utils.StringUtil;
import com.sinoair.core.utils.XMLUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

import static java.lang.System.out;

/**
 * Created by WangXX4 on 2016/6/14.
 */
public class CainiaoServiceTest extends CommonTestCase {
    @Resource
    CainiaoService cainiaoService;
    @Resource
    EawbpreMapper eawbpreMapper;

    String resultExpected = "<responses>\n" +
            "\t<responseItems>\n" +
            "\t\t<response>\n" +
            "\t\t\t<success>true</success>\n" +
            "\t\t</response>\n" +
            "\t</responseItems>\n" +
            "</responses>";

    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void report() throws Exception {
        String xml = "";
        xml = "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2015-05-19 19:09:33</eventTime><eventSource>taobao</eventSource><eventTarget>DISTRIBUTOR_903172</eventTarget></eventHeader><eventBody><OrderInfos><product><productNameCN></productNameCN><productNameEN>furniture</productNameEN><productQantity>1</productQantity><productCateCN></productCateCN><productCateEN>furniture</productCateEN><productId /><producingArea /><productWeight>1900</productWeight><productPrice>2000</productPrice></product></OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>35917668244</logisticsOrderId><tradeId>3018196085</tradeId>" +
                "<mailNo>LPWXX2016-9-20-006</mailNo><Rcountry>ES</Rcountry><Rprovince>Barcelona</Rprovince><Remail><EMAIL></Remail><Raddress>Avenida Paralelo 15, 2</Raddress><Rpostcode>08004</Rpostcode><Rname>Marco a Garcia</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId><Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode><channel>HK</channel><Itotleweight>2168</Itotleweight><Itotlevalue>2000</Itotlevalue><totleweight>2168</totleweight><country>CN</country><mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress></pickUpAddress><packageCode>YWES20150724001</packageCode><orderCode>LPwxx0623001</orderCode><packageWeight>1232</packageWeight><ordersInPackage>1</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>\n";

        String resultActual = cainiaoService.reportTransaction(xml);
        out.println("resultActual = " + resultActual);
        assertEquals(resultExpected, resultActual);
    }

    /**
     * 智利邮政
     * 对邮编有特殊处理
     *
     * @throws Exception
     */
    @Test
    public void reportChile() throws Exception {
        String eawb_reference1 = "WXX000000000" + new Date().getTime();
        System.out.println("eawb_reference1 = " + eawb_reference1);
        String xml = createXml(eawb_reference1, TransMode.SinotransCLNonTrack, "ES", "000000");
        String resultActual = cainiaoService.reportTransaction(xml);
        Eawbpre pre = new Eawbpre();
        pre.setEawbReference1(eawb_reference1);
        final List<Eawbpre> eawbpres = eawbpreMapper.selectEawbPreDetailList(pre);
        assertEquals(Expressairwaybill.NOTALLOWED, (eawbpres.get(0).getEawbChangeLabelStatus()));
        out.println("resultActual = " + resultActual);
        assertEquals(resultExpected, resultActual);
    }

    private String createXml(String eawb_reference1, String serviceType, String destCountry, String destPostCode) {
        return "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2015-05-19 19:09:33</eventTime><eventSource>taobao</eventSource>" +
                "<eventTarget>" + serviceType + "</eventTarget></eventHeader><eventBody><OrderInfos><product><productNameCN></productNameCN><productNameEN>furniture</productNameEN><productQantity>1</productQantity><productCateCN></productCateCN><productCateEN>furniture</productCateEN><productId /><producingArea /><productWeight>1900</productWeight><productPrice>2000</productPrice></product></OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>35917668244</logisticsOrderId><tradeId>3018196085</tradeId>" +
                "<mailNo>" + eawb_reference1 + "</mailNo>" +
                "<Rcountry>" + destCountry + "</Rcountry><Rprovince>Barcelona</Rprovince><Rcity>Alcala de Henares</Rcity><Remail><EMAIL></Remail><Raddress>Avenida Paralelo 15, 2</Raddress>" +
                "<Rpostcode>" + destPostCode + "</Rpostcode>" +
                "<Rname>Marco a Garcia</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId><Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode><channel>HK</channel><Itotleweight>2168</Itotleweight><Itotlevalue>2000</Itotlevalue><totleweight>2168</totleweight><country>CN</country><mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress></pickUpAddress><packageCode>YWES20150724001</packageCode><orderCode>LPwxx0623001</orderCode><packageWeight>1232</packageWeight><ordersInPackage>1</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>\n";
    }

    /**
     * 英国邮政
     * 对目的国家二字码
     *
     * @throws Exception
     */
    @Test
    public void reportUKGB_Roymal() throws Exception {
        String eawb_reference1 = "WXX" + new Date().getTime();
        System.out.println("eawb_reference1 = " + eawb_reference1);
        String xml = createXml(eawb_reference1, TransMode.SinotransRoyalMailTracked48, "UK", "000000");
        String resultActual = cainiaoService.reportTransaction(xml);
        Eawbpre pre = new Eawbpre();
        pre.setEawbReference1(eawb_reference1);
        List<Eawbpre> eawbpres = eawbpreMapper.selectEawbPreDetailList(pre);
        assertEquals("GB", (eawbpres.get(0)).getEawbDestcountry());
        out.println("resultActual = " + resultActual);
        assertEquals(resultExpected, resultActual);
    }


    //DISTRIBUTOR_1499269 中外运-英邮标准小包
    /*String pkg_No="CNGB20160709TPL20001";
    int ordersInPackage=15;//大包内运单数
    String postcode="TW14 0NG";// 邮编
    String city="auto city";//目的城市
    String mailNo="JJ113701296GB";
    String servicetype="DISTRIBUTOR_1499269";*/


     /*String pkg_No="CNGB20160709TPL20001";
     int ordersInPackage=15;//大包内运单数
    String postcode="88888888";// 邮编
    String city="auto city";//目的城市
    String mailNo="JJ113701282GB";
    String servicetype="DISTRIBUTOR_1499269";*/



   /* String pkg_No="CNGB20160709TPL20001";
    int ordersInPackage=15;//大包内运单数
    String postcode="TW14 0NG";// 邮编
    String city="auto city";//目的城市
    String mailNo="JJ113701296GB";
    String servicetype="DISTRIBUTOR_1499269";*/

//--------------------------------------------------------------------------------------------------------------------
    //DISTRIBUTOR_1682692 中外运英邮-经济小包

/*    String pkg_No="YWGB20160709CRL3001";
    int ordersInPackage=15;//大包内运单数
    String postcode="B28 9QP";// 邮编
    String city="Hall Green,Birmingham";//目的城市
    String mailNo="LP00042546970186";
    String servicetype="DISTRIBUTOR_1682692";*/

/*
    String pkg_No="YWGB20160709CRL3001";
    int ordersInPackage=15;//大包内运单数
    String postcode="bt221ej";// 邮编
    String city="newtownards";//目的城市
    String mailNo="LP00039503168331";
    String servicetype="DISTRIBUTOR_1682692";*/


    /*String pkg_No="YWGB20160709CRL3001";
    int ordersInPackage=15;//大包内运单数
    String postcode="E16 4HQ";// 邮编
    String city="london";//目的城市
    String mailNo="LP00042789168963";
    String servicetype="DISTRIBUTOR_1682692";*/

//--------------------------------------------------------------------------------------------------------------------
    //DISTRIBUTOR_11358681 中外运-英邮泛欧标准小包

    //意大利
    String pkg_No = "CNUK20160709RM10000";
    int ordersInPackage = 15;//大包内运单数
    String postcode = "";// 邮编
    String city = "Italy";//目的城市
    String mailNo = "RS595937001GB";
    String servicetype = "DISTRIBUTOR_11358681";

   /* String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="Italy";//目的城市
    String mailNo="RS595937280GB";
    String servicetype="DISTRIBUTOR_11358681";*/

    /*String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="Italy";//目的城市
    String mailNo="RS595937302GB";
    String servicetype="DISTRIBUTOR_11358681";*/


    //法国
 /*   String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="France";//目的城市
    String mailNo="RS595937301GB";
    String servicetype="DISTRIBUTOR_11358681";*/

 /*  String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="France";//目的城市
    String mailNo="RS595937125GB";
    String servicetype="DISTRIBUTOR_11358681";*/

   /*String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="France";//目的城市
    String mailNo="RS595937127GB";
    String servicetype="DISTRIBUTOR_11358681";*/

    //拉脱维亚
  /* String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="Latvia";//目的城市
    String mailNo="RS595937128GB";
    String servicetype="DISTRIBUTOR_11358681";
*/
    /*String pkg_No="CNUK20160709RM00009";
    int ordersInPackage=15;//大包内运单数
    String postcode="";// 邮编
    String city="Latvia";//目的城市
    String mailNo="RS595937129GB";
    String servicetype="DISTRIBUTOR_11358681";*/

//--------------------------------------------------------------------------------------------------------------------
    //DISTRIBUTOR_903172 中外运-西邮经济小包
  /* String pkg_No="YWES20150709EE2002";
    int ordersInPackage=15;//大包内运单数
    String postcode="37004";// 邮编
    String city="Salamanca";//目的城市
    String mailNo="LP00033612842010";
    String servicetype="DISTRIBUTOR_903172";*/

    /*String pkg_No="YWES20150709EE2002";
    int ordersInPackage=15;//大包内运单数
    String postcode="45005";// 邮编
    String city="TOLEDO";//目的城市
    String mailNo="LP00033665163858";
    String servicetype="DISTRIBUTOR_903172";*/

    /*String pkg_No="YWES20150709EE2002";
    int ordersInPackage=15;//大包内运单数
    String postcode="06120";// 邮编
    String city="Oliva de la Frontera";//目的城市
    String mailNo="LP00033673334221";
    String servicetype="DISTRIBUTOR_903172";*/


    //--------------------------------------------------------------------------------------------------------------------
    //DISTRIBUTOR_902950  中外运-西邮标准小包
    /* String pkg_No="YWES20150709RR6009";
    int ordersInPackage=15;//大包内运单数
    String postcode="08001";// 邮编
    String city="Barcelona";//目的城市
    String mailNo="PQ48K20400000410108001P";
    String servicetype="DISTRIBUTOR_902950";*/

    /*String pkg_No="YWES20150709RR6009";
    int ordersInPackage=15;//大包内运单数
    String postcode="08430";// 邮编
    String city="Barcelona";//目的城市
    String mailNo="PQ48K20400000270108430H";
    String servicetype="DISTRIBUTOR_902950";*/
/*
    String pkg_No="YWES20150709RR6009";
    int ordersInPackage=15;//大包内运单数
    String postcode="08430";// 邮编
    String city="Barcelona";//目的城市
    String mailNo="PQ48K20400000210108430N";
    String servicetype="DISTRIBUTOR_902950";*/


    //--------------------------------------------------------------------------------------------------------------------
    //DISTRIBUTOR_11131306  中外运-美邮墨西哥标准小包
  /* String pkg_No="BAG160519161955298";
    int ordersInPackage=15;//大包内运单数
    String postcode="GL43SJ";// 邮编
    String city="Gloucester";//目的城市
    String mailNo="218100007667";
    String servicetype="DISTRIBUTOR_11131306";*/


  /*  String pkg_No="BAG160519161955298";
    int ordersInPackage=15;//大包内运单数
    String postcode="cr41ef";// 邮编
    String city="surrey";//目的城市
    String mailNo="218100003521";
    String servicetype="DISTRIBUTOR_11131306";*/

  /*  String pkg_No="USUAKAMXMEXDAUZ64014001010175";
    int ordersInPackage=15;//大包内运单数
    String postcode="4014";// 邮编
    String city="surrey";//目的城市
    String mailNo="201607113521";
    String servicetype="DISTRIBUTOR_11131306";*/
    //--------------------------------------------------------------------------------------------------------------------
    //DISTRIBUTOR_11131307  中外运-美邮哥伦比亚标准小包
  /*  String pkg_No="SNMA20160427RM00002";
    int ordersInPackage=15;//大包内运单数
    String postcode="70976";// 邮编
    String city="Medellin";//目的城市
    String mailNo="RT185635778HK";
    String servicetype="DISTRIBUTOR_11131307";*/

   /* String pkg_No = "CNUAKAMXMEXDAUZ65015001010175";
    int ordersInPackage = 15;//大包内运单数
    String postcode = "5015";// 邮编
    String city = "surrey";//目的城市
    String mailNo = "201607115015";
    String servicetype = "DISTRIBUTOR_11131307";*/


    String xml = "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2012-02-25 00:00:00</eventTime>\" +\n" +
            "            \"<eventSource>taobao</eventSource>\" +\n" +
            "            \"<eventTarget>" + servicetype + "</eventTarget>\" +\n" +
            "            \"</eventHeader><eventBody><OrderInfos><product>\" +\n" +
            "            \"<productNameCN/>\" +\n" +
            "            \"<productNameEN>Test scarf2</productNameEN>\" +\n" +
            "            \"<productQantity>1</productQantity>\" +\n" +
            "            \"<productCateCN/>\" +\n" +
            "            \"<productCateEN>Test scarf4</productCateEN>\" +\n" +
            "            \"<productId /><producingArea />\" +\n" +
            "            \"<productWeight>500</productWeight>\" +\n" +
            "            \"<productPrice>23.7</productPrice>\" +\n" +
            "            \"</product>\" +\n" +
            "            \"</OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>MAM0001</logisticsOrderId><tradeId>3018196085</tradeId>\" +\n" +
            "            \"<mailNo>" + mailNo + "</mailNo>\" +\n" +
            "            \"<Rcountry>CL</Rcountry>\" +\n" +
            "            \"<Rprovince>Barcelona</Rprovince><Rcity>" + city + "</Rcity><Remail><EMAIL></Remail><Raddress>Avda. Jose Pedro Alessandri 605 Depto. 801, Nunoa</Raddress>\" +\n" +
            "            \"<Rpostcode>" + postcode + "</Rpostcode><Rname>Sandra Zambrno</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId>\" +\n" +
            "            \"<Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode>\" +\n" +
            "            \"<channel>HK</channel><Itotleweight>2168</Itotleweight><Itotlevalue>2000</Itotlevalue><totleweight>2168</totleweight>\" +\n" +
            "            \"<country>CN</country>\" +\n" +
            "            \"<mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress/>\" +\n" +
            "            \"<packageCode>" + pkg_No + "</packageCode>\" +\n" +
            "            \"<orderCode>" + mailNo + "</orderCode>\" +\n" +
            "            \"<packageWeight>1232</packageWeight>\" +\n" +
            "            \"<ordersInPackage>" + ordersInPackage + "</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>";


    @Test
    public void batch() throws Exception {
        String resultExpected = "<responses>\n" +
                "\t<responseItems>\n" +
                "\t\t<response>\n" +
                "\t\t\t<success>true</success>\n" +
                "\t\t</response>\n" +
                "\t</responseItems>\n" +
                "</responses>";
 /*       String xml = "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2015-05-19 19:09:33</eventTime><eventSource>taobao</eventSource>" +
                "<eventTarget>DISTRIBUTOR_903172</eventTarget></eventHeader><eventBody><OrderInfos><product><productNameCN></productNameCN><productNameEN>furniture</productNameEN><productQantity>1</productQantity><productCateCN></productCateCN><productCateEN>furniture</productCateEN><productId /><producingArea /><productWeight>1900</productWeight><productPrice>2000</productPrice></product></OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>35917668244</logisticsOrderId><tradeId>3018196085</tradeId>" +
                "<mailNo>LPWXX08111110002</mailNo><Rcountry>ES</Rcountry><Rprovince>Barcelona</Rprovince><Rcity>Alcala de Henares</Rcity><Remail><EMAIL></Remail><Raddress>Avenida Paralelo 15, 2</Raddress><Rpostcode>08004</Rpostcode><Rname>Marco a Garcia</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId><Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode><channel>HK</channel><Itotleweight>2168</Itotleweight><Itotlevalue>2000</Itotlevalue><totleweight>2168</totleweight><country>CN</country><mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress></pickUpAddress><packageCode>YWES20150724001</packageCode><orderCode>LPwxx0623001</orderCode><packageWeight>1232</packageWeight><ordersInPackage>1</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>\n";
;*/


        String resultActual = cainiaoService.batchTransaction(xml);
        out.println("resultActual = " + resultActual);
        assertEquals(resultExpected, resultActual);
    }


    /*	CNGB204120413625620002 中外运-英邮标准小包
        YWED204120413625620004 中外运英邮-经济小包
        YWES204120413625620002 中外运-西邮经济小包
        YWED204120413625620002 中外运-西邮标准小包
        CNUK204120413625620002 中外运-英邮泛欧标准小包
    */
    @Test
    public void reportUKGB_CORREOS() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = "2017010630778771";//日期唯一号
        String dectCountry = "Salamanca";//目的国家
        String serviceType = "DISTRIBUTOR_903172";//线路
        int ordersInPackage = 6;//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "LPU" + date + "XXX";//参考号1 mailno
        String pkg_no = "YWED" + date + "019";//大包号
        String postcode = "08945";//邮编
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int i = 0; i < ordersInPackage; i++) {
            String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, i);
            String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, i);
            cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                    eawb_reference1,
                    postcode,
                    dectCountry, dectCountry,
                    cainiaoLPNO1, pkg_no, ordersInPackage));
        }

    }

    //---------------------------------------------西邮标准小包 ---------------------------------------------------------------
    @Test
    public void batchUKGBXB() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String postcode = "08430";// 邮编
        String date = "20170104";//日期唯一号
        String dectCountry = "ES";//目的国家
        String serviceType = "DISTRIBUTOR_902950";//线路
        int[] ordersInPackageArray = {7, 8, 9, 10};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "LP" + date;//参考号1 mailno  RB780569565CO
        String base_pkg_no = "YWES" + date + "EE" + dectCountry;//大包号  YWES20150709EE2002
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    //---------------------------------------------西邮经济小包 ---------------------------------------------------------------
    @Test
    public void batchUKGBXJ() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String postcode = "37004";// 邮编
        String date = "20160805";//日期唯一号
        String dectCountry = "ES";//目的国家
        String serviceType = "DISTRIBUTOR_903172";//线路
        int[] ordersInPackageArray = {4, 5, 6, 7};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "LP" + date + dectCountry;//参考号1 mailno  RB780569565CO
        String base_pkg_no = "YWES" + date + dectCountry + "EE";//大包号  YWES20150709EE2002
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    //--------------------------------------------英邮经济小包-----------------------------------------------------------------
    @Test
    public void batchUKGBYJ() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String postcode = "B28 9QP";// 邮编
        String date = "20160805";//日期唯一号
        String dectCountry = "GB";//目的国家
        String serviceType = "DISTRIBUTOR_1682692";//线路
        int[] ordersInPackageArray = {7, 8, 9, 10};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "LP" + date;//参考号1 mailno  RB780569565CO
        String base_pkg_no = "YWGB" + date + dectCountry + "CRL";//大包号  YWGB20160709CRL3001
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    //---------------------------------------------英邮标准小包-----------------------------------------------------------------
    @Test
    public void batchUKGBYB() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String postcode = "TW14 0NG";// 邮编
        String date = "20160805";//日期唯一号
        String dectCountry = "GB";//目的国家
        String serviceType = "DISTRIBUTOR_1499269";//线路
        int[] ordersInPackageArray = {7, 8, 9, 10};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "JJ" + date + "1";//参考号1 mailno  RB780569565CO
        String base_pkg_no = "CNGB" + date + dectCountry + "TPL2";//大包号  CNGB20160709TPL20001
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }
//  ------------------------------------------英邮泛欧----------------------------------------------------------------

    /**
     * 英邮泛欧(Italy)
     *
     * @throws Exception
     */
    @Test
    public void batchIT() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String postcode = "";// 邮编
        String date = "20170822";//日期唯一号
        String dectCountry = "IT";//目的国家
        String serviceType = "DISTRIBUTOR_11358681";//线路
        int[] ordersInPackageArray = {20};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "RS" + date + "1";//参考号1 mailno  RB780569565CO
        String base_pkg_no = "CNUK" + date + dectCountry + "1";//大包号  CNUK20160709RM10000
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "GB";
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    /**
     * 英邮泛欧(France)
     *
     * @throws Exception
     */
    @Test
    public void batchFC() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String pkg_No = "CNUK20160709RM00009";
        String postcode = "";// 邮编
        String date = "20160805";//日期唯一号
        String dectCountry = "FC";//目的国家
        String serviceType = "DISTRIBUTOR_11358681";//线路
        int[] ordersInPackageArray = {4, 5, 6, 7};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "RS" + date + "2";//参考号1 mailno  RS595937301GB
        String base_pkg_no = "CNUK" + date + dectCountry + "2";//大包号  CNUK20160709RM00009
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "GB";
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    /**
     * 英邮泛欧(Latvia)
     *
     * @throws Exception
     */
    @Test
    public void batchLV() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String postcode = "";// 邮编
        String date = "20160805";//日期唯一号
        String dectCountry = "LV";//目的国家
        String serviceType = "DISTRIBUTOR_11358681";//线路
        int[] ordersInPackageArray = {4, 5, 6, 7};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "RS" + date + "3";//参考号1 mailno  RS595937128GB
        String base_pkg_no = "CNUK" + date + dectCountry + "3";//大包号  CNUK20160709RM00009
        System.out.println("pkg_no = " + base_pkg_no);
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "GB";
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }
//   -----------------------------------------美洲------------------------------------------------------------------------

    /**
     * 哥伦比亚批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchCO() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = "20161124";//日期唯一号
        String dectCountry = "CO";//目的国家
        String serviceType = "DISTRIBUTOR_11131307";//线路  哥伦比亚哥标准
        int[] ordersInPackageArray = {7, 8, 9, 10};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RB" + date;//参考号1 mailno  RB780569565CO
        String base_pkg_no = "CNMA" + date + dectCountry + "2";//大包号
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "70976";//邮编
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "CO";
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    /**
     * 墨西哥批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchMX() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "MX";//目的国家

        String serviceType = "DISTRIBUTOR_11131306";//线路  墨西哥标准
        int[] ordersInPackageArray = {59, 123, 65};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RZ" + date.substring(4);//参考号1 mailno  RZ040003243MH  RZ040000220MH  RZ040171365MH
        String base_pkg_no = "CNMA" + date.substring(6) + dectCountry;//大包号  CNMA20160226MX20001
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "GL43SJ";//邮编
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "MH";
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, getRandomString(new String[]{"JUCHITAN DE ZARAGOZA", "zapopan", "tangancicuaro", "Queretaro", "Mexico", "Durango"}),
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    @Test
    public void batchNL() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = "201604730778776";//日期唯一号
        String dectCountry = "NL";//目的国家
        String serviceType = "DISTRIBUTOR_11180269";//线路
        int[] ordersInPackageArray = {10, 8, 6, 12};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "LPU" + date + "XXX";//参考号1 mailno
        String base_pkg_no = "CNNL" + date + "001";//大包号
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "08945";//邮编
        String cainiaoLPNO = "LP" + date + "XXX";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }

    private String combinateBatchXml(String servicetype, String mailNo, String postcode, String country,
                                     String city, String orderCode, String pkg_No, int ordersInPackage) {
        String producttionName = getOneProductionName();
        String productionCnName = producttionName.split("-")[0];
        String productionEnName = producttionName.split("-")[1];
        String xml =
                "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2013-02-25 00:00:00</eventTime>" +
                        "<eventSource>taobao</eventSource>" +
                        "<eventTarget>" + servicetype + "</eventTarget>" +
                        "</eventHeader><eventBody><OrderInfos><product>" +
                        "<productNameCN>" + productionCnName + "</productNameCN>" +
                        "<productNameEN>" + productionEnName + "</productNameEN>" +
                        "<productQantity>" + randomMinMax(1, 5) + "</productQantity>" +
                        "<productCateCN/>" +
                        "<productCateEN>Cuddly cat Toy</productCateEN>" +
                        "<productId /><producingArea />" +
                        "<productWeight>500</productWeight>" +
                        "<productPrice>10</productPrice>" +
                        "</product>" +
                        "</OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>MAM0001</logisticsOrderId><tradeId>3018196085</tradeId>" +
                        "<mailNo>" + mailNo + "</mailNo>" +
                        "<Rcountry>" + country + "</Rcountry>" +
                        "<Rprovince>Barcelona</Rprovince><Rcity>" + city + "</Rcity><Remail><EMAIL></Remail><Raddress>Avda. Jose Pedro Alessandri 605 Depto. 801, Nunoa</Raddress>" +
                        "<Rpostcode>" + postcode + "</Rpostcode><Rname>Sandra Zambrno</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId>" +
                        "<Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode>" +
                        "<channel>HK</channel><Itotleweight>" + randomMinMax(50, 500) + "</Itotleweight><Itotlevalue>" + randomMinMax(50, 2000) + "</Itotlevalue><totleweight>2168</totleweight>" +
                        "<country>CN</country>" +
                        "<mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress/>" +
                        "<packageCode>" + pkg_No + "</packageCode>" +
                        "<orderCode>" + orderCode + "</orderCode>" +
                        "<packageWeight>" + randomMinMax(4000, 75000) + "</packageWeight>" +
                        "<ordersInPackage>" + ordersInPackage + "</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>";

        return xml;
    }

    @Test
    public void batchChile() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "CL";//目的国家
        String serviceType = "DISTRIBUTOR_8782834";//线路
        int[] ordersInPackageArray = {30};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "1280000000000200";//参考号1 mailno  12892500007000200242021001


        String base_pkg_no = "CNCL" + date;//大包号
        System.out.println("pkg_no = " + date);
        String postcode = "1234567";//邮编
        String cainiaoLPNO = "LPCL" + date;//参考号2  菜鸟lp号 前缀
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                Random random = new Random();
                StringBuffer buf = new StringBuffer();
                for (int k = 0; k < 3; k++) {
                    buf.append(random.nextInt(10));//取三个随机数追加到StringBuffer
                }
                String eawb_reference1 = eawb_reference1Pre + buf.toString() + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        "",
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }
    }

    @Test
    public void batchCorreosPAQ() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String dectCountry = "ES";//目的国家
        String destCity = "madrid";
        String serviceType = "DISTRIBUTOR_902950";//线路
        int[] ordersInPackageArray = {132, 144, 56};//袋子预报内装数
//		-------------------需要改的参数--end---------------------------------------------------------------
        String eawb_reference1Pre = "PQ48K2" + date.substring(4);//参考号1 mailno  PQ48K2-0415190530103-530Y  PQ48K20415190530103530Y
        String base_pkg_no = "YE01R" + date;//大包号
        String cainiaoLPNO = "LPESPAQ" + date.substring(4);//参考号2  菜鸟lp号 前缀
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + getOneLetter();
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        getCorreosPostcode(),
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("西邮标准大包号：--------------------- " + pkg_no);
        }
    }

    @Test
    public void batchCorreosOM() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String dectCountry = "ES";//目的国家
        String destCity = "Villena";
        String serviceType = "DISTRIBUTOR_903172";//线路
        int[] ordersInPackageArray = {27};//袋子预报内装数
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "YE02N" + date;//大包号
        System.out.println("pkg_no = " + date);
        String cainiaoLPNO = "LPESOM" + date.substring(4);//参考号2  菜鸟lp号 前缀
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {

                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String eawb_reference1 = eawb_reference2;
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        getCorreosPostcode(),
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("西邮经济大包号：--------------------- " + pkg_no);
        }
    }

    @Test
    public void batchRoyalMailOM() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String dectCountry = "GB";//目的国家
        String destCity = "london";
        String serviceType = "DISTRIBUTOR_1682692";//线路
        int[] ordersInPackageArray = {68, 227};//袋子预报内装数
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "YE04U" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String eawb_reference1 = eawb_reference2;
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        "SW4 8DH",//LU40RW,SE6 2LN,NW9 9LU,BT23 8BN,LU5 4HH
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("英邮经济大包号：--------------------- " + pkg_no);
        }
    }

    @Test
    public void batchRoyalMailPAQ() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String dectCountry = "GB";//目的国家
        String destCity = "Manchester";
        String serviceType = "DISTRIBUTOR_1499269";//线路
        int[] ordersInPackageArray = {123, 153};//袋子预报内装数
//		-------------------需要改的参数--end---------------------------------------------------------------
        String preEawb_reference1 = "JJ" + date.substring(4);//JJ116331515GB
        String base_pkg_no = "XE03T" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = preEawb_reference1 + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "GB";
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        "SW4 8DH",//LU40RW,SE6 2LN,NW9 9LU,BT23 8BN,LU5 4HH
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("英邮标准大包号：--------------------- " + pkg_no);
        }
    }

    @Test
    public void batchRoyaMailInternational() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String serviceType = "DISTRIBUTOR_11358681";//线路
        int[] ordersInPackageArray = {145};//袋子预报内装数
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "XE0600" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = "RS" + date.substring(4) + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "GB";//RS597124928GB
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                String destCountry = getRMInternationalCountry();
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        getCorreosPostcode(),
                        destCountry, destCountry,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("英邮泛欧大包号：--------------------- " + pkg_no);
        }
    }

    @Test
    public void batchPostNL() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String serviceType = "DISTRIBUTOR_11180269";//线路
        String dectCountry = "NL";//目的国家
        int[] ordersInPackageArray = {150, 173};//袋子预报内装数 200*10
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "XE05NL" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        //for (int k = 0; k < 10; k++) {

        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String destCity = getRandomString(new String[]{"Rotterdam", "Kampen", "Utrecht", "rotterdam", "Reeuwijk", "Putte", "Den Haag", "Eindhoven", "Amsterdam", "den haag", "Nijmegen", "Hardenberg", "Wolfheze", "Sevenum", "Roosendaal", "Gorinchem", "Tilburg", "amsterdam", "Lelystad", "zaandam", "Delft", "Oost-Souburg", "PANNINGEN", "s-gravendeel", "Etten-Leur", "belfeld", "heerlen", "schoonrewoerd", "Zwolle", "Den helder", "Meppel", "Almere", "Sint-Oedenrode", "Lisse", "Vianen", "Ridderkerk", "3561RT", "Haarlem", "Zoetermeer", "Den haag", "The Hague", "s-Hertogenbosch", "dinteloord", "Netherlands", "Kockengen", "Cuijk", "Balk", "jabeek", "Waalwijk", "Amersfoort", "koedijk", "Katwijk", "enschede", "Berg en Terblijt", "nieuwegein", "Renkum", "Sappemeer", "simonshaven", "assen", "Hardinxveld-Giessendam", "Heeze", "Alphen aan den Rijn", "Ravenstein", "Veldhoven", "Maastricht", "ASSEN", "Enschede", "Rhenen", "De Meern", "Woerden", "heeten", "Kootstertille", "IJmuiden", "Kapelle", "Weesp", "Puttershoek", "honselersdijk", "Escharen", "Groningen", "Zuidland", "Mechelen", "Harderwijk", "Roermond", "2106 EG", "Drachten", "westervoort", "Putten", "Alblasserdam", "Gemert", "Panningen", "eersel", "zaandijk", "Zuidlaren", "HEESCH", "Den Bosch", "Geleen", "Barneveld", "Drunen", "Batenburg", "VEENENDAAL", "kruiningen", "meerkerk", "Vlaardingen", "Capelle a/d IJssel", "obdam", "utrecht", "landgraaf", "Ingelum", "Ommen", "Hoensbroek", "Klaaswaal", "Heerlen", "Hilversum", "Someren", "Duiven", "Nieuwerkerk aan den IJssel", "Purmerend", "Zwijndrecht", "Denekamp", "Blyham", "Zevenbergschen Hoek", "klazienaveen", "Bruinisse", "Poeldijk", "Brielle", "Schildwolde", "Enkhuizen", "Nieuwveen", "nijmegen", "Nieuw-Weerdinge", "Bleiswijk", "Apeldoorn", "Rijkevoort", "ermelo", "rijssen", "Abcoude", "Noordgouwe", "Alkmaar", "almere", "APPELSCHA", "Fijnaart", "Neede", "ROTTERDAM", "Almere Haven", "Arnhem", "Rijswijk", "hoogerheide", "Saasveld", "harskamp", "lemele", "Harskamp", "WEESP", "Strijen", "Bennebroek", "voorhout", "Weert", "Oss", "veenendaal", "DELFT", "hoofddorp", "Gelderland", "Velsen-Noord", "Nieuw-Beijerland", "Schagen", "breda", "Andijk", "naarden", "leersum", "Kerkrade", "Wapse", "Lochem", "Waardhuizen", "Oosterhout", "Halsteren", "groningen", "Voorburg", "Wapenveld", "AMSTERDAM", "roosendaal", "Emmen", "den helder", "Geldrop", "eemnes", "Grevenbicht", "Zundert", "Bergen op zoom", "Waddinxveen", "Breda", "Silvolde", "Rijen", "hoensbroek", "Wassenaar", "Leiden", "hoogezand", "mookhoek", "Zandvoort", "Maassluis", "putten", "ZUIDSCHERMER", "Katwijk aan Zee", "apeldoorn", "Helmond", "Baarn", "helmond", "De Rips", "Beek", "sint michielsgestel", "Almelo", "Leek", "Steensel", "Maarssen", "afferden", "wernhout", "Doetinchem", "Dalfsen", "Hellevoetsluis", "Veenendaal", "Noordscheschut", "Heerde", "Zutphen", "doetinchem", "Soest", "amersfoort", "Goes", "katwijk", "Simpelveld", "Oudemirdum", "Odijk", "Axel", "Esch", "spijkenisse", "Etten-leur", "Suwald", "DEN BURG", "ridderkerk", "Sommelsdijk", "Cothen", "purmerend", "Leidschendam", "Schoonhoven", "Zevenhuizen", "Stadskanaal", "Drouwen", "Leerbroek", "almelo", "gouda", "Groot-Ammers", "Krommenie", "Breugel", "Berlicum", "Hengelo", "delft", "Sittard", "ijsselstein", "vuren", "maastricht", "Hoogblokland", "Velp NB", "Harkstede", "Landgraaf", "Albergen", "hoogeveen", "Vaassen", "Dodewaard", "Oudenbosch", "Westerbork", "Dinteloord", "Westerhaar", "nieuwerkerk Aan Den ijssel", "2665 CJ BLEIJSWIJK", "Bergschenhoek", "landsmeer", "bergen op zoom", "Koudum", "Oirschot", "De meern", "Hoek", "Bennekom", "Nieuwegein", "slaakweg", "kollum", "Gaanderen", "Dordrecht", "heinenoord", "leiden", "Gouda", "Brunssum", "Philippine", "Werkendam", "dronten", "Arnemuiden", "Hasselt", "6042CN Roermond", "oosterhout nb", "Oisterwijk", "soesterberg", "Boxmeer", "Hendrik ido ambacht", "LEIDEN", "bern", "Delfgauw", "Velsen-Zuid", "rijsenhout", "heerde", "wolvega", "Obdam", "De Rijp", "ommen", "Valkenswaard", "witmarsum", "Alteveer", "Eck en Wiel (Buren)", "8435SG Donkerbroek", "Castenray", "Twello", "Montfoort", "Hoeven", "velddriel", "sprundel", "Geldermalsen", "Emmeloord", "Waalre", "harderwijk", "haarlem", "dordrecht", "Nunspeet", "Oosteind", "Velden", "oss", "Grave", "Geulle", "bodegraven", "Uithoorn", "Lemmer", "Coevorden", "Amstelveen", "Almere-Haven", "Schiedam", "Geffen", "Oudkarspel", "Rijssen", "Rosmalen", "Hoofddorp", "Raamsdonksveer", "Amsterdam zuidoost", "Weerselo", "Culemborg", "Wierden", "Woubrugge", "Assen", "Joure", "haaksbergen", "Rijsenhout", "Hoorn", "Oud vossemeer", "Haaksbergen", "Sint-Pancras", "Nieuw Amsterdam", "Beverwijk", "Haaften", "Hoogeveen", "nuenen", "Spijkenisse", "beesd", "Loenen aan de Vecht", "Posterholt", "Alphen", "beek", "breukelen", "Berkel en Rodenrijs", "Wagenberg", "Ijlst", "Vorden", "zoetermeer", "Oud-Beijerland", "megen", "valkenswaard", "Voorhout", "Franeker", "Voorthuizen", "stolwijk", "Houten", "Geesteren", "Heemskerk", "Den Helder", "SPIJKENISSE", "made", "Made", "'s gravenzande", "s'hertogenbosch", "oostvoorne", "zevenaar", "Sint Maarten", "Volkel", "Dongen", "Middenbeemster", "nieuw-vennep", "Bunnik", "roermond", "Oudeschans", "uitgeest", "barendrecht", "Monster", "Rozenburg", "Haren, Gn", "Wernhout", "Akersloot", "Lopik", "DEDEMSVAART", "vlaardingen", "Amsterdm", "Hijum", "Pijnacker", "WINSUM GN", "assendelft", "Leeuwarden", "Rolde", "Lekkerkerk", "Ede", "DIDAM", "Vroomshoop", "schinveld", "schiedam", "Herten", "EDE", "Kortenhoef", "sint nicolaasga", "Velp", "Rhoon", "Zuidhorn", "Aalsmeer", "Vlissingen", "hendrik ido ambacht", "Diemen", "Borne", "ijmuiden", "Uithuizermeeden", "strijen", "Vught", "tilburg", "Wolvega", "Tiel", "Zetten", "Hoek van Holland", "Hoogezand", "rijswijk", "Hapert", "wassenaar", "Udenhout", "Cromvoirt", "Kornhorn", "Stramproy", "sibculo", "arnhem", "Neerbeek", "Nw. en St. Joosland", "Monnickendam", "Meijel", "4301XL ZIERIKZEE", "Hollum", "Tegelen", "goes", "Leiderdorp", "Kwadendamme", "Gennep", "Zuid-Scharwoude", "ZAANDAM", "HOOGLAND", "Wageningen", "lelystad", "Bodegraven", "velp", "BEERTA", "Oost Souburg", "hilversum", "voorburg", "Schoonebeek", "Oost,- West en Middelbeers", "Nederland", "Zeewolde", "Langweer", "ede", "Huizen", "houten", "Sassenheim", "buinerveen", "elst", "s Gravenzande", "Dronten", "Winschoten", "Riel", "nederland", "kerkrade", "Den Haag (The Hague)", "Nijkerk", "grave", "Milheeze", "benschop", "Ommeren", "Hulst", "Kollumerzwaag", "urk", "Amstelhoek", "muntendam", "huizen", "Volendam", "ALBLASSERDAM", "Opijnen", "geldermalsen", "Castricum"});
                String postCode = getRandomString(new String[]{"518000", "518102", "310023", "230601", "430023", "321404", "523941", "325603", "510000", "314300", "314001", "315314", "518103", "266000", "322000", "315000", "511400", "341000", "518111", "465550", "330000", "518116", "581000", "518109", "529600", "523118", "528251", "415800", "528225", "528000", "362000", "201299", "518131", "350000", "363000", "510260", "322118", "321000", "201318", "518112", "518055", "362212", "523900", "325800", "518126", "518129", "528425", "516600", "518100", "558003", "054001", "200120", "518400", "314100", "522000", "325100", "322200", "351100", "110000", "362333", "523000", "362700", "215000", "310000", "066000", "322099", "315206", "515800", "511500", "311215", "321100", "523863", "516081", "511446", "102600", "201612", "233000", "521000", "361000", "528200", "074004", "528441", "528042", "515041", "226000", "201803", "325604", "510320", "516001", "510140", "276000", "321300", "511430", "523690", "528471", "518003", "523129", "201400", "430070", "000000", "361001", "310020", "320000", "516029", "100020", "520620", "510650", "510145", "350003", "332200", "211100", "215600", "518110", "518132", "510405", "215100", "266101", "317200", "51810 ", "518028", "311112", "523899", "317300", "200063", "580000", "325204", "518031", "325000", "515010", "215008", "325804", "125100", "518054", "201800", "510165", "518019", "350109", "201104", "311512", "310051", "510660", "512500", "310015", "221000", "325200", "211400", "315033", "310018", "518101", "410007", "311800", "510080", "518104", "510599", "518014", "510025", "100011", "528400", "200023", "511490", "201315", "215500", "510520", "310030", "322017", "450016", "529000", "213161", "200900", "100018", "472200", "510420", "313000", "510430", "518133", "518004", "523770", "531214", "510100", "518042", "310052", "510630", "250100", "201808", "300163", "322008", "240000", "312400", "201108", "201418", "510403", "516000", "125000", "518114", "510235", "516006", "201106", "510641", "315012", "313100", "214028", "518108", "519090", "362400", "311804", "510070", "332000", "518115", "510700", "523915", "510510", "266071", "239341", "523039", "323000", "410510", "318000", "621700", "271600", "200000", "210209", "511800", "310019", "361009", "518113", "518033", "519000", "214000", "102211", "311100", "510620", "315334", "417600", "322100", "362018", "322299", "510640", "410000", "310009", "102208", "365000", "315400", "518048", "325600", "310014"});
                String eawb_reference1 = "RS" + date.substring(4) + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "NL";//RS597124928GB
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postCode,
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("荷兰标准大包号：--------------------- " + pkg_no);
        }
        //}
    }

    /**
     * 用于西邮PAQ生成最后一个字母
     *
     * @return
     */
    private String getOneLetter() {
        String chars = "abcdefghijklmnopqrstuvwxyz";
        String oneLetter = String.valueOf(chars.charAt((int) (Math.random() * 26)));
        System.out.println("随机字母" + oneLetter);
        return oneLetter;
    }

    private String getRMInternationalCountry() {
        String[] str = new String[]{"LV", "IT", "FR"};
        return str[random0_2()];
    }

    private String getOneProductionName() {
        String[] str = new String[]{"棉外套-coat", "短裙-philabeg", "帽子-hat", "鞋子-shoes", "衬衫-shirt", "手机-phone", "袜子-stockings", "缎带-Ribbons", "墨水盒-Ink Cartridges", "童装套装-Children's Sets", "耳机-Headphones"};
        return getRandomString(str);
    }

    private String getRandomString(String[] arrs) {
        return arrs[getRandom0_n(arrs.length - 1)];
    }

    /**
     * 生成0-num的数据
     *
     * @param num
     * @return
     */
    private int getRandom0_n(int num) {
        Random rd = new Random();
        int n = rd.nextInt(num + 1);
        return n;
    }

    private String getCorreosPostcode() {
        String str = "";
        for (int i = 0; i < 5; i++) {
            str += random0_9();
        }
        return str;
    }

    private int random0_9() {
        //return (int)(Math.random()*10)%6+1;
        Random rd = new Random();
        int num = rd.nextInt(10);
        return num;
    }

    private int random0_2() {
        //return (int)(Math.random()*10)%6+1;
        Random rd = new Random();
        int num = rd.nextInt(3);
        return num;
    }

    private int randomMinMax(int min, int max) {
        Random random = new Random();
        int s = random.nextInt(max) % (max - min + 1) + min;
        return s;
    }

    @Test
    public void testXml() throws DocumentException {
        String xml = "<request>\n" +
                "    <logisticsOrderCode>LP20160920141206</logisticsOrderCode>\n" +
                "    <sender>\n" +
                "        <imID>BifrostTester</imID>\n" +
                "        <name>BifrostTester</name>\n" +
                "        <address>\n" +
                "            <country>CN</country>\n" +
                "            <detailAddress>some detailed address</detailAddress>\n" +
                "        </address>\n" +
                "    </sender>\n" +
                "    <receiver>\n" +
                "        <imID>receiverTester</imID>\n" +
                "        <name>receiverTester</name>\n" +
                "        <zipCode>123123</zipCode>\n" +
                "        <address>\n" +
                "            <country>PE</country>\n" +
                "            <detailAddress>detailed receiver address</detailAddress>\n" +
                "        </address>\n" +
                "    </receiver>\n" +
                "    <parcel>\n" +
                "        <weight>1000</weight>\n" +
                "        <weightUnit>g</weightUnit>\n" +
                "        <suggestedWeight>1000</suggestedWeight>\n" +
                "        <price>40</price>\n" +
                "        <priceUnit>CENT</priceUnit>\n" +
                "        <goodsList>\n" +
                "            <goods>\n" +
                "                <name>name</name>\n" +
                "                <cnName>商品</cnName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>100</price>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <quantity>10</quantity>\n" +
                "                <url>some url</url>\n" +
                "            </goods>\n" +
                "        </goodsList>\n" +
                "    </parcel>\n" +
                "    <trackingNumber>1120160920141207</trackingNumber>\n" +
                "    <bizType>1</bizType>\n" +
                "</request>";
        Document doc = DocumentHelper.parseText(xml);
        Element rootElement = doc.getRootElement();
        String logisticsOrderCode = XMLUtil.getElementContent(rootElement, "logisticsOrderCode");
        String trackingNumber = XMLUtil.getElementContent(rootElement, "trackingNumber");
        String bizType = XMLUtil.getElementContent(rootElement, "bizType");
        System.out.println(logisticsOrderCode);
        System.out.println(trackingNumber);
        System.out.println(bizType);

        Iterator senderIterator = rootElement.elementIterator("sender");
        if (senderIterator.hasNext()) {
            Element senderElement = (Element) senderIterator.next();
            String imID = XMLUtil.getElementContent(senderElement, "imID");
            String name = XMLUtil.getElementContent(senderElement, "name");
            System.out.println(imID+"--"+name);
            Iterator addressIterator = senderElement.elementIterator("address");
            if (addressIterator.hasNext()) {
                Element addressElement = (Element) addressIterator.next();
                String country = XMLUtil.getElementContent(addressElement, "country");
                String detailAddress = XMLUtil.getElementContent(addressElement, "detailAddress");
                System.out.println(country+"--"+detailAddress);
            }

        }
//        List<org.dom4j.Element> list = doc.selectNodes("/message");
//        for (org.dom4j.Element element : list) {
//            System.out.println("name : "+ element.getName() + "->UniquePath:" + element.getUniquePath() + "->value:"+element.getTextTrim());
//        }
    }

    @Test
    public void batchTransactionFirst() throws Exception {
        String xml = "";
        xml = "<request>\n" +
                "    <batchNo>YR05000170306100033</batchNo>\n" +
                "    <preCPResCode>STORE_11750293</preCPResCode>\n" +
                "    <currentCPResCode>TRUNK_12720648</currentCPResCode>\n" +
                "    <bigBagList>\n" +
                "            <bigBag>\n" +
                "                <bigBagID>LP00062858846757</bigBagID>\n" +
                "            </bigBag>\n" +
                "            <bigBag>\n" +
                "                <bigBagID>LP00062858846767</bigBagID>\n" +
                "            </bigBag>\n" +
                "            <bigBag>\n" +
                "                <bigBagID>LP00062858846777</bigBagID>\n" +
                "            </bigBag>\n" +
                "    </bigBagList>\n" +
                " <truckNumber></truckNumber>"+
                "</request>";
        String resultActual = cainiaoService.batchTransactionFirst(xml);
        System.out.println("resultActual = " + resultActual);
    }

    @Test
    public void packagebatchTransactionFirst() throws Exception {
        String xml = "";
        xml = "<request>\n" +
                "    <bigBagID>YR05000170306100021ZMJ</bigBagID>\n" +
                "    <sortCode>YW1234</sortCode>\n" +
                "    <preCPResCode>STORE_12720648</preCPResCode>\n" +
                "    <currentCPResCode>TRUNK_11750293</currentCPResCode>\n" +
                "    <grossWeight>1234</grossWeight>\n" +
                "    <netWeight>1234</netWeight>\n" +
                "    <labelWeight>1234</labelWeight>\n" +
                "    <packingWeight>1234</packingWeight>\n" +
                "    <standardWeight>1234</standardWeight>\n" +
                "    <weightUnit>g</weightUnit>\n" +
                "    <parcelQty>123</parcelQty>\n" +
                "    <parcelList>\n" +
                "            <parcel>\n" +
                "                <logisticsOrderCode>LP00062858846767</logisticsOrderCode>\n" +
                "                <trackingNumber>AECA0001571296RU1</trackingNumber>\n" +
                "            </parcel>\n" +
                "             <parcel>\n" +
                "                <logisticsOrderCode>LP00062858846768</logisticsOrderCode>\n" +
                "                <trackingNumber>AECA0001571296RU1</trackingNumber>\n" +
                "            </parcel>\n" +
                "            <parcel>\n" +
                "                <logisticsOrderCode>LP00062858846769</logisticsOrderCode>\n" +
                "                <trackingNumber>AECA0001571296RU1</trackingNumber>\n" +
                "            </parcel>\n" +
                "    </parcelList>\n" +
                "</request>";
        String resultActual = cainiaoService.packagebatchTransactionFirst(xml,null);
        System.out.println("resultActual = " + resultActual);
    }

    @Test
    public void notifynew() throws Exception {
        String xml = "<request>\n" +
                "    <logisticsOrderCode>LP00125040838981</logisticsOrderCode>\n" +
                "    <sender>\n" +
                "        <imID>cn1524546797qiyy</imID>\n" +
                "        <name>IKSNAIL</name>\n" +
                "        <phone>***********</phone>\n" +
                "        <zipCode>518000</zipCode>\n" +
                "        <address>\n" +
                "            <country>China</country>\n" +
                "            <province>guang dong sheng</province>\n" +
                "            <city>shen zhen shi</city>\n" +
                "            <district>long gang qu</district>\n" +
                "            <detailAddress>ping hu jie dao~~~18th Fenghuang Industrial Park Fenghuang Road</detailAddress>\n" +
                "        </address>\n" +
                "        <storeName/>\n" +
                "    </sender>\n" +
                "    <receiver>\n" +
                "        <imID>sa1247511511foat</imID>\n" +
                "        <name>ahmad</name>\n" +
                "        <phone>+966</phone>\n" +
                "        <mobile>559775335</mobile>\n" +
                "        <email><EMAIL></email>\n" +
                "        <zipCode>67388</zipCode>\n" +
                "        <address>\n" +
                "            <country>SA</country>\n" +
                "            <province>saudi Arabia</province>\n" +
                "            <city>Aseer</city>\n" +
                "            <district/>\n" +
                "            <detailAddress>Namas</detailAddress>\n" +
                "        </address>\n" +
                "    </receiver>\n" +
                "    <parcel>\n" +
                "        <weight>2180</weight>\n" +
                "        <weightUnit>g</weightUnit>\n" +
                "        <suggestedWeight>2180</suggestedWeight>\n" +
                "        <price>2500</price>\n" +
                "        <priceUnit>CENT</priceUnit>\n" +
                "        <dimensionUnit>cm</dimensionUnit>\n" +
                "        <goodsList>\n" +
                "            <goods>\n" +
                "                <productID>32948234501</productID>\n" +
                "                <name>GPS Stand</name>\n" +
                "                <cnName>GPS支架</cnName>\n" +
                "                <categoryID>200003386</categoryID>\n" +
                "                <categoryName>GPS Stand</categoryName>\n" +
                "                <categoryCNName>GPS支架</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>100</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>100</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32948234501.html</url>\n" +
                "                <productCategory>Automobiles &amp; Motorcycles|Interior Accessories|Mounts &amp; Holder|GPS Stand</productCategory>\n" +
                "                <suggestedCNName>自行车手机支架</suggestedCNName>\n" +
                "                <suggestedENName>Stand</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32960583345</productID>\n" +
                "                <name>Air Freshener</name>\n" +
                "                <cnName>香氛</cnName>\n" +
                "                <categoryID>200000187</categoryID>\n" +
                "                <categoryName>Air Freshener</categoryName>\n" +
                "                <categoryCNName>香氛</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>200</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>200</declarePrice>\n" +
                "                <quantity>2</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32960583345.html</url>\n" +
                "                <productCategory>Automobiles &amp; Motorcycles|Interior Accessories|Air Freshener</productCategory>\n" +
                "                <suggestedCNName>手机GPS支架</suggestedCNName>\n" +
                "                <suggestedENName>Phone GPS Bracket</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32733019318</productID>\n" +
                "                <name>down coat</name>\n" +
                "                <cnName>男装</cnName>\n" +
                "                <categoryID>200128143</categoryID>\n" +
                "                <categoryName>Down Coats</categoryName>\n" +
                "                <categoryCNName>男装</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>500</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>500</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32733019318.html</url>\n" +
                "                <productCategory>Men's Clothing|Coats &amp; Jackets|Down Coats</productCategory>\n" +
                "                <suggestedCNName>涤纶、棉毛男式风衣</suggestedCNName>\n" +
                "                <suggestedENName>Men Polyester Cotton Wool Trench</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32893373088</productID>\n" +
                "                <name>Jeans</name>\n" +
                "                <cnName>牛仔裤</cnName>\n" +
                "                <categoryID>200000378</categoryID>\n" +
                "                <categoryName>Jeans</categoryName>\n" +
                "                <categoryCNName>牛仔裤</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>1000</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>1000</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32893373088.html</url>\n" +
                "                <productCategory>Men's Clothing|Jeans</productCategory>\n" +
                "                <suggestedCNName>男式丹宁布牛仔裤</suggestedCNName>\n" +
                "                <suggestedENName>men denim jeans</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <name>casual shoes</name>\n" +
                "                <cnName>休闲鞋</cnName>\n" +
                "                <categoryID>0</categoryID>\n" +
                "                <categoryName>casual shoes</categoryName>\n" +
                "                <categoryCNName>休闲鞋</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>500</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>500</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url/>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32913631986</productID>\n" +
                "                <name>Skullies &amp; Beanies(HatSet245XOXO)</name>\n" +
                "                <cnName>无檐便帽/套头帽/蒙面帽</cnName>\n" +
                "                <categoryID>200000447</categoryID>\n" +
                "                <categoryName>Skullies &amp; Beanies</categoryName>\n" +
                "                <categoryCNName>无檐便帽/套头帽/蒙面帽</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>200</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>200</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32913631986.html</url>\n" +
                "                <productCategory>Apparel Accessories|Hats &amp; Caps|Skullies &amp; Beanies</productCategory>\n" +
                "                <suggestedCNName>羊毛针织冬帽</suggestedCNName>\n" +
                "                <suggestedENName>Wool Knitted Winter Hats</suggestedENName>\n" +
                "            </goods>\n" +
                "        </goodsList>\n" +
                "    </parcel>\n" +
                "    <customs>\n" +
                "        <declarePriceTotal>2500</declarePriceTotal>\n" +
                "    </customs>\n" +
                "    <returnParcel>\n" +
                "        <imID>cn1524546797qiyy</imID>\n" +
                "        <name>IKSNAIL</name>\n" +
                "        <phone>***********</phone>\n" +
                "        <undeliverableOption>1</undeliverableOption>\n" +
                "        <zipCode>518000</zipCode>\n" +
                "        <address>\n" +
                "            <country>China</country>\n" +
                "            <province>guang dong sheng</province>\n" +
                "            <city>shen zhen shi</city>\n" +
                "            <district>long gang qu</district>\n" +
                "            <detailAddress>ping hu jie dao~~~18th Fenghuang Industrial Park Fenghuang Road</detailAddress>\n" +
                "        </address>\n" +
                "    </returnParcel>\n" +
                "    <trackingNumber>************</trackingNumber>\n" +
                "    <preCPResCode>Tran_Store_13422200</preCPResCode>\n" +
                "    <currentCPResCode>TRUNK_13453341</currentCPResCode>\n" +
                "    <nextCPResCode>GATE_13452751</nextCPResCode>\n" +
                "    <interCPResCode>GATE_13452751</interCPResCode>\n" +
                "    <routingTrial>1</routingTrial>\n" +
                "    <bizType>CN_4PL_CONSOLIDATION</bizType>\n" +
                "    <logisticsOrderCreateTime>2019-02-01 01:34:48</logisticsOrderCreateTime>\n" +
                "    <sortCode>Other</sortCode>\n" +
                "    <laneCode>L_CN_PREMIUM_SINOARAMEX</laneCode>\n" +
                "</request>";
        cainiaoService.reportTransaction4Gsdp(xml, "aaa");

    }

    @Test
    public void testPoland() throws Exception {
        String xml = "\n" +
                "<request>\n" +
                "    <logisticsOrderCode>LP00125040838981</logisticsOrderCode>\n" +
                "    <sender>\n" +
                "        <imID>cn1524546797qiyy</imID>\n" +
                "        <name>IKSNAIL</name>\n" +
                "        <phone>***********</phone>\n" +
                "        <zipCode>518000</zipCode>\n" +
                "        <address>\n" +
                "            <country>China</country>\n" +
                "            <province>guang dong sheng</province>\n" +
                "            <city>shen zhen shi</city>\n" +
                "            <district>long gang qu</district>\n" +
                "            <detailAddress>ping hu jie dao~~~18th Fenghuang Industrial Park Fenghuang Road</detailAddress>\n" +
                "        </address>\n" +
                "        <storeName/>\n" +
                "    </sender>\n" +
                "    <receiver>\n" +
                "        <imID>sa1247511511foat</imID>\n" +
                "        <name>ahmad</name>\n" +
                "        <phone>+966</phone>\n" +
                "        <mobile>559775335</mobile>\n" +
                "        <email><EMAIL></email>\n" +
                "        <zipCode>67388</zipCode>\n" +
                "        <address>\n" +
                "            <country>SA</country>\n" +
                "            <province>saudi Arabia</province>\n" +
                "            <city>Aseer</city>\n" +
                "            <district/>\n" +
                "            <detailAddress>Namas</detailAddress>\n" +
                "        </address>\n" +
                "    </receiver>\n" +
                "    <parcel>\n" +
                "        <weight>2020</weight>\n" +
                "        <weightUnit>g</weightUnit>\n" +
                "        <suggestedWeight>2180</suggestedWeight>\n" +
                "        <price>2500</price>\n" +
                "        <priceUnit>CENT</priceUnit>\n" +
                "        <length></length>\n" +
                "        <width>5</width>\n" +
                "        <height>5</height>\n" +
                "        <bigBagID>JAE1901310013</bigBagID>\n" +
                "        <parcelQuantity>107</parcelQuantity>\n" +
                "        <bigBagWeight>336050</bigBagWeight>\n" +
                "        <bigBagWeightUnit>GRAM</bigBagWeightUnit>\n" +
                "        <goodsList>\n" +
                "            <goods>\n" +
                "                <productID>32948234501</productID>\n" +
                "                <name>GPS Stand</name>\n" +
                "                <cnName>GPS支架</cnName>\n" +
                "                <categoryID>200003386</categoryID>\n" +
                "                <categoryName>GPS Stand</categoryName>\n" +
                "                <categoryCNName>GPS支架</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>100</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>100</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32948234501.html</url>\n" +
                "                <productCategory>Automobiles &amp; Motorcycles|Interior Accessories|Mounts &amp; Holder|GPS Stand</productCategory>\n" +
                "                <weight>60</weight>\n" +
                "                <weightUnit>g</weightUnit>\n" +
                "                <suggestedCNName>自行车手机支架</suggestedCNName>\n" +
                "                <suggestedENName>Stand</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32960583345</productID>\n" +
                "                <name>Air Freshener</name>\n" +
                "                <cnName>香氛</cnName>\n" +
                "                <categoryID>200000187</categoryID>\n" +
                "                <categoryName>Air Freshener</categoryName>\n" +
                "                <categoryCNName>香氛</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>200</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>200</declarePrice>\n" +
                "                <quantity>2</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32960583345.html</url>\n" +
                "                <productCategory>Automobiles &amp; Motorcycles|Interior Accessories|Air Freshener</productCategory>\n" +
                "                <weight>200</weight>\n" +
                "                <weightUnit>g</weightUnit>\n" +
                "                <suggestedCNName>手机GPS支架</suggestedCNName>\n" +
                "                <suggestedENName>Phone GPS Bracket</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32733019318</productID>\n" +
                "                <name>down coat</name>\n" +
                "                <cnName>男装</cnName>\n" +
                "                <categoryID>200128143</categoryID>\n" +
                "                <categoryName>Down Coats</categoryName>\n" +
                "                <categoryCNName>男装</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>500</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>500</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32733019318.html</url>\n" +
                "                <productCategory>Men's Clothing|Coats &amp; Jackets|Down Coats</productCategory>\n" +
                "                <weight>300</weight>\n" +
                "                <weightUnit>g</weightUnit>\n" +
                "                <suggestedCNName>涤纶、棉毛男式风衣</suggestedCNName>\n" +
                "                <suggestedENName>Men Polyester Cotton Wool Trench</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32893373088</productID>\n" +
                "                <name>Jeans</name>\n" +
                "                <cnName>牛仔裤</cnName>\n" +
                "                <categoryID>200000378</categoryID>\n" +
                "                <categoryName>Jeans</categoryName>\n" +
                "                <categoryCNName>牛仔裤</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>1000</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>1000</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32893373088.html</url>\n" +
                "                <productCategory>Men's Clothing|Jeans</productCategory>\n" +
                "                <weight>700</weight>\n" +
                "                <weightUnit>g</weightUnit>\n" +
                "                <suggestedCNName>男式丹宁布牛仔裤</suggestedCNName>\n" +
                "                <suggestedENName>men denim jeans</suggestedENName>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <name>casual shoes</name>\n" +
                "                <cnName>休闲鞋</cnName>\n" +
                "                <categoryID>0</categoryID>\n" +
                "                <categoryName>casual shoes</categoryName>\n" +
                "                <categoryCNName>休闲鞋</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>500</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>500</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url/>\n" +
                "                <weight>700</weight>\n" +
                "                <weightUnit>g</weightUnit>\n" +
                "            </goods>\n" +
                "            <goods>\n" +
                "                <productID>32913631986</productID>\n" +
                "                <name>Skullies &amp; Beanies(HatSet245XOXO)</name>\n" +
                "                <cnName>无檐便帽/套头帽/蒙面帽</cnName>\n" +
                "                <categoryID>200000447</categoryID>\n" +
                "                <categoryName>Skullies &amp; Beanies</categoryName>\n" +
                "                <categoryCNName>无檐便帽/套头帽/蒙面帽</categoryCNName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>200</price>\n" +
                "                <itemPrice>0</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>200</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url>http://www.aliexpress.com/item//32913631986.html</url>\n" +
                "                <productCategory>Apparel Accessories|Hats &amp; Caps|Skullies &amp; Beanies</productCategory>\n" +
                "                <weight>220</weight>\n" +
                "                <weightUnit>g</weightUnit>\n" +
                "                <suggestedCNName>针织帽子</suggestedCNName>\n" +
                "                <suggestedENName>Knit HAT</suggestedENName>\n" +
                "            </goods>\n" +
                "        </goodsList>\n" +
                "    </parcel>\n" +
                "    <customs>\n" +
                "        <declarePriceTotal>2500</declarePriceTotal>\n" +
                "    </customs>\n" +
                "    <returnParcel>\n" +
                "        <imID>cn1524546797qiyy</imID>\n" +
                "        <name>IKSNAIL</name>\n" +
                "        <phone>***********</phone>\n" +
                "        <undeliverableOption>1</undeliverableOption>\n" +
                "        <zipCode>518000</zipCode>\n" +
                "        <address>\n" +
                "            <country>China</country>\n" +
                "            <province>guang dong sheng</province>\n" +
                "            <city>shen zhen shi</city>\n" +
                "            <district>long gang qu</district>\n" +
                "            <detailAddress>ping hu jie dao~~~18th Fenghuang Industrial Park Fenghuang Road</detailAddress>\n" +
                "        </address>\n" +
                "    </returnParcel>\n" +
                "    <trackingNumber>************</trackingNumber>\n" +
                "    <preCPResCode>Tran_Store_13422200</preCPResCode>\n" +
                "    <currentCPResCode>TRUNK_13453341</currentCPResCode>\n" +
                "    <nextCPResCode>GATE_13452751</nextCPResCode>\n" +
                "    <interCPResCode>GATE_13452751</interCPResCode>\n" +
                "    <routingTrial>1</routingTrial>\n" +
                "    <bizType>CN_4PL_CONSOLIDATION</bizType>\n" +
                "    <logisticsOrderCreateTime>2019-02-01 01:34:48</logisticsOrderCreateTime>\n" +
                "    <sortCode>Other</sortCode>\n" +
                "    <cloudPrintData>{\\\\\\\"encryptedData\\\\\\\":\\\\\\\"AES: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\\\\\\\",\\\\\\\"signature\\\\\\\":\\\\\\\"MD:yICqqrMxeblnBlxQtC21NQ==\\\\\\\",\\\\\\\"templateURL\\\\\\\":\\\\\\\"http://cloudprint.cainiao.com/cloudprint/template/getStandardTemplate.json?template_id=245329\\\\\\\"}</cloudPrintData>\n" +
                "    <laneCode>L_CN_PREMIUM_SINOARAMEX</laneCode>\n" +
                "</request>";
        cainiaoService.batchTransaction4Gsdp(xml, "");
    }


    @Test
    public void testConsoFirstConsignPackage() throws Exception {
        String xml = "<request> \n" +
                "  <logisticsOrderCode>LP000403383446501</logisticsOrderCode>  \n" +
                "  <receiver> \n" +
                "    <imID>ru1168923880uhvl</imID>  \n" +
                "    <name>Котик Денис Анатольевич</name>  \n" +
                "    <phone>9175691825</phone>  \n" +
                "    <email><EMAIL></email>  \n" +
                "    <zipCode>143902</zipCode>  \n" +
                "    <address> \n" +
                "      <country>RU</country>  \n" +
                "      <province>Moskovskaya oblast</province>  \n" +
                "      <city>Balashikha</city>  \n" +
                "      <district/>  \n" +
                "      <detailAddress>5Post (Пятерочка) – выдача на кассе Орджоникидзе ул вл.23</detailAddress> \n" +
                "    </address> \n" +
                "  </receiver>  \n" +
                "  <customs> \n" +
                "    <passportInfo>2DC8X5IEVqxpilMnblYo+g==</passportInfo>  \n" +
                "    <declarePriceTotal>500</declarePriceTotal>  \n" +
                "    <passportIssueDate>1KnholfwfuzJ/pBsTAvV4Q==</passportIssueDate>  \n" +
                "    <passportHolderName>pVC0hHTrEfK07Mw141eIadjnt+UO4IyI8tqRn6uAlWv9N0xE0eBVMAIcET4Yqbj+</passportHolderName>  \n" +
                "    <passportSeries>ns3ob0pO0C3Ocss7RU+y2w==</passportSeries>  \n" +
                "    <passportID>9GCgDeojkoU38b3qxs+hDA==</passportID>  \n" +
                "    <passportIssueOrganization>FdpSQmRgTpZgQgfTQo10I4oXAcOgs71leJbKV8iKJ2N+rsxl7oyyUQ30+eGJe9HM5cMRRiYZmWXPHHoBz/PekYLOe+QvaG02RHXvEq+XxYtdkl9ssn+R1gzAaLt1aC30uWRbOt73qw007uM6luLrJau0wNHDIgDNGdoc5l3+Knc=</passportIssueOrganization>  \n" +
                "    <passportLastName>P/fWAU4d+O/Fl5lDyawJ6ETSSOA7Cf85I3JUn72U400=</passportLastName>  \n" +
                "    <passportMiddleName>IXTlWvByISJORt4RKRlsww==</passportMiddleName>  \n" +
                "    <passportFirstName>xtNa4mbHfKlHh3GCPCShoQ==</passportFirstName>  \n" +
                "    <taxNumber>7wU/mhW95NV3ap8OeY/h8w==</taxNumber> \n" +
                "  </customs>  \n" +
                "  <trackingNumber>**************</trackingNumber>  \n" +
                "  <preCPResCode>TRUNK_13422207</preCPResCode>  \n" +
                "  <currentCPResCode>L_AE_STANDARD_SINOPLPOST_RM</currentCPResCode>  \n" +
                "  <nextCPResCode>DISTRIBUTOR_13463001</nextCPResCode>  \n" +
                "  <interCPResCode>DISTRIBUTOR_13463001</interCPResCode>  \n" +
                "  <routingTrial>1</routingTrial>  \n" +
                "  <bizType>AE_4PL_STATION</bizType>  \n" +
                "  <tradeList/>  \n" +
                "  <consoFirstConsignPackageList> \n" +
                "    <consoFirstConsignPackage> \n" +
                "      <logisticsOrderCode>LP0016449805423701</logisticsOrderCode>  \n" +
                "      <trackingNumber>****************</trackingNumber>  \n" +
                "      <sender> \n" +
                "        <imID>cn1518208075sucs</imID>  \n" +
                "        <name>glassware</name>  \n" +
                "        <phone>glassware</phone>  \n" +
                "        <mobile>86-***********</mobile>  \n" +
                "        <email><EMAIL></email>  \n" +
                "        <zipCode>101110</zipCode>  \n" +
                "        <address> \n" +
                "          <country>China</country>  \n" +
                "          <province>he bei sheng</province>  \n" +
                "          <city>cang zhou shi</city>  \n" +
                "          <district>xin hua qu</district>  \n" +
                "          <street>he fang jie</street>  \n" +
                "          <detailAddress>cang zhou kai fa qu~~~jia xing wu liu yuan A22-28</detailAddress> \n" +
                "        </address>  \n" +
                "        <companyName>type</companyName>  \n" +
                "        <storeUrl>https://jumper.aliexpress.com/store/99999F</storeUrl>  \n" +
                "        <storeName>StoreABC</storeName>  \n" +
                "        <storeID>2216020</storeID>  \n" +
                "        <storeType>storeType</storeType>  \n" +
                "        <identity> \n" +
                "          <type>type</type>  \n" +
                "          <id>7645345</id> \n" +
                "        </identity> \n" +
                "      </sender>  \n" +
                "      <pickup/>  \n" +
                "      <parcel> \n" +
                "        <weight>1450</weight>  \n" +
                "        <weightUnit>g</weightUnit>  \n" +
                "        <suggestedWeight>1450</suggestedWeight>  \n" +
                "        <payWeight>1450</payWeight>  \n" +
                "        <itemWeight>1450</itemWeight>  \n" +
                "        <price>2696</price>  \n" +
                "        <priceUnit>CENT</priceUnit>  \n" +
                "        <length>100</length>  \n" +
                "        <width>100</width>  \n" +
                "        <height>100</height>  \n" +
                "        <dimensionUnit>cm</dimensionUnit>  \n" +
                "        <bigBagID>YR05000170306100021</bigBagID>  \n" +
                "        <parcelQuantity>25</parcelQuantity>  \n" +
                "        <bigBagWeight>1100</bigBagWeight>  \n" +
                "        <bigBagWeightUnit>g</bigBagWeightUnit>  \n" +
                "        <masterID>FM102</masterID>  \n" +
                "        <asnID>asnID</asnID>  \n" +
                "        <airID>UA998</airID>  \n" +
                "        <goodsList> \n" +
                "          <goods> \n" +
                "            <productID>32681820727</productID>  \n" +
                "            <skuID>539230</skuID>  \n" +
                "            <name>glassware</name>  \n" +
                "            <cnName>玻璃制品</cnName>  \n" +
                "            <localName>玻璃制品</localName>  \n" +
                "            <categoryID>2321</categoryID>  \n" +
                "            <categoryName>glassware</categoryName>  \n" +
                "            <categoryCNName>玻璃制品</categoryCNName>  \n" +
                "            <categoryFeature>00</categoryFeature>  \n" +
                "            <price>2696</price>  \n" +
                "            <itemPrice>0</itemPrice>  \n" +
                "            <priceUnit>CENT</priceUnit>  \n" +
                "            <priceCurrency>USD</priceCurrency>  \n" +
                "            <declarePrice>2696</declarePrice>  \n" +
                "            <quantity>1</quantity>  \n" +
                "            <extension>extension information</extension>  \n" +
                "            <url>http://www.aliexpress.com/item//32681820727.html</url>  \n" +
                "            <hsCode>8400000001</hsCode>  \n" +
                "            <mailTaxNumber>mailTaxNumber</mailTaxNumber>  \n" +
                "            <productCategory>productCategory</productCategory>  \n" +
                "            <weight>1000</weight>  \n" +
                "            <weightUnit>g</weightUnit>  \n" +
                "            <suggestedCNName>牙刷</suggestedCNName>  \n" +
                "            <suggestedENName>toothbrush</suggestedENName> \n" +
                "          </goods> \n" +
                "        </goodsList> \n" +
                "      </parcel>  \n" +
                "      <returnParcel> \n" +
                "        <imID>cn1518208075sucs</imID>  \n" +
                "        <name>glassware</name>  \n" +
                "        <phone>***********</phone>  \n" +
                "        <mobile>86-***********</mobile>  \n" +
                "        <email><EMAIL></email>  \n" +
                "        <undeliverableOption>2</undeliverableOption>  \n" +
                "        <zipCode>101110</zipCode>  \n" +
                "        <address> \n" +
                "          <country>CH</country>  \n" +
                "          <province>he bei sheng</province>  \n" +
                "          <city>cang zhou shi</city>  \n" +
                "          <district>xin hua qu</district>  \n" +
                "          <street>he fang jie</street>  \n" +
                "          <detailAddress>cang zhou kai fa qu~~~jia xing wu liu yuan A22-28</detailAddress> \n" +
                "        </address> \n" +
                "      </returnParcel> \n" +
                "    </consoFirstConsignPackage> \n" +
                "  </consoFirstConsignPackageList> \n" +
                "</request>\n" +
                " ";

        cainiaoService.consoFirstConsignPackage(xml, "");

    }



}