package com.sinoair.ceop.service;

import com.alibaba.druid.util.StringUtils;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.dao.*;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.EdiRecord;
import com.sinoair.ceop.domain.model.EdiRecordWithBLOBs;
import com.sinoair.ceop.domain.model.PackageParcle;
import com.sinoair.ceop.domain.vo.EawbManifestCommonVO;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.DateUtil;
import com.sinoair.core.utils.HttpUtils;
import com.sinoair.core.utils.MD5;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.client.utils.URLEncodedUtils;
import org.bouncycastle.jcajce.provider.asymmetric.rsa.RSAUtil;
import org.junit.After;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.omg.CORBA.Object;
import org.springframework.beans.factory.annotation.Autowired;

import javax.annotation.Resource;
import javax.crypto.Cipher;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.security.Key;
import java.security.KeyFactory;
import java.security.KeyPair;
import java.security.KeyPairGenerator;
import java.security.spec.PKCS8EncodedKeySpec;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * Created by WangXX4 on 2016/6/24.
 */
public class SinoairStandardInterchangeServiceTest extends CommonTestCase {
    @Resource
    SinoairStandardInterchangeService sinoairStandardInterchangeService;

    @Resource(name = "PackageCheckMapper")
    private PackageCheckMapper packageCheckMapper;

    @Resource(name = "PackageParcleMapper")
    private PackageParcleMapper packageParcleMapper;


    @Resource(name = "ExpressManifestMapper")
    private ExpressManifestMapper expressManifestMapper;

    @Resource(name = "ExpressassignmentMapper")
    private ExpressassignmentMapper expressassignmentMapper;

    @Resource(name = "EawbpreMapper")
    private EawbpreMapper eawbpreMapper;




    @Autowired
    EdiRecordMapper ediRecordMapper;


    @Before
    public void setUp() throws Exception {

    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void report() throws Exception {

    }

    @Test
    public void batch() throws Exception {

    }

    @Test
    public void updateOrder() throws Exception {

    }

    @Test
    public void logisticsDetailQuery() throws Exception {

    }

    /**
     * 用户名错误
     *
     * @throws Exception
     */

    @Test
    public void reportWrongUserName() throws Exception {
        String OrderNo = "wxxJunitTest" + new SimpleDateFormat("yyyy-MM-dd--h:m:s").format(new Date());
        System.out.println("OrderNo = " + OrderNo);
        String xml = combinateInputXml("wxx", "jolly", OrderNo, "ARMHGH15", "3228");
        System.out.println(xml);
        String actualResult = sinoairStandardInterchangeService.report(xml);
        System.out.println("actualResult = " + actualResult);
        assertEquals("<?xml version='1.0' encoding='UTF-8' ?><WSRETURN><Orders><Order><Status>N</Status><reasonCode></reasonCode><Comments>wxx未注册</Comments></Order></Orders></WSRETURN>",
                actualResult);
    }

    /**
     * 用户名错误
     *
     * @throws Exception
     */

    @Test
    public void reportWrongXMLFormat() throws Exception {
        String OrderNo = "wxxJunitTest" + new SimpleDateFormat("yyyy-MM-dd--h:m:s").format(new Date());
        System.out.println("OrderNo = " + OrderNo);
        String xml = combinateInputXml("jolly", "jolly", OrderNo, "ARMHGH15", "<>");
        System.out.println(xml);
        String actualResult = sinoairStandardInterchangeService.report(xml);
        System.out.println("actualResult = " + actualResult);
        assertEquals("<?xml version='1.0' encoding='UTF-8' ?><WSRETURN><Orders><Order><Status>N</Status><reasonCode>S12</reasonCode><Comments></Comments></Order></Orders></WSRETURN>"
                , actualResult);
    }

    /**
     * 没有开通此渠道
     *
     * @throws Exception
     */

    @Test
    public void reportWrongRoute() throws Exception {
        String OrderNo = "wxxJunitTest" + new SimpleDateFormat("yyyy-MM-dd--h:m:s").format(new Date());
        System.out.println("OrderNo = " + OrderNo);
        String xml = combinateInputXml("jolly", "jolly", OrderNo, "FRM", "3228");
        String actualResult = sinoairStandardInterchangeService.report(xml);
        System.out.println("actualResult = " + actualResult);
        assertEquals("<?xml version='1.0' encoding='UTF-8' ?><WSRETURN><Orders><Order><Status>N</Status><reasonCode></reasonCode><Comments>渠道无访问权限</Comments></Order></Orders></WSRETURN>",
                actualResult);
    }

    /**
     * 正常情况
     *
     * @throws Exception
     */

    @Test
    public void reportRouteNull() throws Exception {
        String OrderNo = "wxxJunitTest" + new SimpleDateFormat("yyyy-MM-dd--h:m:s").format(new Date());
        System.out.println("OrderNo = " + OrderNo);
        String xml = combinateInputXml("jolly", "jolly", OrderNo, "ARMHGH15", "3228");
        System.out.println(xml);
        String actualResult = sinoairStandardInterchangeService.report(xml);
        System.out.println("actualResult = " + actualResult);
        assertEquals(true, actualResult.contains(OrderNo));
        assertEquals(true, actualResult.contains("<Status>Y</Status>"));
    }

    // TODO: Simon 2016/6/24 每条线路都需要有测试用例覆盖，用老系统的输入输出作为新系统的测试用例的素材，确保新系统和老系统从外边看是一样的

    @Ignore
    public String combinateInputXml(String username, String password, String OrderNo, String agent, String receiverPost) {
        String result = "<?xml version='1.0' encoding='UTF-8' ?>\n" +
                "<WSGET>\n" +
                "    <AccessRequest>\n" +
                "        <username>" + username + "</username>\n" +
                "        <password>" + password + "</password>\n" +
                "    </AccessRequest>\n" +
                "    <Orders>\n" +
                "\t\t<Order>\n" +
                "\t\t\t<!--客户的订单号-->\n" +
                "\t\t\t<OrderNo>" + OrderNo + "</OrderNo>\n" +
                "\t\t\t<!--目的城市名-->\n" +
                "\t\t\t<desCity>sydney</desCity>\n" +
                "\t\t\t<!-- 发运渠道-->\n" +
                "\t\t\t<agent>" + agent + "</agent>\n" +
                "\t\t\t<!-- 目的国家二字码-->\n" +
                "\t\t\t<desCountry>AU</desCountry>\n" +
                "\t\t\t<!--客户编码 266-->\n" +
                "\t\t\t<customerNo>266</customerNo>\n" +
                "\t\t\t<!--口岸-->\n" +
                "\t\t\t<sac_id>HGH</sac_id>\n" +
                "\t\t\t<!--发件人 英文-->\n" +
                "\t\t\t<shipperEN>WANG CHAO</shipperEN>\n" +
                "\t\t\t<!--发件公司 英文-->\n" +
                "\t\t\t<shippingCompanyEN>ZHEJIANG JOLLY IT CO LTD</shippingCompanyEN>\n" +
                "\t\t\t<!--发件公司 中文-->\n" +
                "\t\t\t<shippingCompanyCN>浙江执御信息技术有限公司</shippingCompanyCN>\n" +
                "\t\t\t<!-- 发件人地址 英文-->\n" +
                "\t\t\t<shipperAddressEN>3F NO 1 BLDG 2 XIANGMAO RD GONGSHU DISTRICT</shipperAddressEN>\n" +
                "\t\t\t<!--始发城市 英文 -->\n" +
                "\t\t\t<depCity>HANGZHOU</depCity>\n" +
                "\t\t\t<!--始发省 英文 -->\n" +
                "\t\t\t<depState>ZHEJIANG</depState>\n" +
                "\t\t\t<!-- 发件人电话-->\n" +
                "\t\t\t<shipperPhone>13396541508</shipperPhone>\n" +
                "\t\t\t<!-- 收件公司 英文-->\n" +
                "\t\t\t<receiveCompanyEN>KELLIE PAPWORTH</receiveCompanyEN>\n" +
                "\t\t\t<!-- 收件人 英文-->\n" +
                "\t\t\t<receiverEN>KELLIE PAPWORTH</receiverEN>\n" +
                "\t\t\t<!-- 收件人地址 英文-->\n" +
                "\t\t\t<receiverAddressEN>30 COWRIE ROAD TORQUAY VICTORIA AU BELLBRAE</receiverAddressEN>\n" +
                "\t\t\t<!-- 邮编-->\n" +
                "\t\t\t<receiverPost>" + receiverPost + "</receiverPost>\n" +
                "\t\t\t<!-- 收件人电话-->\n" +
                "\t\t\t<receiverPhone>*********</receiverPhone>\n" +
                "\t\t\t<!-- 货物类型-->\n" +
                "\t\t\t<cargoType>ND</cargoType>\n" +
                "\t\t\t<!-- 件数-->\n" +
                "\t\t\t<pieces>1</pieces>\n" +
                "\t\t\t<!-- 预报实重-->\n" +
                "\t\t\t<actualWeight>1</actualWeight>\n" +
                "\t\t\t<!-- 预报体积重-->\n" +
                "\t\t\t<cubWeight>2</cubWeight>\n" +
                "\t\t\t<!-- 描述-->\n" +
                "\t\t\t<productDeclare>10*29*34</productDeclare>\n" +
                "\t\t\t<!-- 参考号-->\n" +
                "\t\t\t<referenceNO>lcm23412321</referenceNO>\n" +
                "\t\t\t<!--多品名begin-->\n" +
                "\t\t\t<Items>\n" +
                "\t\t\t\t<Item>\n" +
                "\t\t\t\t\t<!-- 品名 英文-->\n" +
                "\t\t\t\t\t<productNameEN>COAT</productNameEN>\n" +
                "\t\t\t\t\t<!-- 品名 中文-->\n" +
                "\t\t\t\t\t<productNameCN>外套</productNameCN>\n" +
                "\t\t\t\t\t<!-- 规格型号-->\n" +
                "\t\t\t\t\t<specification>0.5kg/件</specification>\n" +
                "\t\t\t\t\t<!-- 计量单位-->\n" +
                "\t\t\t\t\t<unit>件</unit>\n" +
                "\t\t\t\t\t<!-- 数量-->\n" +
                "\t\t\t\t\t<quantity>7</quantity>\n" +
                "\t\t\t\t\t<!-- HS编码-->\n" +
                "\t\t\t\t\t<HScode>6104320090</HScode>\n" +
                "\t\t\t\t\t<!-- 海关申报价值-->\n" +
                "\t\t\t\t\t<declareValue>22.27</declareValue>\n" +
                "\t\t\t\t\t<!-- 备注-->\n" +
                "\t\t\t\t\t<comments>just for test</comments>\n" +
                "\t\t\t\t</Item>\n" +
                "\t\t\t\t<Item>\n" +
                "\t\t\t\t\t<!-- 品名 英文-->\n" +
                "\t\t\t\t\t<productNameEN>shoes</productNameEN>\n" +
                "\t\t\t\t\t<!-- 品名 中文-->\n" +
                "\t\t\t\t\t<productNameCN>鞋子</productNameCN>\n" +
                "\t\t\t\t\t<!-- 规格型号-->\n" +
                "\t\t\t\t\t<specification>1kg/件</specification>\n" +
                "\t\t\t\t\t<!-- 计量单位-->\n" +
                "\t\t\t\t\t<unit>件</unit>\n" +
                "\t\t\t\t\t<!-- 数量-->\n" +
                "\t\t\t\t\t<quantity>3</quantity>\n" +
                "\t\t\t\t\t<!-- HS编码-->\n" +
                "\t\t\t\t\t<HScode>6104320091</HScode>\n" +
                "\t\t\t\t\t<!-- 海关申报价值-->\n" +
                "\t\t\t\t\t<declareValue>2323</declareValue>\n" +
                "\t\t\t\t\t<!-- 备注-->\n" +
                "\t\t\t\t\t<comments>just for test</comments>\n" +
                "\t\t\t\t</Item>\n" +
                "\t\t\t</Items>\n" +
                "\t\t\t<!--多品名end-->\n" +
                "\t\t\t\n" +
                "\t\t\t<!--暂时用不到begin-->\n" +
                "\t\t\t<desCity3C/>\n" +
                "\t\t\t<packageType/>\n" +
                "\t\t\t<isEcommerce/>\n" +
                "\t\t\t<allInOne/>\n" +
                "\t\t\t<serviceType/>\n" +
                "\t\t\t<email/>\n" +
                "\t\t\t<referenceNO/>\n" +
                "\t\t\t<pickUpProvince/>\n" +
                "\t\t\t<receiverState/>\n" +
                "\t\t\t<taxCode/>\n" +
                "\t\t\t<freightPrice/>\n" +
                "\t\t\t<airwaybill/>\n" +
                "\t\t\t<shipperAddressCN/>\n" +
                "\t\t\t<netWeight/>\n" +
                "\t\t\t<!--暂时用不到end-->\n" +
                "\t\t</Order>\n" +
                "\t</Orders>\n" +
                "</WSGET>";
        return result;
    }


    public static String decryptByPrivateKey(String encryptedData, String key) throws Exception {
        // 对密钥解密
        byte[] keyBytes = java.util.Base64.getDecoder().decode(key);
        // 取得私钥
        PKCS8EncodedKeySpec pkcs8KeySpec = new PKCS8EncodedKeySpec(keyBytes);
        KeyFactory keyFactory = KeyFactory.getInstance("RSA");
        Key privateKey = keyFactory.generatePrivate(pkcs8KeySpec);
        // 对数据解密
        Cipher cipher = Cipher.getInstance(keyFactory.getAlgorithm());
        cipher.init(Cipher.DECRYPT_MODE, privateKey);
        return new String(cipher.doFinal(Base64.getDecoder().decode(encryptedData)));
    }

    @Test
    public void testHqygXML() {

        /**try {
            String  sss=decryptByPrivateKey("Tn4IMeDMeH8jgIitEpOqgYqZYGVMA52j4h1IcU2mqoO4orm5pyErxLyCirCRwgdTEPCVJr5Op4w56SEXx3ISc3RNaoaT86eWJFfD25VtQ9IwajM9OwvlT39lUr/3hcsmyixvtE7i4i7dw8hxK9iVYe2mW7/xC8XwyUBg9YpkYco=","MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIRnd2tUNHMu0ksLHLuH1dNSX9vr8KkP8wt/LfEI4DQGDVRHqlFXt+vaFVqcDpWUWiUPBuNLXZJp055kS0urc+MJh2F7yBJfayTKJf9XfC8DlCPyVoogPLBrZG5FoOAOI6hc8jve4I8UdAOlQrkbt2jCJKAt2swo9iBhQ5KEonmdAgMBAAECgYAoFKYq4GesQo04WFnPTxRkjzgje+3zT7e0UECZCaXNOdPJIpSlYRd1FySFtF4Z27naJA1iuwrNaqm79gJxGqrOrPdxubBwejUW22bz8QvC8xRdvuQL+V/yXZYYzD/q2KyENnVpKLr0G1hXdaz+wp7+1uOImq1WBCqEeU1400PPAQJBAMUTuX0nFWdQn48Ny6ekdxwIdPWJ5PAg5xcQ9sw7KCd/wRZP34EuLdWvheZLbvls/k9tjj6E/zAHGiBQgBi7dcECQQCr/a4YsUvoHwyCPG56GhH2cg8HGXAfKrGa7hoO/uLZrhlUk0So5i2ZmuRBDpaew765jXYrY/xp2T9RJQIgllLdAkEAxGysa/kQdDD/V23Alg5uSRqEuFYAQPYKXgsicNxlYTkMElj5w59rP5f6NVbp/OCNeE0RgW69ifkKKTsS8ZOmAQJAWpg4kUcqva1FMIDWjLiYx6DS66FpnvElNn+++DEomxMKwrMEEpeUY3nkS6R8kEp5d1HdcgOr3LGTmZGN6k5kfQJBALpNsuOGD9NlS0cPa3Rx1S6j7R4bKoLngFxLbU/HECvwVVCwN/9rTO7Ws02weiOBGBpR3mTsUA6rSOHHlOkAjsY=");
            System.out.println(sss);
        }catch (Exception ex){

        }**/

        /**try {
            // 生成RSA密钥对
            KeyPairGenerator keyPairGenerator = KeyPairGenerator.getInstance("RSA");
            keyPairGenerator.initialize(1024);
            KeyPair keyPair = keyPairGenerator.generateKeyPair();

// 获取公钥和私钥
            String privateKeyBase64 = java.util.Base64.getEncoder().encodeToString(keyPair.getPrivate().getEncoded());
            String publicKeyBase64 = java.util.Base64.getEncoder().encodeToString(keyPair.getPublic().getEncoded());

            System.out.println("私密:"+privateKeyBase64);
            System.out.println("公钥:"+publicKeyBase64);

         String




        }catch (Exception ex){


        }**/


       //String  sss=decryptByPrivateKey("Tn4IMeDMeH8jgIitEpOqgYqZYGVMA52j4h1IcU2mqoO4orm5pyErxLyCirCRwgdTEPCVJr5Op4w56SEXx3ISc3RNaoaT86eWJFfD25VtQ9IwajM9OwvlT39lUr/3hcsmyixvtE7i4i7dw8hxK9iVYe2mW7/xC8XwyUBg9YpkYco=","MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIRnd2tUNHMu0ksLHLuH1dNSX9vr8KkP8wt/LfEI4DQGDVRHqlFXt+vaFVqcDpWUWiUPBuNLXZJp055kS0urc+MJh2F7yBJfayTKJf9XfC8DlCPyVoogPLBrZG5FoOAOI6hc8jve4I8UdAOlQrkbt2jCJKAt2swo9iBhQ5KEonmdAgMBAAECgYAoFKYq4GesQo04WFnPTxRkjzgje+3zT7e0UECZCaXNOdPJIpSlYRd1FySFtF4Z27naJA1iuwrNaqm79gJxGqrOrPdxubBwejUW22bz8QvC8xRdvuQL+V/yXZYYzD/q2KyENnVpKLr0G1hXdaz+wp7+1uOImq1WBCqEeU1400PPAQJBAMUTuX0nFWdQn48Ny6ekdxwIdPWJ5PAg5xcQ9sw7KCd/wRZP34EuLdWvheZLbvls/k9tjj6E/zAHGiBQgBi7dcECQQCr/a4YsUvoHwyCPG56GhH2cg8HGXAfKrGa7hoO/uLZrhlUk0So5i2ZmuRBDpaew765jXYrY/xp2T9RJQIgllLdAkEAxGysa/kQdDD/V23Alg5uSRqEuFYAQPYKXgsicNxlYTkMElj5w59rP5f6NVbp/OCNeE0RgW69ifkKKTsS8ZOmAQJAWpg4kUcqva1FMIDWjLiYx6DS66FpnvElNn+++DEomxMKwrMEEpeUY3nkS6R8kEp5d1HdcgOr3LGTmZGN6k5kfQJBALpNsuOGD9NlS0cPa3Rx1S6j7R4bKoLngFxLbU/HECvwVVCwN/9rTO7Ws02weiOBGBpR3mTsUA6rSOHHlOkAjsY=");






        String xml = "<WSGET>\n" +
                "  <AccessRequest>\n" +
                "  <username>HGH394860</username>\n" +
                "  <password>a2068afea2690db9ae489074994</password>\n" +
                "    <platform>SINOAIR</platform>\n" +
                "  </AccessRequest>\n" +
                "  <Orders>\n" +
                "    <Order>\n" +
                "      <OrderNo>31222-03110818-8493073101</OrderNo>\n" +
                "      <desCity>HAMILTON</desCity>\n" +
                "      <agent>US_PSY</agent>\n" +
                "      <servicetype>US_STD_NBT</servicetype>\n" +
                "      <cod>N</cod>\n" +
                "      <desCountry>US</desCountry>\n" +
                "      <sac_id>SNR</sac_id>\n" +
                "      <shipperEN>Stella Wong</shipperEN>\n" +
                "      <shippingCompanyEN></shippingCompanyEN>\n" +
                "      <shipperAddressEN>Gongyeyuanqubeixieyujie  Taiyangxinchenghuayuansiqi 124-102</shipperAddressEN>\n" +
                "      <pickupPostcode>215000</pickupPostcode>\n" +
                "      <pickupDistrict></pickupDistrict>\n" +
                "      <sku>贝斯琴弓</sku>\n" +
                "      <depCity>Suzhou</depCity>\n" +
                "      <depState>Jiangsu</depState>\n" +
                "      <shipperPhone>18151787175</shipperPhone>\n" +
                "      <receiveCompanyEN></receiveCompanyEN>\n" +
                "      <receiverEN>Joel and Rachel Schnackel</receiverEN>\n" +
                "      <receiverAddressEN>262 COOPER LN</receiverAddressEN>\n" +
                "      <receiverPost>02011-3325</receiverPost>\n" +
                "      <receiverPhone>******-728-4548</receiverPhone>\n" +
                "      <receiverMobile></receiverMobile>\n" +
                "      <receiverEmail></receiverEmail>\n" +
                "      <taxId></taxId>\n" +
                "      <cargoType>ND</cargoType>\n" +
                "      <declareValue>29.8</declareValue>\n" +
                "      <actualWeight>0.5</actualWeight>\n" +
                "      <pieces>1</pieces>\n" +
                "      <desState>MT</desState>\n" +
                "      <desStreet></desStreet>\n" +
                "      <desDistrict></desDistrict>\n" +
                "      <ioosNo></ioosNo>\n" +
                "      <goodsValue>29.8</goodsValue>\n" +
                "      <goodsDeclareCurrency>USD</goodsDeclareCurrency>\n" +
                "      <Items>\n" +
                "        <Item>\n" +
                "          <productNameEN>bass bow</productNameEN>\n" +
                "          <productNameCN>贝斯琴弓</productNameCN>\n" +
                "          <specification></specification>\n" +
                "          <unit>PCS</unit>\n" +
                "          <quantity>1</quantity>\n" +
                "          <HScode>9202900090</HScode>\n" +
                "          <declareValue>29.8</declareValue>\n" +
                "          <productWeight>0.500</productWeight>\n" +
                "          <sku>贝斯琴弓</sku>\n" +
                "        </Item>\n" +
                "      </Items>\n" +
                "    </Order>\n" +
                "  </Orders>\n" +
                "</WSGET>";
        String report = sinoairStandardInterchangeService.report(xml);
    }




    @Ignore
    public String testIRANPDEXML() {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?> <WSGET><AccessRequest><username>HGH1000001139</username><password>HGH1000001139I64</password><platform>SINOAIR</platform></AccessRequest><Orders><Order><agent>FRMAP</agent><servicetype>DISTRIBUTOR_FRM6A</servicetype><customers>J-NET</customers><sac_id>SNR</sac_id><shipperEN>J-NET</shipperEN><shipperAddressEN>Room T1-2301, SOHO Tianshan square, No. 421, Ziyun Road, Changning District</shipperAddressEN><depCity>Hang Zhou</depCity><depState>Zhe Jiang</depState><shipperPhone>************</shipperPhone><receiverEN>Anthony</receiverEN><receiverAddressEN>9 Place du 11 Novembre   </receiverAddressEN><receiverPost>39190</receiverPost><receiverPhone>0637314151</receiverPhone><desCity>Jura</desCity><desState>Bourgogne-Franche-Comte</desState><desCountry>FR</desCountry><OrderNo>JNTCU1000000010YQ</OrderNo><cargoType>ND</cargoType><declareValue>15.00</declareValue><actualWeight>1.588</actualWeight><pieces>1</pieces><Items><Item><productNameEN>Rc boat</productNameEN><productNameCN>遥控船</productNameCN><quantity>1</quantity><declareValue>15</declareValue><productWeight>1.588</productWeight></Item></Items></Order></Orders></WSGET>";
        return xml;
    }

    @Ignore
    public String testINDONESIA() {
        String xml = "<?xml version=\"1.0\" encoding=\"UTF-8\"?>\n" +
                "<WSGET>\n" +
                " <AccessRequest>\n" +
                "  <username>HGH1000001139</username>\n" +
                "  <password>HGH1000001139I64</password>\n" +
                "  <platform>SINOAIR段</platform> \n" +
                "</AccessRequest>\n" +
                " <Orders>\n" +
                "  <Order>\n" +
                "   <agent>UKGB</agent>\n" +
                "   <servicetype>DISTRIBUTOR_ITSTANDARD</servicetype>\n" +
                "   <customers>J-NET</customers>\n" +
                "   <sac_id>SZX</sac_id>\n" +
                "   <shipperEN>J-NET</shipperEN>\n" +
                "   <shipperAddressEN>Room T1-2301, SOHO Tianshan square, No. 421, Ziyun Road, Changning District</shipperAddressEN>\n" +
                "   <depCity>Hong Kong</depCity>\n" +
                "   <depState>Hong Kong</depState>\n" +
                "   <shipperPhone>************</shipperPhone>\n" +
                "   <receiverEN>Moses Myombi</receiverEN>\n" +
                "   <receiverAddressEN>22A Hartley Avenue</receiverAddressEN>\n" +
                "   <receiverPost>E6 1NT</receiverPost>\n" +
                "   <receiverPhone>7405180458</receiverPhone>\n" +
                "   <desCity>London</desCity>\n" +
                "   <desState>England</desState>\n" +
                "   <desCountry>United Kingdom</desCountry>\n" +
                "   <OrderNo>JNTCU2100225325YQ</OrderNo>\n" +
                "   <cargoType>ND</cargoType>\n" +
                "   <declareValue>7.00</declareValue>\n" +
                "   <actualWeight>1.830</actualWeight>\n" +
                "   <pieces>1</pieces>\n" +
                "   <Items>\n" +
                "    <Item>\n" +
                "     <productNameEN>BACKGROUND</productNameEN>\n" +
                "     <productNameCN>背景布</productNameCN>\n" +
                "     <quantity>1</quantity>\n" +
                "     <declareValue>1</declareValue>\n" +
                "     <productWeight>0.91</productWeight>\n" +
                "    </Item>\n" +
                "    <Item>\n" +
                "     <productNameEN>stand kit</productNameEN>\n" +
                "     <productNameCN>背景架</productNameCN>\n" +
                "     <quantity>1</quantity>\n" +
                "     <declareValue>6</declareValue>\n" +
                "     <productWeight>0.91</productWeight>\n" +
                "    </Item>\n" +
                "   </Items>\n" +
                "  </Order>\n" +
                " </Orders>\n" +
                "</WSGET>";

        return xml;
    }

    @Ignore
    public String testMBXxml() {
        String xml = "";

        return xml;
    }

    @Test
    public void testINA() throws Exception {
        String xml = testMBXxml();
        String report = sinoairStandardInterchangeService.report(xml);
    }

    @Test
    public void testPDE() throws Exception {
        int num=1;//测试数据的票数
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String codreplace="Y";//Y或N
        String codValuereplace="2947185";//数字或为“”
        for (int i=0;i<num;i++){
           // String OrderNo = "IRANPDE201705160001";//"IR" + date.substring(4) +  StringUtil.getIntFormString(3, i);
           // String cod=codreplace;
            //String codValue=codValuereplace;
            //String trackingNo="IRAN" + date.substring(4) +  StringUtil.getIntFormString(3, i);
            String xml = testIRANPDEXML();
            String report = sinoairStandardInterchangeService.report(xml);
        }

    }

    @Test
    public void testCharges() throws Exception {
        String xml = testIRANPDEXML();
        String report = sinoairStandardInterchangeService.report(xml);
    }

    @Test
    public void testMBX() throws Exception {
        String xml = testMBXxml();
        String report = sinoairStandardInterchangeService.report(xml);
    }

    @Test
    public void testBatch() {
        String xml = "<WSGET>\n" +
                "  <AccessRequest>\n" +
                "    <username>057132770</username>\n" +
                "    <password>SNR90974S7ANI8NZ</password>\n" +
                "    <platform>SINOAIR</platform>\n" +
                "  </AccessRequest>\n" +
                "  <Orders>\n" +
                "    <Order>\n" +
                "      <OrderNo>1231231232</OrderNo>\n" +
                "      <desCity>SOMERSET</desCity>\n" +
                "      <agent>US_</agent>\n" +
                "      <servicetype>DISTRIBUTOR_US_SNX</servicetype>\n" +
                "      <cod>N</cod>\n" +
                "      <desCountry>US</desCountry>\n" +
                "      <sac_id>SNR</sac_id>\n" +
                "      <shipperEN>JACK</shipperEN>\n" +
                "      <shippingCompanyEN></shippingCompanyEN>\n" +
                "      <shipperAddressEN>萧山区左右世界</shipperAddressEN>\n" +
                "      <pickupPostcode></pickupPostcode>\n" +
                "      <pickupDistrict></pickupDistrict>\n" +
                "      <sku>保龄球</sku>\n" +
                "      <depCity>HANGZHOU</depCity>\n" +
                "      <depState>浙江省</depState>\n" +
                "      <shipperPhone></shipperPhone>\n" +
                "      <receiveCompanyEN></receiveCompanyEN>\n" +
                "      <receiverEN>Jen Ohler</receiverEN>\n" +
                "      <receiverAddressEN>1476 N CENTER AVE SOMERSET, PA</receiverAddressEN>\n" +
                "      <receiverPost>15501-1632</receiverPost>\n" +
                "      <receiverPhone>******-671-6610</receiverPhone>\n" +
                "      <receiverMobile></receiverMobile>\n" +
                "      <receiverEmail></receiverEmail>\n" +
                "      <taxId></taxId>\n" +
                "      <cargoType>ND</cargoType>\n" +
                "      <declareValue>6.0</declareValue>\n" +
                "      <actualWeight>1.500</actualWeight>\n" +
                "      <pieces>1</pieces>\n" +
                "      <desState>PA</desState>\n" +
                "      <desStreet></desStreet>\n" +
                "      <desDistrict></desDistrict>\n" +
                "      <ioosNo></ioosNo>\n" +
                "      <goodsValue>6.0</goodsValue>\n" +
                "      <goodsDeclareCurrency>USD</goodsDeclareCurrency>\n" +
                "      <Items>\n" +
                "        <Item>\n" +
                "          <productNameEN>bowling</productNameEN>\n" +
                "          <productNameCN>保龄球</productNameCN>\n" +
                "          <specification></specification>\n" +
                "          <unit>PCS</unit>\n" +
                "          <quantity>1</quantity>\n" +
                "          <HScode></HScode>\n" +
                "          <declareValue>6</declareValue>\n" +
                "          <productWeight>1.500</productWeight>\n" +
                "          <productURL></productURL>\n" +
                "          <sku>保龄球</sku>\n" +
                "        </Item>\n" +
                "      </Items>\n" +
                "    </Order>\n" +
                "  </Orders>\n" +
                "</WSGET>";
        String report = sinoairStandardInterchangeService.report(xml);

    }


    @Test
    public void insertPP() {
        PackageParcle packageParcle = new PackageParcle();
        String PcSyscode = packageCheckMapper.selectPcSyscode();
        packageParcle.setPpSyscode(Long.parseLong(PcSyscode));
        packageParcle.setPkgPrintcode("bbbbbbbbbbbbbbb");
        // packageParcle.setServicetype(_eawbpre.getEawbServicetype());
        packageParcle.setPpGrossweight(new BigDecimal(11));
        packageParcle.setPpNetweight(new BigDecimal(11));//目前将实际重量放到了袋子的重量字段
        packageParcle.setPpParcelqty(Integer.valueOf(11));
        packageParcle.setPpLogisticsordercode("22222222222");
        packageParcle.setPpTrackingnumber("33333333333");
        packageParcle.setPpCreatetime(DateUtil.getNowDateTime());
        packageParcle.setPpRemark("接收wish组包关系");
        packageParcle.setPpWishType("1");
        packageParcleMapper.insertSelective(packageParcle);
    }

    @Test
    public void testYunlu() {
        /**String xml="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010730991</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>Dorene Johnson</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>3825 Wichita St</receiverAddressEN><receiverPost>76384</receiverPost><receiverPhone>9406054969</receiverPhone><receiverMobile>9406054969</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Vernon</desCity><desState>TX</desState><desStreet>3825 Wichita St</desStreet><declareValue>36.59</declareValue><actualWeight>2.288</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>36.59</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士两件套</productNameCN><productNameEN>Ladies Two Piece Sets</productNameEN><declareValue>11.70</declareValue><quantity>3</quantity><Hscode>6204130090</Hscode><productWeight>0.010</productWeight><sku>50001054672114619233</sku></Item><Item><productNameCN>女士项链</productNameCN><productNameEN>Ladies Necklaces</productNameEN><declareValue>1.49</declareValue><quantity>1</quantity><Hscode>7117190000</Hscode><productWeight>0.010</productWeight><sku>50001108481992750023</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report = sinoairStandardInterchangeService.report(xml);

        String xml1="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010341961</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>keeli smith</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>64 Hunters Overlook Dr</receiverAddressEN><receiverPost>30276</receiverPost><receiverPhone>(*************</receiverPhone><receiverMobile>(*************</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Senoia</desCity><desState>GA</desState><desStreet>64 Hunters Overlook Dr</desStreet><declareValue>71.67</declareValue><actualWeight>2.971</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>71.67</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士帽衫和针织衫</productNameCN><productNameEN>Ladies Hoodies and Sweatshirts</productNameEN><declareValue>8.69</declareValue><quantity>3</quantity><Hscode>6103310000</Hscode><productWeight>0.010</productWeight><sku>50001026412035408638</sku></Item><Item><productNameCN>女士牛仔裤</productNameCN><productNameEN>Ladies Jeans</productNameEN><declareValue>12.30</declareValue><quantity>3</quantity><Hscode>6204630000</Hscode><productWeight>0.010</productWeight><sku>50001054672039017572</sku></Item><Item><productNameCN>女士衬衫和女衫</productNameCN><productNameEN>Ladies Shirts and Tops</productNameEN><declareValue>7.20</declareValue><quantity>1</quantity><Hscode>6206300010</Hscode><productWeight>0.010</productWeight><sku>50001098852038812141</sku></Item><Item><productNameCN>女士项链</productNameCN><productNameEN>Ladies Necklaces</productNameEN><declareValue>1.50</declareValue><quantity>1</quantity><Hscode>7117190000</Hscode><productWeight>0.010</productWeight><sku>50001108481992750023</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report1 = sinoairStandardInterchangeService.report(xml1);

        String xml2="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN22120531905</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>CHONDA CUMMINGS</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>5433 Mulberry Drive</receiverAddressEN><receiverPost>71112</receiverPost><receiverPhone>0000000000</receiverPhone><receiverMobile>0000000000</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Bossier City</desCity><desState>LA</desState><desStreet>5433 Mulberry Drive</desStreet><declareValue>59.38</declareValue><actualWeight>3.711</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>59.38</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士两件套</productNameCN><productNameEN>Ladies Two Piece Sets</productNameEN><declareValue>11.70</declareValue><quantity>4</quantity><Hscode>6204130090</Hscode><productWeight>0.010</productWeight><sku>50001009612068147497</sku></Item><Item><productNameCN>女士帽衫和针织衫</productNameCN><productNameEN>Ladies Hoodies and Sweatshirts</productNameEN><declareValue>11.08</declareValue><quantity>1</quantity><Hscode>6103310000</Hscode><productWeight>0.010</productWeight><sku>50001054672064362443</sku></Item><Item><productNameCN>女士项链</productNameCN><productNameEN>Ladies Necklaces</productNameEN><declareValue>1.50</declareValue><quantity>1</quantity><Hscode>7117190000</Hscode><productWeight>0.010</productWeight><sku>50001108481992750023</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report2 = sinoairStandardInterchangeService.report(xml2);


        String xml3="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010230701</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>David Hobday</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>1033 se rummel st</receiverAddressEN><receiverPost>97128</receiverPost><receiverPhone>4432659268</receiverPhone><receiverMobile>4432659268</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>McMinnville</desCity><desState>OR</desState><desStreet>1033 se rummel st</desStreet><declareValue>67.18</declareValue><actualWeight>2.415</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>67.18</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士裤子</productNameCN><productNameEN>Ladies Pants</productNameEN><declareValue>11.08</declareValue><quantity>1</quantity><Hscode>6204630000</Hscode><productWeight>0.010</productWeight><sku>50001054672057966736</sku></Item><Item><productNameCN>女士连衣裙</productNameCN><productNameEN>Ladies Dresses</productNameEN><declareValue>8.10</declareValue><quantity>2</quantity><Hscode>6104420000</Hscode><productWeight>0.010</productWeight><sku>50001056302004721244</sku></Item><Item><productNameCN>女士裙装</productNameCN><productNameEN>Ladies Skirts</productNameEN><declareValue>9.90</declareValue><quantity>2</quantity><Hscode>4203100090</Hscode><productWeight>0.010</productWeight><sku>50001059852087672506</sku></Item><Item><productNameCN>女士居家服</productNameCN><productNameEN>Ladies Sleep and Lounge</productNameEN><declareValue>11.70</declareValue><quantity>1</quantity><Hscode>6108920090</Hscode><productWeight>0.010</productWeight><sku>50001098042094350013</sku></Item><Item><productNameCN>女士塑身衣</productNameCN><productNameEN>Ladies Shapewear</productNameEN><declareValue>8.40</declareValue><quantity>1</quantity><Hscode>6108920010</Hscode><productWeight>0.010</productWeight><sku>50001107292107357253</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report3 = sinoairStandardInterchangeService.report(xml3);


        /**String xml4="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN22123132002</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>Angelo Papa</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>2143 Forest Lakes Blvd</receiverAddressEN><receiverPost>29414</receiverPost><receiverPhone>0000000000</receiverPhone><receiverMobile>0000000000</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Charleston</desCity><desState>SC</desState><desStreet>2143 Forest Lakes Blvd</desStreet><declareValue>38.98</declareValue><actualWeight>2.770</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>38.98</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士毛衣</productNameCN><productNameEN>Ladies Sweaters</productNameEN><declareValue>7.50</declareValue><quantity>5</quantity><Hscode>6103310000</Hscode><productWeight>0.010</productWeight><sku>50001054671754635013</sku></Item><Item><productNameCN>女士项链</productNameCN><productNameEN>Ladies Necklaces</productNameEN><declareValue>1.48</declareValue><quantity>1</quantity><Hscode>7117190000</Hscode><productWeight>0.010</productWeight><sku>50001108481992750023</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report4 = sinoairStandardInterchangeService.report(xml4);

        String xml5="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010231163</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>Joy Eze</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>3211 deeds rd</receiverAddressEN><receiverPost>77084</receiverPost><receiverPhone>0000000000</receiverPhone><receiverMobile>0000000000</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Houston</desCity><desState>TX</desState><desStreet>3211 deeds rd</desStreet><declareValue>46.80</declareValue><actualWeight>2.535</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>46.8</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士两件套</productNameCN><productNameEN>Ladies Two Piece Sets</productNameEN><declareValue>11.70</declareValue><quantity>4</quantity><Hscode>6204130090</Hscode><productWeight>0.010</productWeight><sku>50001055312139497789</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report5 = sinoairStandardInterchangeService.report(xml5);

        String xml6="";
        String report6 = sinoairStandardInterchangeService.report(xml6);

       String xml7="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010731279</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>Toshia Johnson</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>1131 Pikes Peak Drive</receiverAddressEN><receiverPost>79110</receiverPost><receiverPhone>0000000000</receiverPhone><receiverMobile>0000000000</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Amarillo</desCity><desState>TX</desState><desStreet>1131 Pikes Peak Drive</desStreet><declareValue>58.50</declareValue><actualWeight>2.514</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>58.5</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士牛仔裤</productNameCN><productNameEN>Ladies Jeans</productNameEN><declareValue>12.00</declareValue><quantity>3</quantity><Hscode>6204630000</Hscode><productWeight>0.010</productWeight><sku>50001054672032010296</sku></Item><Item><productNameCN>女士大衣和外套</productNameCN><productNameEN>Ladies Coats and Jackets</productNameEN><declareValue>10.80</declareValue><quantity>1</quantity><Hscode>6103320000</Hscode><productWeight>0.010</productWeight><sku>50001054672039953498</sku></Item><Item><productNameCN>女士连衣裙</productNameCN><productNameEN>Ladies Dresses</productNameEN><declareValue>10.20</declareValue><quantity>1</quantity><Hscode>6104420000</Hscode><productWeight>0.010</productWeight><sku>50001071301857202688</sku></Item><Item><productNameCN>女士项链</productNameCN><productNameEN>Ladies Necklaces</productNameEN><declareValue>1.50</declareValue><quantity>1</quantity><Hscode>7117190000</Hscode><productWeight>0.010</productWeight><sku>50001108481992750023</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report7 = sinoairStandardInterchangeService.report(xml7);

        String xml8="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010630127</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>Carter Whalen</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>685 SPRING STREET # 198</receiverAddressEN><receiverPost>98250</receiverPost><receiverPhone>3603783846</receiverPhone><receiverMobile>3603783846</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Friday Harbor</desCity><desState>WA</desState><desStreet>685 SPRING STREET # 198</desStreet><declareValue>49.19</declareValue><actualWeight>2.370</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>49.19</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士牛仔裤</productNameCN><productNameEN>Ladies Jeans</productNameEN><declareValue>11.70</declareValue><quantity>4</quantity><Hscode>6204630000</Hscode><productWeight>0.010</productWeight><sku>50001054671893712033</sku></Item><Item><productNameCN>女士袜子及袜类</productNameCN><productNameEN>Ladies Socks and Hosiery</productNameEN><declareValue>2.39</declareValue><quantity>1</quantity><Hscode>6115960000</Hscode><productWeight>0.010</productWeight><sku>50001120112083369901</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report8 = sinoairStandardInterchangeService.report(xml8);

        String xml9="<?xml version=\"1.0\" encoding=\"UTF-8\"?><WSGET><Orders><Order><OrderNo>BCN23010441623</OrderNo><agent>US_SNX</agent><servicetype>DISTRIBUTOR_US_SNX</servicetype><sac_id>SNR</sac_id><cargoType>ND</cargoType><shipperEN>Renyongjian</shipperEN><shippingCompanyEN>ruzhe</shippingCompanyEN><shipperAddressEN>No.39   lanhezhen,renmingongyequ</shipperAddressEN><depCity>GuangZhou</depCity><depState>GuangDong</depState><shipperPhone>13926430218</shipperPhone><pickupPostcode>511468</pickupPostcode><desCountry>US</desCountry><receiverEN>Brooke Schwausch</receiverEN><receiveCompanyEN></receiveCompanyEN><receiverAddressEN>20339 Towering Cypress Dr</receiverAddressEN><receiverPost>77433</receiverPost><receiverPhone>8329644819</receiverPhone><receiverMobile>8329644819</receiverMobile><receiverEmail><EMAIL></receiverEmail><desDistrict></desDistrict><desCity>Cypress</desCity><desState>TX</desState><desStreet>20339 Towering Cypress Dr</desStreet><declareValue>48.29</declareValue><actualWeight>2.252</actualWeight><length>1.000</length><width>1.000</width><height>1.000</height><pieces>1</pieces><ioosNo></ioosNo><goodsValue>48.29</goodsValue><goodsDeclareCurrency>USD</goodsDeclareCurrency><Items><Item><productNameCN>女士牛仔裤</productNameCN><productNameEN>Ladies Jeans</productNameEN><declareValue>11.70</declareValue><quantity>4</quantity><Hscode>6204630000</Hscode><productWeight>0.010</productWeight><sku>50001054671893712033</sku></Item><Item><productNameCN>女士项链</productNameCN><productNameEN>Ladies Necklaces</productNameEN><declareValue>1.49</declareValue><quantity>1</quantity><Hscode>7117190000</Hscode><productWeight>0.010</productWeight><sku>50001108481992750023</sku></Item></Items></Order></Orders><AccessRequest><username>ZSH394460</username><password></password><platform></platform></AccessRequest></WSGET>";
        String report9 = sinoairStandardInterchangeService.report(xml9);**/
        String xml = "<?xml version=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<WSGET>\n" +
                "    <AccessRequest>\n" +
                "        <username>HGH168292</username>\n" +
                "        <platform>SINOAIR</platform>\n" +
                "    </AccessRequest>\n" +
                "    <Orders>\n" +
                "        <Order>\n" +
                "            <OrderNo>YYGKR8200003053YQ</OrderNo>\n" +
                "            <desCity>경기도 고양시 일산서구 덕이로172번길 4-43 수펠리스201호 ( 덕이동 )</desCity>\n" +
                "            <desState>경기도 고양시 일산서구 덕이로172번길 4-43 수펠리스201호 ( 덕이동 )</desState>\n" +
                "            <desStreet>경기도 고양시 일산서구 덕이로172번길 4-43 수펠리스201호 ( 덕이동 )</desStreet>\n" +
                "            <agent>KOREA_SEA</agent>\n" +
                "            <servicetype>DISTRIBUTOR_KOREA_SEA</servicetype>\n" +
                "            <desCountry>KR</desCountry>\n" +
                "            <pickupDistrict>Longgang</pickupDistrict>\n" +
                "            <sac_id>SNR</sac_id>\n" +
                "            <shipperEN>Liu Bin</shipperEN>\n" +
                "            <shipperAddressEN>2016 Xuegang Road</shipperAddressEN>\n" +
                "            <depCity>Shenzhen</depCity>\n" +
                "            <depState>Guangdong</depState>\n" +
                "            <desDistrict>경기도 고양시 일산서구 덕이로172번길 4-43 수펠리스201호 ( 덕이동 )</desDistrict>\n" +
                "            <pickupPostcode>518129</pickupPostcode>\n" +
                "            <shipperPhone>13311825195</shipperPhone>\n" +
                "            <receiverMobile>01099914978</receiverMobile>\n" +
                "            <receiverEmail />\n" +
                "            <receiverEN>이석훈</receiverEN>\n" +
                "            <receiverAddressEN>경기도 고양시 일산서구 덕이로172번길 4-43 수펠리스201호 ( 덕이동 )</receiverAddressEN>\n" +
                "            <receiverPost>10228</receiverPost>\n" +
                "            <receiverPhone>01099914978</receiverPhone>\n" +
                "            <cargoType>ND</cargoType>\n" +
                "            <declareValue>40</declareValue>\n" +
                "            <actualWeight>0.06</actualWeight>\n" +
                "            <length>10</length>\n" +
                "            <width>10</width>\n" +
                "            <height>10</height>\n" +
                "            <pieces>1</pieces>\n" +
                "            <undeliveryOption>1</undeliveryOption>\n" +
                "            <goodsValue>40</goodsValue>\n" +
                "            <goodsDeclareCurrency>USD</goodsDeclareCurrency>\n" +
                "            <ioosNo>P801160059367</ioosNo>\n" +
                "            <taxId>P801160059367</taxId>\n" +
                "            <Items>\n" +
                "                <Item>\n" +
                "                    <productNameEN>glasses*2</productNameEN>\n" +
                "                    <productNameCN>眼镜</productNameCN>\n" +
                "                    <quantity>2</quantity>\n" +
                "                    <HScode>12345678</HScode>\n" +
                "                    <declareValue>20</declareValue>\n" +
                "                    <productWeight>0.06</productWeight>\n" +
                "                </Item>\n" +
                "            </Items>\n" +
                "        </Order>\n" +
                "    </Orders>\n" +
                "</WSGET>" ;
        String report = sinoairStandardInterchangeService.report(xml);
        System.out.println("report====" + report);

    }


    @Test
    public void testUploadTrace() {
        String xml = "<?xml vxersion=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<WSGET>\n" +
                "\t<AccessRequest>\n" +
                "\t\t<username>ZSH373812</username>\n" +
                "\t\t<password>ZSH373812X31EM13</password>\n" +
                "\t\t<platform>DXM</platform>\n" +
                "\t</AccessRequest>\n" +
                "\t<Orders>\n" +
                "\t\t<Order>\n" +
                "\t\t\t<OrderNo>C1672456591028798421111</OrderNo>\n" +
                "\t\t\t<desCity>Einbeck</desCity>\n" +
                "\t\t\t<agent>US_PSY</agent>\n" +
                "\t\t\t<servicetype>DISTRIBUTOR_US_PSY</servicetype>\n" +
                "\t\t\t<desCountry>DE</desCountry>\n" +
                "\t\t\t<sac_id>SNR</sac_id>\n" +
                "\t\t\t<trackingNo></trackingNo>\n" +
                "\t\t\t<shippingCompanyEN></shippingCompanyEN>\n" +
                "\t\t\t<shippingCompanyCN></shippingCompanyCN>\n" +
                "\t\t\t<depState></depState>\n" +
                "\t\t\t<receiveCompanyEN></receiveCompanyEN>\n" +
                "\t\t\t<shipperEN>WANG CHAO</shipperEN>\n" +
                "\t\t\t<shipperAddressEN>3F NO 1 BLDG 2 XIANGMAO RD GONGSHU DISTRICT</shipperAddressEN>\n" +
                "\t\t\t<depCity>ShangHai</depCity>\n" +
                "\t\t\t<shipperPhone>1339654100</shipperPhone>\n" +
                "\t\t\t<packageSalesAmount>4.0250</packageSalesAmount>\n" +
                "\t\t\t<receiverEN>Kasim Muhsin</receiverEN>\n" +
                "\t\t\t<receiverAddressEN>Bach Str 25</receiverAddressEN>\n" +
                "\t\t\t<receiverPost>37574</receiverPost>\n" +
                "\t\t\t<receiverPhone>0157 52797794</receiverPhone>\n" +
                "\t\t\t<receiverEmail><EMAIL></receiverEmail>\n" +
                "\t\t\t<cargoType>ND</cargoType>\n" +
                "\t\t\t<declareValue>4.0250</declareValue>\n" +
                "\t\t\t<actualWeight>0.026</actualWeight>\n" +
                "\t\t\t<pieces>1</pieces>\n" +
                "\t\t\t<unit>件</unit>\n" +
                "\t\t\t<desState>Niedersachsen</desState>\n" +
                "\t\t\t<desStreet></desStreet>\n" +
                "\t\t\t<goodsValue>4.0250</goodsValue>\n" +
                "\t\t\t<goodsDeclareCurrency>USD</goodsDeclareCurrency>\n" +
                "\t\t\t<Items>\n" +
                "\t\t\t\t<Item>\n" +
                "\t\t\t\t\t<productNameEN>Charm Bracelets</productNameEN>\n" +
                "\t\t\t\t\t<productNameCN>魅力手链</productNameCN>\n" +
                "\t\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t\t<declareValue>4.0250</declareValue>\n" +
                "\t\t\t\t\t<productWeight>0.026</productWeight>\n" +
                "\t\t\t\t</Item>\n" +
                "\t\t\t</Items>\n" +
                "\t\t\t<ioosNo>IM2500000295</ioosNo>\n" +
                "\t\t</Order>\n" +
                "\t</Orders>\n" +
                "</WSGET>";
        String report = sinoairStandardInterchangeService.report(xml);
        System.out.println("report====" + report);
    }




    @Test
    public void testAoPengReport() {
        /**String appSecret="269e3ea277cfb5173534ff6a849a123a8d7f4625";
        String clientId="c85f54f60a078ea41e68c2971b00455c";
        String targetClientId="218ddd7a02e04ef875ea4d006fc4e1b4";
        long timestamp= System.currentTimeMillis() / 1000;;
        Long requestTime = System.currentTimeMillis();
        Eawbpre e =new Eawbpre();
        e.setEawbSoCode("HGH394860");
        List<Eawbpre> list=eawbpreMapper.selectEawbPreDetail(e);

        for (Eawbpre eawbpre:list) {

            String packageSn = eawbpre.getEawbReference2();
            String waybillSn = eawbpre.getEawbPrintcode();
            String tailWaybillSn =eawbpre.getEawbReference1();
            String tailWaybillUrl = eawbpre.getEawbServicerequirement();

            String requestXX = appSecret + "client_id" + clientId + "data[{\"packageSn\":\"" + packageSn + "\",\"waybillSn\":\"" + waybillSn + "\",\"tailWaybillSn\":\"" + tailWaybillSn + "\",\"tailServiceName\":\"USPS\",\"operateTime\":" + requestTime + ",\"tailWaybillUrl\":\"" + tailWaybillUrl + "\",\"tailWaybillUrlType\":\"pdf\"}]data_typeJSONtarget_client_id" + targetClientId + "timestamp" + timestamp + "typebg.logistics.lastmile.trackno.upload" + appSecret;
            String sign = DigestUtils.md5Hex(requestXX).toUpperCase();
            String json = "{\n" +
                    " \"type\": \"bg.logistics.lastmile.trackno.upload\",\n" +
                    " \"client_id\": \"" + clientId + "\",\n" +
                    " \"timestamp\": \"" + timestamp + "\",\n" +
                    " \"target_client_id\": \"" + targetClientId + "\",\n" +
                    " \"data_type\": \"JSON\",\n" +
                    " \"sign\": \"" + sign + "\",\n" +
                    " \"data\": [\n" +
                    " {\n" +
                    " \"packageSn\": \"" + packageSn + "\",\n" +
                    " \"waybillSn\": \"" + waybillSn + "\",\n" +
                    " \"tailWaybillSn\": \"" + tailWaybillSn + "\",\n" +
                    " \"tailServiceName\": \"USPS\",\n" +
                    " \"operateTime\": " + requestTime + ",\n" +
                    " \"tailWaybillUrl\": \"" + tailWaybillUrl + "\",\n" +
                    " \"tailWaybillUrlType\": \"pdf\"\n" +
                    " }\n" +
                    " ]\n" +
                    "}";

            String url = "https://kj-openapi.htjdemo.com/ark/router";
            try {
                String responseResult = HttpUtils.doPost("https://kj-openapi.pinduoduo.com/ark/router", json, 5000, 5000);
                System.out.println("responseResult====" + responseResult);
                JSONObject responseJson = JSONObject.parseObject(responseResult);
                if ("true".equals(responseJson.get("success").toString())) {
                    Long responseTime = System.currentTimeMillis();
                    EdiRecordWithBLOBs ediRecord = new EdiRecordWithBLOBs();
                    ediRecord.setCreateTime(new Date());
                    ediRecord.setForeignCode(packageSn);
                    ediRecord.setRequestBody(JSON.toJSONString(json));
                    ediRecord.setInterfaceName("US_SNX");
                    ediRecord.setPartner("US_SNX");
                    ediRecord.setForeignType("US_SNX");
                    ediRecord.setRequestTime(new Date());
                    ediRecord.setUrl(url);
                    ediRecord.setResponseBody(responseResult);
                    ediRecord.setStatus("ok");
                    Long costTime = responseTime - requestTime;
                    ediRecord.setTimeCost(costTime.intValue());
                    ediRecordMapper.insertSelective(ediRecord);
                } else {
                    Long responseTime = System.currentTimeMillis();
                    EdiRecordWithBLOBs ediRecord = new EdiRecordWithBLOBs();
                    ediRecord.setCreateTime(new Date());
                    ediRecord.setForeignCode(packageSn);
                    ediRecord.setRequestBody(JSON.toJSONString(json));
                    ediRecord.setInterfaceName("US_SNX");
                    ediRecord.setPartner("US_SNX");
                    ediRecord.setForeignType("US_SNX");
                    ediRecord.setRequestTime(new Date());
                    ediRecord.setUrl(url);
                    ediRecord.setResponseBody(responseResult);
                    ediRecord.setStatus("fail");
                    Long costTime = responseTime - requestTime;
                    ediRecord.setTimeCost(costTime.intValue());
                    ediRecordMapper.insertSelective(ediRecord);
                }

            } catch (Exception ex) {
                Long responseTime = System.currentTimeMillis();
                EdiRecordWithBLOBs ediRecord = new EdiRecordWithBLOBs();
                ediRecord.setCreateTime(new Date());
                ediRecord.setForeignCode(packageSn);
                ediRecord.setRequestBody(JSON.toJSONString(json));
                ediRecord.setInterfaceName("US_SNX");
                ediRecord.setPartner("US_SNX");
                ediRecord.setForeignType("US_SNX");
                ediRecord.setRequestTime(new Date());
                ediRecord.setUrl(url);
                ediRecord.setResponseBody(ex.getMessage());
                ediRecord.setStatus("fail");
                Long costTime = responseTime - requestTime;
                ediRecord.setTimeCost(costTime.intValue());
                ediRecordMapper.insertSelective(ediRecord);
            }
        }**/




        //String ss=mapObject.toString();



















        String xml = "<?xml vxersion=\"1.0\" encoding=\"utf-8\"?>\n" +
                "<WSGET>\n" +
                "\t<AccessRequest>\n" +
                "\t\t<username>ZSH373812</username>\n" +
                "\t\t<password>ZSH373812X31EM13</password>\n" +
                "\t\t<platform>DXM</platform>\n" +
                "\t</AccessRequest>\n" +
                "\t<Orders>\n" +
                "\t\t<Order>\n" +
                "\t\t\t<OrderNo>C1672456591028798421111</OrderNo>\n" +
                "\t\t\t<desCity>Einbeck</desCity>\n" +
                "\t\t\t<agent>US_PSY</agent>\n" +
                "\t\t\t<servicetype>DISTRIBUTOR_US_PSY</servicetype>\n" +
                "\t\t\t<desCountry>DE</desCountry>\n" +
                "\t\t\t<sac_id>SNR</sac_id>\n" +
                "\t\t\t<trackingNo></trackingNo>\n" +
                "\t\t\t<shippingCompanyEN></shippingCompanyEN>\n" +
                "\t\t\t<shippingCompanyCN></shippingCompanyCN>\n" +
                "\t\t\t<depState></depState>\n" +
                "\t\t\t<receiveCompanyEN></receiveCompanyEN>\n" +
                "\t\t\t<shipperEN>WANG CHAO</shipperEN>\n" +
                "\t\t\t<shipperAddressEN>3F NO 1 BLDG 2 XIANGMAO RD GONGSHU DISTRICT</shipperAddressEN>\n" +
                "\t\t\t<depCity>ShangHai</depCity>\n" +
                "\t\t\t<shipperPhone>1339654100</shipperPhone>\n" +
                "\t\t\t<packageSalesAmount>4.0250</packageSalesAmount>\n" +
                "\t\t\t<receiverEN>Kasim Muhsin</receiverEN>\n" +
                "\t\t\t<receiverAddressEN>Bach Str 25</receiverAddressEN>\n" +
                "\t\t\t<receiverPost>37574</receiverPost>\n" +
                "\t\t\t<receiverPhone>0157 52797794</receiverPhone>\n" +
                "\t\t\t<receiverEmail><EMAIL></receiverEmail>\n" +
                "\t\t\t<cargoType>ND</cargoType>\n" +
                "\t\t\t<declareValue>4.0250</declareValue>\n" +
                "\t\t\t<actualWeight>0.026</actualWeight>\n" +
                "\t\t\t<pieces>1</pieces>\n" +
                "\t\t\t<unit>件</unit>\n" +
                "\t\t\t<desState>Niedersachsen</desState>\n" +
                "\t\t\t<desStreet></desStreet>\n" +
                "\t\t\t<goodsValue>4.0250</goodsValue>\n" +
                "\t\t\t<goodsDeclareCurrency>USD</goodsDeclareCurrency>\n" +
                "\t\t\t<Items>\n" +
                "\t\t\t\t<Item>\n" +
                "\t\t\t\t\t<productNameEN>Charm Bracelets</productNameEN>\n" +
                "\t\t\t\t\t<productNameCN>魅力手链</productNameCN>\n" +
                "\t\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t\t<declareValue>4.0250</declareValue>\n" +
                "\t\t\t\t\t<productWeight>0.026</productWeight>\n" +
                "\t\t\t\t</Item>\n" +
                "\t\t\t</Items>\n" +
                "\t\t\t<ioosNo>IM2500000295</ioosNo>\n" +
                "\t\t</Order>\n" +
                "\t</Orders>\n" +
                "</WSGET>";
        String report = sinoairStandardInterchangeService.report(xml);
        System.out.println("report====" + report);
    }




    @Test
    public void testAoPengBatch() {
        String xml = "<?xml version='1.0' encoding='UTF-8' ?>\n" +
                "      <WSGET>\n" +
                "\t<AccessRequest>\n" +
                "    <username>aopeng</username>\n" +
                "    <password>aopeng123</password>\n" +
                "\t</AccessRequest>\n" +
                "\t<Orders>\n" +
                "\t\t<Order>\n" +
                "\t\t\t<!--客户订单号-->\n" +
                "      <OrderNo>BG-014950047E-2-T009</OrderNo>\n" +
                "\t\t\t<!--追踪号-->\n" +
                "\t\t\t<trackingNo>4041812837054985</trackingNo>\n" +
                "\t\t\t<!--预报实重,单位kg，保留三位小数-->\n" +
                "\t\t\t<actualWeight>1</actualWeight>\n" +
                "\t\t\t<!--预报体积重-->\n" +
                "\t\t\t<cubWeight>2</cubWeight>\n" +
                "\t\t\t<!--大包号-->\n" +
                "\t\t\t<pkgCode>B201812170006</pkgCode>\n" +
                "\t\t\t<!--大包号重量，单位kg，保留三位小数-->\n" +
                "\t\t\t<pkgWeight>10.8</pkgWeight>\n" +
                "\t\t\t<!--大包内装件数-->\n" +
                "\t\t\t<pkgOrdersAmount>1</pkgOrdersAmount>\n" +
                "\t\t</Order>\n" +
                "\t</Orders>\n" +
                "      </WSGET>";
        String batch = sinoairStandardInterchangeService.batch(xml);
    }
}
