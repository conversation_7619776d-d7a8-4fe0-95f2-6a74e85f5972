package com.sinoair.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.dao.ExpressairwaybillMapper;
import com.sinoair.ceop.dao.ExpressitemMapper;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.domain.model.Expressitem;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Base64;
import java.util.List;

/**
 * <AUTHOR> 2023-08-07 15:40
 */
public class ShanDongPostUtilsTest extends CommonTestCase {

    private static Logger LOGGER = LoggerFactory.getLogger(ShanDongPostUtilsTest.class);

    String uploadurl = PropertiesUtil.getString("/comm.properties", "sinoair_file_wb_ecdown_url_key");

    String downloadUrl = PropertiesUtil.getString("/comm.properties", "sinoair_file_wb_ec_download_url_kdy");

    @Autowired
    private ShanDongPostUtils shanDongPostUtils;

    @Autowired
    private EawbpreMapper eawbpreMapper;

    @Autowired
    private ExpressairwaybillMapper expressairwaybillMapper;

    @Autowired
    private ExpressitemMapper expressitemMapper;

    @Test
    public void test() throws Exception {
        /*Eawbpre eawbpre = this.eawbpreMapper.queryEawbpreByEawbPrintCode("SE108755270631");
        List<Expressitem> expressitemList = this.expressitemMapper.selectExpressitemsByEawbSyscode(eawbpre.getEawbSyscode());
        LOGGER.info("查询到的值：" + JSONObject.toJSONString(expressitemList));
        String resStr = this.shanDongPostUtils.attainMailnum(eawbpre, expressitemList);
        LOGGER.info("响应结果:" + resStr);
        try {
            //1、获取尾程单号
            String attainPreRes = shanDongPostUtils.attainMailnum(eawbpre, expressitemList);
            if (null == attainPreRes) return "Terminal non-response";
            String[] splitTrackNumber = attainPreRes.split("<mailnum>");
            if (splitTrackNumber.length < 2) {
                String errorMessage = "";
                String[] errorMessageArray = attainPreRes.split("<description>");
                if (errorMessageArray.length >= 2) {
                    String[] errorMessageStr = errorMessageArray[1].split("</description>");
                    errorMessage = errorMessageStr[0];
                }
                return (StringUtil.isEmpty(errorMessage)) ? "Failed to request ShanDong post order interface to obtain the order number!" : errorMessage;
            }
            String[] splitTrackNumber1 = splitTrackNumber[1].split("</mailnum>");
            String trackNumber = splitTrackNumber1[0];
            if (StringUtil.isEmpty(trackNumber)) {
                return "Shandong Post did not return the tracking number";
            }
            eawbpre.setEawbCarrierNo(trackNumber);

            //2、获取面单base64
            String pdfBase64 = shanDongPostUtils.getPdfBase64(trackNumber, eawbpre.getEawbReference2());
            if (null == pdfBase64) return "Terminal non-response";
            String[] pdfUrl = pdfBase64.split("<url>");
            if (pdfUrl.length < 2) {
                String errorMessage = "";
                String[] errorMessageArray = pdfBase64.split("<description>");
                if (errorMessageArray.length >= 2) {
                    String[] errorMessageStr = errorMessageArray[1].split("</description>");
                    errorMessage = errorMessageStr[0];
                }
                return (StringUtil.isEmpty(errorMessage)) ? "Failed to request ShanDong post order interface to obtain the invoice!" : errorMessage;
            }
            String[] url = pdfUrl[1].split("</url>");
            String labelStr = url[0];
            byte[] bytes = FileUtil.gainByteArrayFromUrl(labelStr);
            String base64Str = Base64.getEncoder().encodeToString(bytes);
            File file = FileUtil.base64GenerateFile(base64Str);
            com.alibaba.fastjson.JSONObject uploadResult_pdf = com.alibaba.fastjson.JSONObject.parseObject(FileUtil.uploadFile(file, "", uploadurl));
            String uuid = uploadResult_pdf.getString("uuid");
            if (uuid != null && !"".equals(uuid) && !"null".equals(uuid)) {
                String labelurl = downloadUrl + uuid;
                eawbpre.setInfoxml(labelurl);
                if (file != null && file.exists()) {
                    file.delete();
                }
            } else {
                if (file != null && file.exists()) {
                    file.delete();
                }
                throw new Exception("获取uuid失败");
            }
        } catch (Exception e) {
            return "ShanDong post response exception " + e.getMessage();
        }*/
    }
}
