package com.sinoair.ceop.service;

import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.service.transmode.TransMode;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.DateUtil;
import com.sinoair.core.utils.StringUtil;
import com.sinoair.core.utils.XMLUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Date;
import java.util.Iterator;
import java.util.List;
import java.util.Random;

import static java.lang.System.out;

/**
 * Created by WangXX4 on 2016/6/14.
 */
public class CainiaoServiceTestPostnl extends CommonTestCase {
    @Resource
    CainiaoService cainiaoService;
    @Resource
    EawbpreMapper eawbpreMapper;


    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {

    }


    @Test
    public void batchPostNL() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String serviceType = "DISTRIBUTOR_11180269";//线路
        String dectCountry = "NL";//目的国家
        int[] ordersInPackageArray = {15, 20, 34};//袋子预报内装数 200*10
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "XE05NL" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        //for (int k = 0; k < 10; k++) {

        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String destCity = getRandomString(new String[]{"Rotterdam", "Kampen", "Utrecht", "rotterdam", "Reeuwijk", "Putte", "Den Haag", "Eindhoven", "Amsterdam", "den haag", "Nijmegen", "Hardenberg", "Wolfheze", "Sevenum", "Roosendaal", "Gorinchem", "Tilburg", "amsterdam", "Lelystad", "zaandam", "Delft", "Oost-Souburg", "PANNINGEN", "s-gravendeel", "Etten-Leur", "belfeld", "heerlen", "schoonrewoerd", "Zwolle", "Den helder", "Meppel", "Almere", "Sint-Oedenrode", "Lisse", "Vianen", "Ridderkerk", "3561RT", "Haarlem", "Zoetermeer", "Den haag", "The Hague", "s-Hertogenbosch", "dinteloord", "Netherlands", "Kockengen", "Cuijk", "Balk", "jabeek", "Waalwijk", "Amersfoort", "koedijk", "Katwijk", "enschede", "Berg en Terblijt", "nieuwegein", "Renkum", "Sappemeer", "simonshaven", "assen", "Hardinxveld-Giessendam", "Heeze", "Alphen aan den Rijn", "Ravenstein", "Veldhoven", "Maastricht", "ASSEN", "Enschede", "Rhenen", "De Meern", "Woerden", "heeten", "Kootstertille", "IJmuiden", "Kapelle", "Weesp", "Puttershoek", "honselersdijk", "Escharen", "Groningen", "Zuidland", "Mechelen", "Harderwijk", "Roermond", "2106 EG", "Drachten", "westervoort", "Putten", "Alblasserdam", "Gemert", "Panningen", "eersel", "zaandijk", "Zuidlaren", "HEESCH", "Den Bosch", "Geleen", "Barneveld", "Drunen", "Batenburg", "VEENENDAAL", "kruiningen", "meerkerk", "Vlaardingen", "Capelle a/d IJssel", "obdam", "utrecht", "landgraaf", "Ingelum", "Ommen", "Hoensbroek", "Klaaswaal", "Heerlen", "Hilversum", "Someren", "Duiven", "Nieuwerkerk aan den IJssel", "Purmerend", "Zwijndrecht", "Denekamp", "Blyham", "Zevenbergschen Hoek", "klazienaveen", "Bruinisse", "Poeldijk", "Brielle", "Schildwolde", "Enkhuizen", "Nieuwveen", "nijmegen", "Nieuw-Weerdinge", "Bleiswijk", "Apeldoorn", "Rijkevoort", "ermelo", "rijssen", "Abcoude", "Noordgouwe", "Alkmaar", "almere", "APPELSCHA", "Fijnaart", "Neede", "ROTTERDAM", "Almere Haven", "Arnhem", "Rijswijk", "hoogerheide", "Saasveld", "harskamp", "lemele", "Harskamp", "WEESP", "Strijen", "Bennebroek", "voorhout", "Weert", "Oss", "veenendaal", "DELFT", "hoofddorp", "Gelderland", "Velsen-Noord", "Nieuw-Beijerland", "Schagen", "breda", "Andijk", "naarden", "leersum", "Kerkrade", "Wapse", "Lochem", "Waardhuizen", "Oosterhout", "Halsteren", "groningen", "Voorburg", "Wapenveld", "AMSTERDAM", "roosendaal", "Emmen", "den helder", "Geldrop", "eemnes", "Grevenbicht", "Zundert", "Bergen op zoom", "Waddinxveen", "Breda", "Silvolde", "Rijen", "hoensbroek", "Wassenaar", "Leiden", "hoogezand", "mookhoek", "Zandvoort", "Maassluis", "putten", "ZUIDSCHERMER", "Katwijk aan Zee", "apeldoorn", "Helmond", "Baarn", "helmond", "De Rips", "Beek", "sint michielsgestel", "Almelo", "Leek", "Steensel", "Maarssen", "afferden", "wernhout", "Doetinchem", "Dalfsen", "Hellevoetsluis", "Veenendaal", "Noordscheschut", "Heerde", "Zutphen", "doetinchem", "Soest", "amersfoort", "Goes", "katwijk", "Simpelveld", "Oudemirdum", "Odijk", "Axel", "Esch", "spijkenisse", "Etten-leur", "Suwald", "DEN BURG", "ridderkerk", "Sommelsdijk", "Cothen", "purmerend", "Leidschendam", "Schoonhoven", "Zevenhuizen", "Stadskanaal", "Drouwen", "Leerbroek", "almelo", "gouda", "Groot-Ammers", "Krommenie", "Breugel", "Berlicum", "Hengelo", "delft", "Sittard", "ijsselstein", "vuren", "maastricht", "Hoogblokland", "Velp NB", "Harkstede", "Landgraaf", "Albergen", "hoogeveen", "Vaassen", "Dodewaard", "Oudenbosch", "Westerbork", "Dinteloord", "Westerhaar", "nieuwerkerk Aan Den ijssel", "2665 CJ BLEIJSWIJK", "Bergschenhoek", "landsmeer", "bergen op zoom", "Koudum", "Oirschot", "De meern", "Hoek", "Bennekom", "Nieuwegein", "slaakweg", "kollum", "Gaanderen", "Dordrecht", "heinenoord", "leiden", "Gouda", "Brunssum", "Philippine", "Werkendam", "dronten", "Arnemuiden", "Hasselt", "6042CN Roermond", "oosterhout nb", "Oisterwijk", "soesterberg", "Boxmeer", "Hendrik ido ambacht", "LEIDEN", "bern", "Delfgauw", "Velsen-Zuid", "rijsenhout", "heerde", "wolvega", "Obdam", "De Rijp", "ommen", "Valkenswaard", "witmarsum", "Alteveer", "Eck en Wiel (Buren)", "8435SG Donkerbroek", "Castenray", "Twello", "Montfoort", "Hoeven", "velddriel", "sprundel", "Geldermalsen", "Emmeloord", "Waalre", "harderwijk", "haarlem", "dordrecht", "Nunspeet", "Oosteind", "Velden", "oss", "Grave", "Geulle", "bodegraven", "Uithoorn", "Lemmer", "Coevorden", "Amstelveen", "Almere-Haven", "Schiedam", "Geffen", "Oudkarspel", "Rijssen", "Rosmalen", "Hoofddorp", "Raamsdonksveer", "Amsterdam zuidoost", "Weerselo", "Culemborg", "Wierden", "Woubrugge", "Assen", "Joure", "haaksbergen", "Rijsenhout", "Hoorn", "Oud vossemeer", "Haaksbergen", "Sint-Pancras", "Nieuw Amsterdam", "Beverwijk", "Haaften", "Hoogeveen", "nuenen", "Spijkenisse", "beesd", "Loenen aan de Vecht", "Posterholt", "Alphen", "beek", "breukelen", "Berkel en Rodenrijs", "Wagenberg", "Ijlst", "Vorden", "zoetermeer", "Oud-Beijerland", "megen", "valkenswaard", "Voorhout", "Franeker", "Voorthuizen", "stolwijk", "Houten", "Geesteren", "Heemskerk", "Den Helder", "SPIJKENISSE", "made", "Made", "'s gravenzande", "s'hertogenbosch", "oostvoorne", "zevenaar", "Sint Maarten", "Volkel", "Dongen", "Middenbeemster", "nieuw-vennep", "Bunnik", "roermond", "Oudeschans", "uitgeest", "barendrecht", "Monster", "Rozenburg", "Haren, Gn", "Wernhout", "Akersloot", "Lopik", "DEDEMSVAART", "vlaardingen", "Amsterdm", "Hijum", "Pijnacker", "WINSUM GN", "assendelft", "Leeuwarden", "Rolde", "Lekkerkerk", "Ede", "DIDAM", "Vroomshoop", "schinveld", "schiedam", "Herten", "EDE", "Kortenhoef", "sint nicolaasga", "Velp", "Rhoon", "Zuidhorn", "Aalsmeer", "Vlissingen", "hendrik ido ambacht", "Diemen", "Borne", "ijmuiden", "Uithuizermeeden", "strijen", "Vught", "tilburg", "Wolvega", "Tiel", "Zetten", "Hoek van Holland", "Hoogezand", "rijswijk", "Hapert", "wassenaar", "Udenhout", "Cromvoirt", "Kornhorn", "Stramproy", "sibculo", "arnhem", "Neerbeek", "Nw. en St. Joosland", "Monnickendam", "Meijel", "4301XL ZIERIKZEE", "Hollum", "Tegelen", "goes", "Leiderdorp", "Kwadendamme", "Gennep", "Zuid-Scharwoude", "ZAANDAM", "HOOGLAND", "Wageningen", "lelystad", "Bodegraven", "velp", "BEERTA", "Oost Souburg", "hilversum", "voorburg", "Schoonebeek", "Oost,- West en Middelbeers", "Nederland", "Zeewolde", "Langweer", "ede", "Huizen", "houten", "Sassenheim", "buinerveen", "elst", "s Gravenzande", "Dronten", "Winschoten", "Riel", "nederland", "kerkrade", "Den Haag (The Hague)", "Nijkerk", "grave", "Milheeze", "benschop", "Ommeren", "Hulst", "Kollumerzwaag", "urk", "Amstelhoek", "muntendam", "huizen", "Volendam", "ALBLASSERDAM", "Opijnen", "geldermalsen", "Castricum"});
                String postCode = getRandomString(new String[]{"518000", "518102", "310023", "230601", "430023", "321404", "523941", "325603", "510000", "314300", "314001", "315314", "518103", "266000", "322000", "315000", "511400", "341000", "518111", "465550", "330000", "518116", "581000", "518109", "529600", "523118", "528251", "415800", "528225", "528000", "362000", "201299", "518131", "350000", "363000", "510260", "322118", "321000", "201318", "518112", "518055", "362212", "523900", "325800", "518126", "518129", "528425", "516600", "518100", "558003", "054001", "200120", "518400", "314100", "522000", "325100", "322200", "351100", "110000", "362333", "523000", "362700", "215000", "310000", "066000", "322099", "315206", "515800", "511500", "311215", "321100", "523863", "516081", "511446", "102600", "201612", "233000", "521000", "361000", "528200", "074004", "528441", "528042", "515041", "226000", "201803", "325604", "510320", "516001", "510140", "276000", "321300", "511430", "523690", "528471", "518003", "523129", "201400", "430070", "000000", "361001", "310020", "320000", "516029", "100020", "520620", "510650", "510145", "350003", "332200", "211100", "215600", "518110", "518132", "510405", "215100", "266101", "317200", "51810 ", "518028", "311112", "523899", "317300", "200063", "580000", "325204", "518031", "325000", "515010", "215008", "325804", "125100", "518054", "201800", "510165", "518019", "350109", "201104", "311512", "310051", "510660", "512500", "310015", "221000", "325200", "211400", "315033", "310018", "518101", "410007", "311800", "510080", "518104", "510599", "518014", "510025", "100011", "528400", "200023", "511490", "201315", "215500", "510520", "310030", "322017", "450016", "529000", "213161", "200900", "100018", "472200", "510420", "313000", "510430", "518133", "518004", "523770", "531214", "510100", "518042", "310052", "510630", "250100", "201808", "300163", "322008", "240000", "312400", "201108", "201418", "510403", "516000", "125000", "518114", "510235", "516006", "201106", "510641", "315012", "313100", "214028", "518108", "519090", "362400", "311804", "510070", "332000", "518115", "510700", "523915", "510510", "266071", "239341", "523039", "323000", "410510", "318000", "621700", "271600", "200000", "210209", "511800", "310019", "361009", "518113", "518033", "519000", "214000", "102211", "311100", "510620", "315334", "417600", "322100", "362018", "322299", "510640", "410000", "310009", "102208", "365000", "315400", "518048", "325600", "310014"});
                String eawb_reference1 = "RS" + date.substring(4) + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "NL";//RS597124928GB
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postCode,
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("荷兰标准大包号：--------------------- " + pkg_no);
        }
        //}
    }

    @Test
    public void batchPostNLMBP() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String serviceType = "DISTRIBUTOR_99999011";//线路
        String dectCountry = "NL";//目的国家
        int[] ordersInPackageArray = {11, 33, 27};//袋子预报内装数 200*10
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "XE011NL" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        //for (int k = 0; k < 10; k++) {

        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String destCity = getRandomString(new String[]{"Rotterdam", "Kampen", "Utrecht", "rotterdam", "Reeuwijk", "Putte", "Den Haag", "Eindhoven", "Amsterdam", "den haag", "Nijmegen", "Hardenberg", "Wolfheze", "Sevenum", "Roosendaal", "Gorinchem", "Tilburg", "amsterdam", "Lelystad", "zaandam", "Delft", "Oost-Souburg", "PANNINGEN", "s-gravendeel", "Etten-Leur", "belfeld", "heerlen", "schoonrewoerd", "Zwolle", "Den helder", "Meppel", "Almere", "Sint-Oedenrode", "Lisse", "Vianen", "Ridderkerk", "3561RT", "Haarlem", "Zoetermeer", "Den haag", "The Hague", "s-Hertogenbosch", "dinteloord", "Netherlands", "Kockengen", "Cuijk", "Balk", "jabeek", "Waalwijk", "Amersfoort", "koedijk", "Katwijk", "enschede", "Berg en Terblijt", "nieuwegein", "Renkum", "Sappemeer", "simonshaven", "assen", "Hardinxveld-Giessendam", "Heeze", "Alphen aan den Rijn", "Ravenstein", "Veldhoven", "Maastricht", "ASSEN", "Enschede", "Rhenen", "De Meern", "Woerden", "heeten", "Kootstertille", "IJmuiden", "Kapelle", "Weesp", "Puttershoek", "honselersdijk", "Escharen", "Groningen", "Zuidland", "Mechelen", "Harderwijk", "Roermond", "2106 EG", "Drachten", "westervoort", "Putten", "Alblasserdam", "Gemert", "Panningen", "eersel", "zaandijk", "Zuidlaren", "HEESCH", "Den Bosch", "Geleen", "Barneveld", "Drunen", "Batenburg", "VEENENDAAL", "kruiningen", "meerkerk", "Vlaardingen", "Capelle a/d IJssel", "obdam", "utrecht", "landgraaf", "Ingelum", "Ommen", "Hoensbroek", "Klaaswaal", "Heerlen", "Hilversum", "Someren", "Duiven", "Nieuwerkerk aan den IJssel", "Purmerend", "Zwijndrecht", "Denekamp", "Blyham", "Zevenbergschen Hoek", "klazienaveen", "Bruinisse", "Poeldijk", "Brielle", "Schildwolde", "Enkhuizen", "Nieuwveen", "nijmegen", "Nieuw-Weerdinge", "Bleiswijk", "Apeldoorn", "Rijkevoort", "ermelo", "rijssen", "Abcoude", "Noordgouwe", "Alkmaar", "almere", "APPELSCHA", "Fijnaart", "Neede", "ROTTERDAM", "Almere Haven", "Arnhem", "Rijswijk", "hoogerheide", "Saasveld", "harskamp", "lemele", "Harskamp", "WEESP", "Strijen", "Bennebroek", "voorhout", "Weert", "Oss", "veenendaal", "DELFT", "hoofddorp", "Gelderland", "Velsen-Noord", "Nieuw-Beijerland", "Schagen", "breda", "Andijk", "naarden", "leersum", "Kerkrade", "Wapse", "Lochem", "Waardhuizen", "Oosterhout", "Halsteren", "groningen", "Voorburg", "Wapenveld", "AMSTERDAM", "roosendaal", "Emmen", "den helder", "Geldrop", "eemnes", "Grevenbicht", "Zundert", "Bergen op zoom", "Waddinxveen", "Breda", "Silvolde", "Rijen", "hoensbroek", "Wassenaar", "Leiden", "hoogezand", "mookhoek", "Zandvoort", "Maassluis", "putten", "ZUIDSCHERMER", "Katwijk aan Zee", "apeldoorn", "Helmond", "Baarn", "helmond", "De Rips", "Beek", "sint michielsgestel", "Almelo", "Leek", "Steensel", "Maarssen", "afferden", "wernhout", "Doetinchem", "Dalfsen", "Hellevoetsluis", "Veenendaal", "Noordscheschut", "Heerde", "Zutphen", "doetinchem", "Soest", "amersfoort", "Goes", "katwijk", "Simpelveld", "Oudemirdum", "Odijk", "Axel", "Esch", "spijkenisse", "Etten-leur", "Suwald", "DEN BURG", "ridderkerk", "Sommelsdijk", "Cothen", "purmerend", "Leidschendam", "Schoonhoven", "Zevenhuizen", "Stadskanaal", "Drouwen", "Leerbroek", "almelo", "gouda", "Groot-Ammers", "Krommenie", "Breugel", "Berlicum", "Hengelo", "delft", "Sittard", "ijsselstein", "vuren", "maastricht", "Hoogblokland", "Velp NB", "Harkstede", "Landgraaf", "Albergen", "hoogeveen", "Vaassen", "Dodewaard", "Oudenbosch", "Westerbork", "Dinteloord", "Westerhaar", "nieuwerkerk Aan Den ijssel", "2665 CJ BLEIJSWIJK", "Bergschenhoek", "landsmeer", "bergen op zoom", "Koudum", "Oirschot", "De meern", "Hoek", "Bennekom", "Nieuwegein", "slaakweg", "kollum", "Gaanderen", "Dordrecht", "heinenoord", "leiden", "Gouda", "Brunssum", "Philippine", "Werkendam", "dronten", "Arnemuiden", "Hasselt", "6042CN Roermond", "oosterhout nb", "Oisterwijk", "soesterberg", "Boxmeer", "Hendrik ido ambacht", "LEIDEN", "bern", "Delfgauw", "Velsen-Zuid", "rijsenhout", "heerde", "wolvega", "Obdam", "De Rijp", "ommen", "Valkenswaard", "witmarsum", "Alteveer", "Eck en Wiel (Buren)", "8435SG Donkerbroek", "Castenray", "Twello", "Montfoort", "Hoeven", "velddriel", "sprundel", "Geldermalsen", "Emmeloord", "Waalre", "harderwijk", "haarlem", "dordrecht", "Nunspeet", "Oosteind", "Velden", "oss", "Grave", "Geulle", "bodegraven", "Uithoorn", "Lemmer", "Coevorden", "Amstelveen", "Almere-Haven", "Schiedam", "Geffen", "Oudkarspel", "Rijssen", "Rosmalen", "Hoofddorp", "Raamsdonksveer", "Amsterdam zuidoost", "Weerselo", "Culemborg", "Wierden", "Woubrugge", "Assen", "Joure", "haaksbergen", "Rijsenhout", "Hoorn", "Oud vossemeer", "Haaksbergen", "Sint-Pancras", "Nieuw Amsterdam", "Beverwijk", "Haaften", "Hoogeveen", "nuenen", "Spijkenisse", "beesd", "Loenen aan de Vecht", "Posterholt", "Alphen", "beek", "breukelen", "Berkel en Rodenrijs", "Wagenberg", "Ijlst", "Vorden", "zoetermeer", "Oud-Beijerland", "megen", "valkenswaard", "Voorhout", "Franeker", "Voorthuizen", "stolwijk", "Houten", "Geesteren", "Heemskerk", "Den Helder", "SPIJKENISSE", "made", "Made", "'s gravenzande", "s'hertogenbosch", "oostvoorne", "zevenaar", "Sint Maarten", "Volkel", "Dongen", "Middenbeemster", "nieuw-vennep", "Bunnik", "roermond", "Oudeschans", "uitgeest", "barendrecht", "Monster", "Rozenburg", "Haren, Gn", "Wernhout", "Akersloot", "Lopik", "DEDEMSVAART", "vlaardingen", "Amsterdm", "Hijum", "Pijnacker", "WINSUM GN", "assendelft", "Leeuwarden", "Rolde", "Lekkerkerk", "Ede", "DIDAM", "Vroomshoop", "schinveld", "schiedam", "Herten", "EDE", "Kortenhoef", "sint nicolaasga", "Velp", "Rhoon", "Zuidhorn", "Aalsmeer", "Vlissingen", "hendrik ido ambacht", "Diemen", "Borne", "ijmuiden", "Uithuizermeeden", "strijen", "Vught", "tilburg", "Wolvega", "Tiel", "Zetten", "Hoek van Holland", "Hoogezand", "rijswijk", "Hapert", "wassenaar", "Udenhout", "Cromvoirt", "Kornhorn", "Stramproy", "sibculo", "arnhem", "Neerbeek", "Nw. en St. Joosland", "Monnickendam", "Meijel", "4301XL ZIERIKZEE", "Hollum", "Tegelen", "goes", "Leiderdorp", "Kwadendamme", "Gennep", "Zuid-Scharwoude", "ZAANDAM", "HOOGLAND", "Wageningen", "lelystad", "Bodegraven", "velp", "BEERTA", "Oost Souburg", "hilversum", "voorburg", "Schoonebeek", "Oost,- West en Middelbeers", "Nederland", "Zeewolde", "Langweer", "ede", "Huizen", "houten", "Sassenheim", "buinerveen", "elst", "s Gravenzande", "Dronten", "Winschoten", "Riel", "nederland", "kerkrade", "Den Haag (The Hague)", "Nijkerk", "grave", "Milheeze", "benschop", "Ommeren", "Hulst", "Kollumerzwaag", "urk", "Amstelhoek", "muntendam", "huizen", "Volendam", "ALBLASSERDAM", "Opijnen", "geldermalsen", "Castricum"});
                String postCode = getRandomString(new String[]{"518000", "518102", "310023", "230601", "430023", "321404", "523941", "325603", "510000", "314300", "314001", "315314", "518103", "266000", "322000", "315000", "511400", "341000", "518111", "465550", "330000", "518116", "581000", "518109", "529600", "523118", "528251", "415800", "528225", "528000", "362000", "201299", "518131", "350000", "363000", "510260", "322118", "321000", "201318", "518112", "518055", "362212", "523900", "325800", "518126", "518129", "528425", "516600", "518100", "558003", "054001", "200120", "518400", "314100", "522000", "325100", "322200", "351100", "110000", "362333", "523000", "362700", "215000", "310000", "066000", "322099", "315206", "515800", "511500", "311215", "321100", "523863", "516081", "511446", "102600", "201612", "233000", "521000", "361000", "528200", "074004", "528441", "528042", "515041", "226000", "201803", "325604", "510320", "516001", "510140", "276000", "321300", "511430", "523690", "528471", "518003", "523129", "201400", "430070", "000000", "361001", "310020", "320000", "516029", "100020", "520620", "510650", "510145", "350003", "332200", "211100", "215600", "518110", "518132", "510405", "215100", "266101", "317200", "51810 ", "518028", "311112", "523899", "317300", "200063", "580000", "325204", "518031", "325000", "515010", "215008", "325804", "125100", "518054", "201800", "510165", "518019", "350109", "201104", "311512", "310051", "510660", "512500", "310015", "221000", "325200", "211400", "315033", "310018", "518101", "410007", "311800", "510080", "518104", "510599", "518014", "510025", "100011", "528400", "200023", "511490", "201315", "215500", "510520", "310030", "322017", "450016", "529000", "213161", "200900", "100018", "472200", "510420", "313000", "510430", "518133", "518004", "523770", "531214", "510100", "518042", "310052", "510630", "250100", "201808", "300163", "322008", "240000", "312400", "201108", "201418", "510403", "516000", "125000", "518114", "510235", "516006", "201106", "510641", "315012", "313100", "214028", "518108", "519090", "362400", "311804", "510070", "332000", "518115", "510700", "523915", "510510", "266071", "239341", "523039", "323000", "410510", "318000", "621700", "271600", "200000", "210209", "511800", "310019", "361009", "518113", "518033", "519000", "214000", "102211", "311100", "510620", "315334", "417600", "322100", "362018", "322299", "510640", "410000", "310009", "102208", "365000", "315400", "518048", "325600", "310014"});
                String eawb_reference1 = "RS" + date.substring(4) + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "NL";//RS597124928GB
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postCode,
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("荷兰标准大包号：--------------------- " + pkg_no);
        }
        //}
    }

    @Test
    public void batchPostNLMBR() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号14位
        String serviceType = "DISTRIBUTOR_99999013";//线路
        String dectCountry = "NL";//目的国家
        int[] ordersInPackageArray = {15, 10, 22};//袋子预报内装数 200*10
//		-------------------需要改的参数--end---------------------------------------------------------------
        String base_pkg_no = "XE013NL" + date;//大包号
        String cainiaoLPNO = "LP" + date.substring(4);//参考号2  菜鸟lp号 前缀
        //for (int k = 0; k < 10; k++) {

        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String destCity = getRandomString(new String[]{"Rotterdam", "Kampen", "Utrecht", "rotterdam", "Reeuwijk", "Putte", "Den Haag", "Eindhoven", "Amsterdam", "den haag", "Nijmegen", "Hardenberg", "Wolfheze", "Sevenum", "Roosendaal", "Gorinchem", "Tilburg", "amsterdam", "Lelystad", "zaandam", "Delft", "Oost-Souburg", "PANNINGEN", "s-gravendeel", "Etten-Leur", "belfeld", "heerlen", "schoonrewoerd", "Zwolle", "Den helder", "Meppel", "Almere", "Sint-Oedenrode", "Lisse", "Vianen", "Ridderkerk", "3561RT", "Haarlem", "Zoetermeer", "Den haag", "The Hague", "s-Hertogenbosch", "dinteloord", "Netherlands", "Kockengen", "Cuijk", "Balk", "jabeek", "Waalwijk", "Amersfoort", "koedijk", "Katwijk", "enschede", "Berg en Terblijt", "nieuwegein", "Renkum", "Sappemeer", "simonshaven", "assen", "Hardinxveld-Giessendam", "Heeze", "Alphen aan den Rijn", "Ravenstein", "Veldhoven", "Maastricht", "ASSEN", "Enschede", "Rhenen", "De Meern", "Woerden", "heeten", "Kootstertille", "IJmuiden", "Kapelle", "Weesp", "Puttershoek", "honselersdijk", "Escharen", "Groningen", "Zuidland", "Mechelen", "Harderwijk", "Roermond", "2106 EG", "Drachten", "westervoort", "Putten", "Alblasserdam", "Gemert", "Panningen", "eersel", "zaandijk", "Zuidlaren", "HEESCH", "Den Bosch", "Geleen", "Barneveld", "Drunen", "Batenburg", "VEENENDAAL", "kruiningen", "meerkerk", "Vlaardingen", "Capelle a/d IJssel", "obdam", "utrecht", "landgraaf", "Ingelum", "Ommen", "Hoensbroek", "Klaaswaal", "Heerlen", "Hilversum", "Someren", "Duiven", "Nieuwerkerk aan den IJssel", "Purmerend", "Zwijndrecht", "Denekamp", "Blyham", "Zevenbergschen Hoek", "klazienaveen", "Bruinisse", "Poeldijk", "Brielle", "Schildwolde", "Enkhuizen", "Nieuwveen", "nijmegen", "Nieuw-Weerdinge", "Bleiswijk", "Apeldoorn", "Rijkevoort", "ermelo", "rijssen", "Abcoude", "Noordgouwe", "Alkmaar", "almere", "APPELSCHA", "Fijnaart", "Neede", "ROTTERDAM", "Almere Haven", "Arnhem", "Rijswijk", "hoogerheide", "Saasveld", "harskamp", "lemele", "Harskamp", "WEESP", "Strijen", "Bennebroek", "voorhout", "Weert", "Oss", "veenendaal", "DELFT", "hoofddorp", "Gelderland", "Velsen-Noord", "Nieuw-Beijerland", "Schagen", "breda", "Andijk", "naarden", "leersum", "Kerkrade", "Wapse", "Lochem", "Waardhuizen", "Oosterhout", "Halsteren", "groningen", "Voorburg", "Wapenveld", "AMSTERDAM", "roosendaal", "Emmen", "den helder", "Geldrop", "eemnes", "Grevenbicht", "Zundert", "Bergen op zoom", "Waddinxveen", "Breda", "Silvolde", "Rijen", "hoensbroek", "Wassenaar", "Leiden", "hoogezand", "mookhoek", "Zandvoort", "Maassluis", "putten", "ZUIDSCHERMER", "Katwijk aan Zee", "apeldoorn", "Helmond", "Baarn", "helmond", "De Rips", "Beek", "sint michielsgestel", "Almelo", "Leek", "Steensel", "Maarssen", "afferden", "wernhout", "Doetinchem", "Dalfsen", "Hellevoetsluis", "Veenendaal", "Noordscheschut", "Heerde", "Zutphen", "doetinchem", "Soest", "amersfoort", "Goes", "katwijk", "Simpelveld", "Oudemirdum", "Odijk", "Axel", "Esch", "spijkenisse", "Etten-leur", "Suwald", "DEN BURG", "ridderkerk", "Sommelsdijk", "Cothen", "purmerend", "Leidschendam", "Schoonhoven", "Zevenhuizen", "Stadskanaal", "Drouwen", "Leerbroek", "almelo", "gouda", "Groot-Ammers", "Krommenie", "Breugel", "Berlicum", "Hengelo", "delft", "Sittard", "ijsselstein", "vuren", "maastricht", "Hoogblokland", "Velp NB", "Harkstede", "Landgraaf", "Albergen", "hoogeveen", "Vaassen", "Dodewaard", "Oudenbosch", "Westerbork", "Dinteloord", "Westerhaar", "nieuwerkerk Aan Den ijssel", "2665 CJ BLEIJSWIJK", "Bergschenhoek", "landsmeer", "bergen op zoom", "Koudum", "Oirschot", "De meern", "Hoek", "Bennekom", "Nieuwegein", "slaakweg", "kollum", "Gaanderen", "Dordrecht", "heinenoord", "leiden", "Gouda", "Brunssum", "Philippine", "Werkendam", "dronten", "Arnemuiden", "Hasselt", "6042CN Roermond", "oosterhout nb", "Oisterwijk", "soesterberg", "Boxmeer", "Hendrik ido ambacht", "LEIDEN", "bern", "Delfgauw", "Velsen-Zuid", "rijsenhout", "heerde", "wolvega", "Obdam", "De Rijp", "ommen", "Valkenswaard", "witmarsum", "Alteveer", "Eck en Wiel (Buren)", "8435SG Donkerbroek", "Castenray", "Twello", "Montfoort", "Hoeven", "velddriel", "sprundel", "Geldermalsen", "Emmeloord", "Waalre", "harderwijk", "haarlem", "dordrecht", "Nunspeet", "Oosteind", "Velden", "oss", "Grave", "Geulle", "bodegraven", "Uithoorn", "Lemmer", "Coevorden", "Amstelveen", "Almere-Haven", "Schiedam", "Geffen", "Oudkarspel", "Rijssen", "Rosmalen", "Hoofddorp", "Raamsdonksveer", "Amsterdam zuidoost", "Weerselo", "Culemborg", "Wierden", "Woubrugge", "Assen", "Joure", "haaksbergen", "Rijsenhout", "Hoorn", "Oud vossemeer", "Haaksbergen", "Sint-Pancras", "Nieuw Amsterdam", "Beverwijk", "Haaften", "Hoogeveen", "nuenen", "Spijkenisse", "beesd", "Loenen aan de Vecht", "Posterholt", "Alphen", "beek", "breukelen", "Berkel en Rodenrijs", "Wagenberg", "Ijlst", "Vorden", "zoetermeer", "Oud-Beijerland", "megen", "valkenswaard", "Voorhout", "Franeker", "Voorthuizen", "stolwijk", "Houten", "Geesteren", "Heemskerk", "Den Helder", "SPIJKENISSE", "made", "Made", "'s gravenzande", "s'hertogenbosch", "oostvoorne", "zevenaar", "Sint Maarten", "Volkel", "Dongen", "Middenbeemster", "nieuw-vennep", "Bunnik", "roermond", "Oudeschans", "uitgeest", "barendrecht", "Monster", "Rozenburg", "Haren, Gn", "Wernhout", "Akersloot", "Lopik", "DEDEMSVAART", "vlaardingen", "Amsterdm", "Hijum", "Pijnacker", "WINSUM GN", "assendelft", "Leeuwarden", "Rolde", "Lekkerkerk", "Ede", "DIDAM", "Vroomshoop", "schinveld", "schiedam", "Herten", "EDE", "Kortenhoef", "sint nicolaasga", "Velp", "Rhoon", "Zuidhorn", "Aalsmeer", "Vlissingen", "hendrik ido ambacht", "Diemen", "Borne", "ijmuiden", "Uithuizermeeden", "strijen", "Vught", "tilburg", "Wolvega", "Tiel", "Zetten", "Hoek van Holland", "Hoogezand", "rijswijk", "Hapert", "wassenaar", "Udenhout", "Cromvoirt", "Kornhorn", "Stramproy", "sibculo", "arnhem", "Neerbeek", "Nw. en St. Joosland", "Monnickendam", "Meijel", "4301XL ZIERIKZEE", "Hollum", "Tegelen", "goes", "Leiderdorp", "Kwadendamme", "Gennep", "Zuid-Scharwoude", "ZAANDAM", "HOOGLAND", "Wageningen", "lelystad", "Bodegraven", "velp", "BEERTA", "Oost Souburg", "hilversum", "voorburg", "Schoonebeek", "Oost,- West en Middelbeers", "Nederland", "Zeewolde", "Langweer", "ede", "Huizen", "houten", "Sassenheim", "buinerveen", "elst", "s Gravenzande", "Dronten", "Winschoten", "Riel", "nederland", "kerkrade", "Den Haag (The Hague)", "Nijkerk", "grave", "Milheeze", "benschop", "Ommeren", "Hulst", "Kollumerzwaag", "urk", "Amstelhoek", "muntendam", "huizen", "Volendam", "ALBLASSERDAM", "Opijnen", "geldermalsen", "Castricum"});
                String postCode = getRandomString(new String[]{"518000", "518102", "310023", "230601", "430023", "321404", "523941", "325603", "510000", "314300", "314001", "315314", "518103", "266000", "322000", "315000", "511400", "341000", "518111", "465550", "330000", "518116", "581000", "518109", "529600", "523118", "528251", "415800", "528225", "528000", "362000", "201299", "518131", "350000", "363000", "510260", "322118", "321000", "201318", "518112", "518055", "362212", "523900", "325800", "518126", "518129", "528425", "516600", "518100", "558003", "054001", "200120", "518400", "314100", "522000", "325100", "322200", "351100", "110000", "362333", "523000", "362700", "215000", "310000", "066000", "322099", "315206", "515800", "511500", "311215", "321100", "523863", "516081", "511446", "102600", "201612", "233000", "521000", "361000", "528200", "074004", "528441", "528042", "515041", "226000", "201803", "325604", "510320", "516001", "510140", "276000", "321300", "511430", "523690", "528471", "518003", "523129", "201400", "430070", "000000", "361001", "310020", "320000", "516029", "100020", "520620", "510650", "510145", "350003", "332200", "211100", "215600", "518110", "518132", "510405", "215100", "266101", "317200", "51810 ", "518028", "311112", "523899", "317300", "200063", "580000", "325204", "518031", "325000", "515010", "215008", "325804", "125100", "518054", "201800", "510165", "518019", "350109", "201104", "311512", "310051", "510660", "512500", "310015", "221000", "325200", "211400", "315033", "310018", "518101", "410007", "311800", "510080", "518104", "510599", "518014", "510025", "100011", "528400", "200023", "511490", "201315", "215500", "510520", "310030", "322017", "450016", "529000", "213161", "200900", "100018", "472200", "510420", "313000", "510430", "518133", "518004", "523770", "531214", "510100", "518042", "310052", "510630", "250100", "201808", "300163", "322008", "240000", "312400", "201108", "201418", "510403", "516000", "125000", "518114", "510235", "516006", "201106", "510641", "315012", "313100", "214028", "518108", "519090", "362400", "311804", "510070", "332000", "518115", "510700", "523915", "510510", "266071", "239341", "523039", "323000", "410510", "318000", "621700", "271600", "200000", "210209", "511800", "310019", "361009", "518113", "518033", "519000", "214000", "102211", "311100", "510620", "315334", "417600", "322100", "362018", "322299", "510640", "410000", "310009", "102208", "365000", "315400", "518048", "325600", "310014"});
                String eawb_reference1 = "RS" + date.substring(4) + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + "NL";//RS597124928GB
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postCode,
                        dectCountry, destCity,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("荷兰标准大包号：--------------------- " + pkg_no);
        }
        //}
    }

    /**
     * 墨西哥批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchMX() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "MX";//目的国家

        String serviceType = "DISTRIBUTOR_11131306";//线路  墨西哥标准
        int[] ordersInPackageArray = {3, 4};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RZ" + date.substring(4);//参考号1 mailno  RZ040003243MH  RZ040000220MH  RZ040171365MH
        String base_pkg_no = "CNMA" + date.substring(6) + dectCountry;//大包号  CNMA20160226MX20001
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "GL43SJ";//邮编
        String cainiaoLPNO =  "LP" + date + dectCountry;;//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) +dectCountry;
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, getRandomString(new String[]{"JUCHITAN DE ZARAGOZA", "zapopan", "tangancicuaro", "Queretaro", "Mexico", "Durango"}),
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }
    /**
     * 秘鲁批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchPE() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "PE";//目的国家

        String serviceType = "DISTRIBUTOR_99999001";//线路  墨西哥标准
        int[] ordersInPackageArray = {4, 2, 9,1};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RZ" + date.substring(4);//参考号1 mailno  RZ040003243MH  RZ040000220MH  RZ040171365MH
        String base_pkg_no = "CNMA" + date.substring(6) + dectCountry;//大包号  CNMA20160226MX20001
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "GL43SJ";//邮编
        String cainiaoLPNO =  "LP" + date + dectCountry;;//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, getRandomString(new String[]{"JUCHITAN DE ZARAGOZA", "zapopan", "tangancicuaro", "Queretaro", "Mexico", "Durango"}),
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }
    /**
     * 智利批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchCL() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "CL";//目的国家

        String serviceType = "DISTRIBUTOR_99999002";//线路  墨西哥标准
        int[] ordersInPackageArray = {3, 5};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RZ" + date.substring(4);//参考号1 mailno  RZ040003243MH  RZ040000220MH  RZ040171365MH
        String base_pkg_no = "CNMA" + date.substring(6) + dectCountry;//大包号  CNMA20160226MX20001
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "GL43SJ";//邮编
        String cainiaoLPNO =  "LP" + date + dectCountry;;//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, getRandomString(new String[]{"JUCHITAN DE ZARAGOZA", "zapopan", "tangancicuaro", "Queretaro", "Mexico", "Durango"}),
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }
    /**
     * 巴西批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchBR() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "BR";//目的国家

        String serviceType = "DISTRIBUTOR_99999003";//线路  墨西哥标准
        int[] ordersInPackageArray = {3, 2,5,11};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RZ" + date.substring(4);//参考号1 mailno  RZ040003243MH  RZ040000220MH  RZ040171365MH
        String base_pkg_no = "CNMA" + date.substring(6) + dectCountry;//大包号  CNMA20160226MX20001
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "GL43SJ";//邮编
        String cainiaoLPNO =  "LP" + date + dectCountry;;//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, getRandomString(new String[]{"JUCHITAN DE ZARAGOZA", "zapopan", "tangancicuaro", "Queretaro", "Mexico", "Durango"}),
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }
    /**
     * 秘鲁批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchBRnon() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "NBR";//目的国家

        String serviceType = "DISTRIBUTOR_99999009";//线路  墨西哥标准
        int[] ordersInPackageArray = {4, 3,7};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "RZ" + date.substring(4);//参考号1 mailno  RZ040003243MH  RZ040000220MH  RZ040171365MH
        String base_pkg_no = "CNMA" + date.substring(6) + dectCountry;//大包号  CNMA20160226MX20001
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "GL43SJ";//邮编
        String cainiaoLPNO =  "LP" + date + dectCountry;;//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, getRandomString(new String[]{"JUCHITAN DE ZARAGOZA", "zapopan", "tangancicuaro", "Queretaro", "Mexico", "Durango"}),
                        eawb_reference2, pkg_no, ordersInPackageArray[j]));
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }



    private String combinateBatchXml(String servicetype, String mailNo, String postcode, String country,
                                     String city, String orderCode, String pkg_No, int ordersInPackage) {
        String producttionName = getOneProductionName();
        String productionCnName = producttionName.split("-")[0];
        String productionEnName = producttionName.split("-")[1];
        String xml =
                "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2013-02-25 00:00:00</eventTime>" +
                        "<eventSource>taobao</eventSource>" +
                        "<eventTarget>" + servicetype + "</eventTarget>" +
                        "</eventHeader><eventBody><OrderInfos><product>" +
                        "<productNameCN>" + productionCnName + "</productNameCN>" +
                        "<productNameEN>" + productionEnName + "</productNameEN>" +
                        "<productQantity>" + randomMinMax(1, 5) + "</productQantity>" +
                        "<productCateCN/>" +
                        "<productCateEN>Cuddly cat Toy</productCateEN>" +
                        "<productId /><producingArea />" +
                        "<productWeight>500</productWeight>" +
                        "<productPrice>10</productPrice>" +
                        "</product>" +
                        "</OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>MAM0001</logisticsOrderId><tradeId>3018196085</tradeId>" +
                        "<mailNo>" + mailNo + "</mailNo>" +
                        "<Rcountry>" + country + "</Rcountry>" +
                        "<Rprovince>Barcelona</Rprovince><Rcity>" + city + "</Rcity><Remail><EMAIL></Remail><Raddress>Avda. Jose Pedro Alessandri 605 Depto. 801, Nunoa</Raddress>" +
                        "<Rpostcode>" + postcode + "</Rpostcode><Rname>Sandra Zambrno</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId>" +
                        "<Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode>" +
                        "<channel>HK</channel><Itotleweight>" + randomMinMax(50, 500) + "</Itotleweight><Itotlevalue>" + randomMinMax(50, 2000) + "</Itotlevalue><totleweight>2168</totleweight>" +
                        "<country>CN</country>" +
                        "<mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress/>" +
                        "<packageCode>" + pkg_No + "</packageCode>" +
                        "<orderCode>" + orderCode + "</orderCode>" +
                        "<packageWeight>" + randomMinMax(4000, 75000) + "</packageWeight>" +
                        "<ordersInPackage>" + ordersInPackage + "</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>";

        return xml;
    }


    private String getOneProductionName() {
        String[] str = new String[]{"棉外套-coat", "短裙-philabeg", "帽子-hat", "鞋子-shoes", "衬衫-shirt", "手机-phone", "袜子-stockings", "缎带-Ribbons", "墨水盒-Ink Cartridges", "童装套装-Children's Sets", "耳机-Headphones"};
        return getRandomString(str);
    }

    private String getRandomString(String[] arrs) {
        return arrs[getRandom0_n(arrs.length - 1)];
    }

    /**
     * 生成0-num的数据
     *
     * @param num
     * @return
     */
    private int getRandom0_n(int num) {
        Random rd = new Random();
        int n = rd.nextInt(num + 1);
        return n;
    }

    private int randomMinMax(int min, int max) {
        Random random = new Random();
        int s = random.nextInt(max) % (max - min + 1) + min;
        return s;
    }

    @Test
    public void testXml() throws DocumentException {
        String xml = "<request>\n" +
                "    <logisticsOrderCode>LP20160920141206</logisticsOrderCode>\n" +
                "    <sender>\n" +
                "        <imID>BifrostTester</imID>\n" +
                "        <name>BifrostTester</name>\n" +
                "        <address>\n" +
                "            <country>CN</country>\n" +
                "            <detailAddress>some detailed address</detailAddress>\n" +
                "        </address>\n" +
                "    </sender>\n" +
                "    <receiver>\n" +
                "        <imID>receiverTester</imID>\n" +
                "        <name>receiverTester</name>\n" +
                "        <zipCode>123123</zipCode>\n" +
                "        <address>\n" +
                "            <country>PE</country>\n" +
                "            <detailAddress>detailed receiver address</detailAddress>\n" +
                "        </address>\n" +
                "    </receiver>\n" +
                "    <parcel>\n" +
                "        <weight>1000</weight>\n" +
                "        <weightUnit>g</weightUnit>\n" +
                "        <suggestedWeight>1000</suggestedWeight>\n" +
                "        <price>40</price>\n" +
                "        <priceUnit>CENT</priceUnit>\n" +
                "        <goodsList>\n" +
                "            <goods>\n" +
                "                <name>name</name>\n" +
                "                <cnName>商品</cnName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>100</price>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <quantity>10</quantity>\n" +
                "                <url>some url</url>\n" +
                "            </goods>\n" +
                "        </goodsList>\n" +
                "    </parcel>\n" +
                "    <trackingNumber>1120160920141207</trackingNumber>\n" +
                "    <bizType>1</bizType>\n" +
                "</request>";
        Document doc = DocumentHelper.parseText(xml);
        Element rootElement = doc.getRootElement();
        String logisticsOrderCode = XMLUtil.getElementContent(rootElement, "logisticsOrderCode");
        String trackingNumber = XMLUtil.getElementContent(rootElement, "trackingNumber");
        String bizType = XMLUtil.getElementContent(rootElement, "bizType");
        System.out.println(logisticsOrderCode);
        System.out.println(trackingNumber);
        System.out.println(bizType);

        Iterator senderIterator = rootElement.elementIterator("sender");
        if (senderIterator.hasNext()) {
            Element senderElement = (Element) senderIterator.next();
            String imID = XMLUtil.getElementContent(senderElement, "imID");
            String name = XMLUtil.getElementContent(senderElement, "name");
            System.out.println(imID+"--"+name);
            Iterator addressIterator = senderElement.elementIterator("address");
            if (addressIterator.hasNext()) {
                Element addressElement = (Element) addressIterator.next();
                String country = XMLUtil.getElementContent(addressElement, "country");
                String detailAddress = XMLUtil.getElementContent(addressElement, "detailAddress");
                System.out.println(country+"--"+detailAddress);
            }

        }
    }
}