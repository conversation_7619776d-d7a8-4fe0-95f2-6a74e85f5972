package com.sinoair.core.utils;

import com.sinoair.ceop.domain.model.Eawbpre;
import org.junit.Assert;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;


public class SinoairUtilTest {

    private Logger log = LoggerFactory.getLogger(SinoairUtilTest.class);

    @Test
    public void validatePassword() {
        //1.密码最少8 characters.
        //2.密码必须至少包含4种不同的字符，如大写字母、小写字母、数字和标点符号
        Assert.assertFalse(SinoairUtil.validatePassword("22"));
        Assert.assertFalse(SinoairUtil.validatePassword("12345678"));
        Assert.assertFalse(SinoairUtil.validatePassword("aaaaaaaa"));
        Assert.assertFalse(SinoairUtil.validatePassword("AAAAAAAA"));
        Assert.assertFalse(SinoairUtil.validatePassword(",,,,,,,,,"));
        Assert.assertFalse(SinoairUtil.validatePassword("123aaaAAA"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA,"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA!"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA&"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA)"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA-"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA<"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA>"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA/"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA["));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA]"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA{"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA}"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA\\"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA|"));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA="));
        Assert.assertTrue(SinoairUtil.validatePassword("123aaaAAA+"));
    }

    @Test
    public void testCodeValidation() {

//        String sinoairCode = "88016516776";
        String sinoairCode = "87000000022";
        boolean res = SinoairUtil.codeValidation(sinoairCode);
        log.info(res + "");
    }


    @Test
    public void testIsKingSpeed() {

        String serviceType = "DISTRIBUTOR_88888017";
        boolean kingSpeed = SinoairUtil.isKingSpeed(serviceType);
        log.info(kingSpeed ? "kingSpeed" : "Why?");
    }


    @Test
    public void testChecked4KingSpeed() {

        Eawbpre eawbpre = new Eawbpre();
        eawbpre.setEawbDeliverPostcode("41048");
        eawbpre.setEawbDeststate("KY");
        eawbpre.setEawbHscode("1111");
        eawbpre.setEawbDeliverAddress("333,  41048");
        eawbpre.setEawbDeliverPhone("123");
        String res = SinoairUtil.checked4KingSpeedFBA("", eawbpre);
        log.info(res);
    }

    @Test
    public void testIsRightFlightNumber() {
        Assert.assertFalse(SinoairUtil.checkFlightNumber(null));
        Assert.assertFalse(SinoairUtil.checkFlightNumber("ck001"));
        Assert.assertTrue(SinoairUtil.checkFlightNumber("CK0"));
        Assert.assertTrue(SinoairUtil.checkFlightNumber("CK001"));
        Assert.assertFalse(SinoairUtil.checkFlightNumber("0CK001"));
        Assert.assertFalse(SinoairUtil.checkFlightNumber("AAAck001"));

        Assert.assertTrue(SinoairUtil.checkFlightNumber("MU002"));
        Assert.assertFalse(SinoairUtil.checkFlightNumber("MU"));
        Assert.assertFalse(SinoairUtil.checkFlightNumber("TK001"));
    }


    @Test
    public void testIsMultiPieces() {
        log.info("--" + SinoairUtil.isMultiplePieces("ASSS"));
        log.info("--" + SinoairUtil.isMultiplePieces("DISTRIBUTOR_88888003"));

    }

    @Test
    public void testIsOnePiece() {
        boolean onePiece = SinoairUtil.isOnePiece("USA_UPS_EXPERDITED_SM");
        log.info(onePiece + "");
    }

    @Test
    public void testDecode() throws Exception {
        String mi = "nHCWVguaxsE=";
        log.info(SinoairEncryptDecryptUtil.decode(mi, "912115144"));
    }

    @Test
    public void testdecodeCustomerDeliver() {
        String fullName = "MGPTE+Q6u/HqvEl+GVb5Cg==";
        String identityNo = "PDoVBiZqvI6sHQreWOXLRQ==";
        String s = SinoairUtil.decodeCustomerDeliver(fullName);
        System.out.println("fullName=" + s);
        s = SinoairUtil.decodeCustomerDeliver(identityNo);
        System.out.println("identityNo=" + s);
    }

    @Test
    public void testIsHscode() {
        String hscode = "";
        Assert.assertFalse(SinoairUtil.isHSCODE(hscode));
        hscode = "842499990";
        Assert.assertFalse(SinoairUtil.isHSCODE(hscode));
        hscode = "4805919000999";
        Assert.assertFalse(SinoairUtil.isHSCODE(hscode));
        hscode = "false";
        Assert.assertFalse(SinoairUtil.isHSCODE(hscode));
        hscode = "1";
        Assert.assertFalse(SinoairUtil.isHSCODE(hscode));
        hscode = "6401100000";
        Assert.assertTrue(SinoairUtil.isHSCODE(hscode));

    }

    @Test
    public void testHH() {
        System.out.println(SinoairUtil.getCodeValidation("1234"));
    }

    @Test
    public void testIsMawbNo() {
        String mawbNo = "111-49469475";
        boolean r = SinoairUtil.isMawbNo(mawbNo);
        System.out.println(r ? "正确的主单号" : "错误的主单号");
    }
}