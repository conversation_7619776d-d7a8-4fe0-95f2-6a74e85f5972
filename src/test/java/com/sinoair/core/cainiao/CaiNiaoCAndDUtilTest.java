package com.sinoair.core.cainiao;

import com.alibaba.fastjson.JSON;
import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.dao.ExpressitemMapper;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.Expressitem;
import com.sinoair.ceop.domain.vo.cainiao.candd.*;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.Base64Utils;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.*;

public class CaiNiaoCAndDUtilTest extends CommonTestCase {

    private static final String APPSECRET = "d37A66270pi0I353490a184713L3G9IY";

    @Autowired
    private EawbpreMapper eawbpreMapper;

    @Autowired
    private ExpressitemMapper expressitemMapper;

    private static final String logisticProviderId = "a469d449835565ab52e6a77c3c8624fd";

    private static final Long havanaId = 2212550187026L;

    private static Map<String,String> SOLUTION_CODE_MAP = new HashMap<>();

    static {
        SOLUTION_CODE_MAP.put("DISTRIBUTOR_FRMPRIVE","EUR_EXPRESS_COLISPRIVE");
        SOLUTION_CODE_MAP.put("DISTRIBUTOR_CAINIAO_FRM6A","EUR_EXPRESS_COLISSIMO");
        SOLUTION_CODE_MAP.put("CAINIAO_CTT_PRT","EUR_EXPRESS_CTT_PT");
        SOLUTION_CODE_MAP.put("CAINIAO_CTT_SG","EUR_EXPRESS_CTT_ES");
    }

    @Test
    public void cainiaoGlobalTakingOrder() {
        List<Eawbpre> eawbpres = eawbpreMapper.selectEawbpreByReference2("WCL20211014022");
        Eawbpre eawbpre = eawbpres.get(0);
        List<Expressitem> expressitems = expressitemMapper.selectExpressitemsByEawbSyscode(eawbpre.getEawbSyscode());
        CainiaoGlobalTakingOrderRequest cainiaoGlobalTakingOrderRequest = decorateCainiaoGlobalTakingOrderRequestByEawbpre(eawbpre,expressitems);
        CainiaoGlobalTakingOrderResponse cainiaoGlobalTakingOrderResponse = CaiNiaoCAndDUtil.cainiaoGlobalTakingOrder(logisticProviderId,cainiaoGlobalTakingOrderRequest);
        System.out.println(cainiaoGlobalTakingOrderResponse.getSuccess());
    }

    @Test
    public void mailnoQueryService() {
        MailnoQueryServiceRequest mailnoQueryServiceRequest = new MailnoQueryServiceRequest();
        mailnoQueryServiceRequest.setBizSource("OPEN");
        mailnoQueryServiceRequest.setCnId(CaiNiaoCAndDUtil.getHavanaId(""));
        mailnoQueryServiceRequest.setOrderCode("LP00495910444985");

        MailnoQueryServiceResponse mailnoQueryServiceResponse = CaiNiaoCAndDUtil.mailnoQueryService(logisticProviderId,mailnoQueryServiceRequest);
        System.out.println(mailnoQueryServiceResponse.getSuccess());

    }

    @Test
    public void globalParcelNetworkPrintDataPdfInfo() {
        GlobalParcelNetworkPrintDataPdfInfoRequest globalParcelNetworkPrintDataPdfInfoRequest = new GlobalParcelNetworkPrintDataPdfInfoRequest();
        globalParcelNetworkPrintDataPdfInfoRequest.setPrintType(1); //小包1 大包256
        globalParcelNetworkPrintDataPdfInfoRequest.setUserId(CaiNiaoCAndDUtil.getHavanaId(""));
        globalParcelNetworkPrintDataPdfInfoRequest.setOrderCode("LP00495454321310");
        System.out.println(JSON.toJSONString(globalParcelNetworkPrintDataPdfInfoRequest));
        GlobalParcelNetworkPrintDataPdfInfoResponse globalParcelNetworkPrintDataPdfInfoResponse = CaiNiaoCAndDUtil.globalParcelNetworkPrintDataPdfInfo(logisticProviderId, globalParcelNetworkPrintDataPdfInfoRequest);
        System.out.println(globalParcelNetworkPrintDataPdfInfoResponse.getSuccess());
        Boolean flag = Base64Utils.base64ToFile(globalParcelNetworkPrintDataPdfInfoResponse.getData(), "D:\\tmp\\" + globalParcelNetworkPrintDataPdfInfoRequest.getOrderCode() + ".pdf");
        System.out.println(flag);
    }

    @Test
    public void cainiaoGlobalUpdateLogisticsOrder() {
        CainiaoGlobalUpdateLogisticsOrderRequest cainiaoGlobalUpdateLogisticsOrderRequest = new CainiaoGlobalUpdateLogisticsOrderRequest();
        cainiaoGlobalUpdateLogisticsOrderRequest.setBizSource("OPEN");
        cainiaoGlobalUpdateLogisticsOrderRequest.setHavanaId(CaiNiaoCAndDUtil.getHavanaId(""));
        cainiaoGlobalUpdateLogisticsOrderRequest.setOrderCode("LP00495454321310");
        PackageParam packageParam = new PackageParam();
//        packageParam.setLength(2L);
//        packageParam.setWeight(2L);
//        packageParam.setHeight(2L);
        packageParam.setWeight(1100L);
        cainiaoGlobalUpdateLogisticsOrderRequest.setPackageParam(packageParam);

        CainiaoGlobalUpdateLogisticsOrderResponse cainiaoGlobalUpdateLogisticsOrderResponse = CaiNiaoCAndDUtil.cainiaoGlobalUpdateLogisticsOrder(logisticProviderId,cainiaoGlobalUpdateLogisticsOrderRequest);
        System.out.println(cainiaoGlobalUpdateLogisticsOrderResponse.getSuccess());


    }

    @Test
    public void cainiaoGlobalCreateBigbag() {
        CainiaoGlobalCreateBigbagRequest cainiaoGlobalCreateBigbagRequest = new CainiaoGlobalCreateBigbagRequest();
        cainiaoGlobalCreateBigbagRequest.setHavanaId(CaiNiaoCAndDUtil.getHavanaId(""));
        cainiaoGlobalCreateBigbagRequest.setWeight(1100d);
        cainiaoGlobalCreateBigbagRequest.setWeightUnit("g");
        cainiaoGlobalCreateBigbagRequest.setBizSource("OPEN");

        List<String> orderCodeList = new ArrayList<>();
        orderCodeList.add("LP00494326603713");
        cainiaoGlobalCreateBigbagRequest.setOrderCodeList(orderCodeList);

        PickUpInfoParam pickUpInfoParam = new PickUpInfoParam();
        pickUpInfoParam.setName("123");
        pickUpInfoParam.setPhone("123");
        pickUpInfoParam.setMobile("123");
        AddressPram addressPram = new AddressPram();
        addressPram.setCountry("中国");
        addressPram.setProvince("浙江省");
        addressPram.setCity("杭州市");
        addressPram.setDetailAddress("文一西路680号");
        addressPram.setZipCode("310012");
        pickUpInfoParam.setAddressPram(addressPram);
        cainiaoGlobalCreateBigbagRequest.setPickUpInfoParam(pickUpInfoParam);

        CainiaoGlobalCreateBigbagResponse cainiaoGlobalCreateBigbagResponse = CaiNiaoCAndDUtil.cainiaoGlobalCreateBigbag(logisticProviderId,cainiaoGlobalCreateBigbagRequest);
        System.out.println(cainiaoGlobalCreateBigbagResponse.getSuccess());
    }

    @Test
    public void trackDetailInfoCallback() {
        TrackDetailInfoCallbackRequest trackDetailInfoCallbackRequest = new TrackDetailInfoCallbackRequest();
        trackDetailInfoCallbackRequest.setMailNo("DY510475194PT");
        trackDetailInfoCallbackRequest.setShowStandard(true);

        TrackDetailInfoCallbackResponse trackDetailInfoCallbackResponse = CaiNiaoCAndDUtil.trackDetailInfoCallback(logisticProviderId, trackDetailInfoCallbackRequest);
        System.out.println(trackDetailInfoCallbackResponse.getSuccess());
    }

    @Test
    public void hoAirLineRequest() {
//        HoAirLineRequestJson hoAirLineRequestJson = new HoAirLineRequestJson();
//        HoAirLineRequest hoAirLineRequest = new HoAirLineRequest();
//        hoAirLineRequest.setShipmentWeight(1100d);
//        hoAirLineRequest.setMawbNo("222-97992212");
//        hoAirLineRequest.setFromPortCode("HGH");
//        hoAirLineRequest.setFlightNo("CA111");
//        hoAirLineRequest.setEta("2021-11-25 9:00:00");
//        hoAirLineRequest.setEtd("2021-11-25 9:00:00");
//        hoAirLineRequest.setMawbPieces(1);
//        hoAirLineRequest.setToPortCode("SCL");
//        ArrayList<BigBag> bigBagLists = new ArrayList<>();
//        BigBag bigBagList = new BigBag();
//        bigBagList.setBigBagId("SINOT00702101CL");
//        bigBagLists.add(bigBagList);
//        hoAirLineRequest.setBigBagList(bigBagLists);
//        hoAirLineRequest.setWeightUnit("1.1");
//        hoAirLineRequestJson.setHoAirLineRequest(hoAirLineRequest);
//
//        TrackDetailInfoCallbackResponse trackDetailInfoCallbackResponse = CaiNiaoCAndDUtil.hoAirLineRequest(logisticProviderId, hoAirLineRequestJson);
//        System.out.println(JSON.toJSONString(trackDetailInfoCallbackResponse));
    }


    private static CainiaoGlobalTakingOrderRequest decorateCainiaoGlobalTakingOrderRequestByEawbpre(Eawbpre eawbpre, List<Expressitem> expressitems) {
        Expressitem expressitem = expressitems.get(0);
        TakingOrderRequest takingOrderRequest = new TakingOrderRequest();
        takingOrderRequest.setSyncConsign(true);
        takingOrderRequest.setOutOrderId(eawbpre.getEawbReference2());
        takingOrderRequest.setBizSource("OPEN");

        //交易订单信息
        TradeOrderParam tradeOrderParam = new TradeOrderParam();
        tradeOrderParam.setTradeOrderId("100");
        tradeOrderParam.setCreateTime(String.valueOf(new Date().getTime() / 1000));
        tradeOrderParam.setPayTime(String.valueOf(new Date().getTime()/1000));
        tradeOrderParam.setTradeOrderCode(eawbpre.getEawbReference2());
        takingOrderRequest.setTradeOrderParam(tradeOrderParam);

        //解决方案信息
        SolutionParam solutionParam = new SolutionParam();
        solutionParam.setSolutionCode(null != SOLUTION_CODE_MAP.get(eawbpre.getEawbServicetype()) ? SOLUTION_CODE_MAP.get(eawbpre.getEawbServicetype()) : "EUR_EXPRESS_COLISPRIVE");
        List<ServiceParam> serviceParams = new ArrayList<>();
        ServiceParam serviceParam = new ServiceParam();
        serviceParam.setCode("SELF_SEND");
        serviceParams.add(serviceParam);
        solutionParam.setServiceParams(serviceParams);
        takingOrderRequest.setSolutionParam(solutionParam);

        //包裹列表
        List<PackageParam> packageParams = new ArrayList<>();
        PackageParam packageParam = new PackageParam();
        packageParam.setLength(1L);
        packageParam.setWidth(1L);
        packageParam.setHeight(1L);
        packageParam.setWeight(null != eawbpre.getEawbTotalgrossweight() ? eawbpre.getEawbTotalgrossweight().longValue() : null);
        List<ItemParam> itemParams = new ArrayList<>();
        ItemParam itemParam = new ItemParam();
        itemParam.setItemId(expressitem.getPid());
        itemParam.setQuantity(expressitem.getEcQuantity());
        itemParam.setEnglishName(expressitem.getEcCustprodenname());
        itemParam.setChineseName(expressitem.getEcCustprodname());
        itemParam.setUnitPrice(null != expressitem.getDeclaredValue() ? expressitem.getDeclaredValue().longValue() : null);
        itemParam.setUnitPriceCurrency("CNY");
        itemParam.setWeight(null != expressitem.getEcCustprodweight() ? expressitem.getEcCustprodweight().longValue() : null);
        ItemFeatures itemFeatures = new ItemFeatures();
        itemFeatures.setItemFlag("TEST");
        itemParams.add(itemParam);
        packageParam.setItemParams(itemParams);
        packageParams.add(packageParam);
        takingOrderRequest.setPackageParams(packageParams);

        //收件人信息
        ReceiverParam receiverParam = new ReceiverParam();
        receiverParam.setName(eawbpre.getEawbDeliverContact());
        receiverParam.setTelephone(null != eawbpre.getEawbDeliverPhone() ? eawbpre.getEawbDeliverPhone() : eawbpre.getEawbDeliverMobile());
        receiverParam.setMobilePhone(null != eawbpre.getEawbDeliverMobile() ? eawbpre.getEawbDeliverMobile() : eawbpre.getEawbDeliverPhone());
        receiverParam.setCountry(eawbpre.getEawbDestcountry());
        receiverParam.setProvince(eawbpre.getEawbDeststate());
        receiverParam.setCity(eawbpre.getEawbDestcity());
        receiverParam.setDetailAddress(eawbpre.getEawbDeliverAddress());
        receiverParam.setZipcode(eawbpre.getEawbDeliverPostcode());
        receiverParam.setEmail(eawbpre.getEawbDeliverEmail());
        takingOrderRequest.setReceiverParam(receiverParam);

        //发件人信息
        SenderParam senderParam = new SenderParam();
        senderParam.setUserNick("aliqatest01");  //TODO 上线时去掉
        senderParam.setName("Ivan Chia");
        senderParam.setTelephone("008657185095928");
        senderParam.setMobilePhone("008657185095928");
        senderParam.setCountry("CN");
        senderParam.setProvince("COMPANS");
        senderParam.setCity("COMPANS");
        senderParam.setDetailAddress("Test-5th Floor,Diguang Digital Technology Park,GuangmingDistrict Jiazitang 9th Road");
        senderParam.setZipcode("1000");
        senderParam.setEmail("<EMAIL>");
        takingOrderRequest.setSenderParam(senderParam);

        //卖家信息
        SellerParam sellerParam = new SellerParam();
        sellerParam.setHavanaId(havanaId);
        takingOrderRequest.setSellerParam(sellerParam);

        //扩展信息
        Features features = new Features();
        features.setShopName("TEST");
        features.setSecondSellerId("sellerTest");
        takingOrderRequest.setFeatures(features);

        CainiaoGlobalTakingOrderRequest cainiaoGlobalTakingOrderRequest =  new CainiaoGlobalTakingOrderRequest();
        cainiaoGlobalTakingOrderRequest.setTakingOrderRequest(takingOrderRequest);
        return cainiaoGlobalTakingOrderRequest;
    }


    public static final String cainiaoGlobalTakingOrderRequestJsonStr = "{\n" +
            "    \"TakingOrderRequest\":{\n" +
            "        \"syncConsign\":\"false\",\n" +
            "        \"outOrderId\":\"SN00463789659515\",\n" +
            "        \"tradeOrderParam\":{\n" +
            "            \"tradeOrderId\":\"191976046995099466\",\n" +
            "            \"createTime\":\"1613806904000\",\n" +
            "            \"payTime\":\"1613806904000\",\n" +
            "            \"tradeOrderCode\":\"AW191976046995099466ER\"\n" +
            "        },\n" +
            "        \"solutionParam\":{\n" +
            "            \"solutionCode\":\"EUR_EXPRESS_CTT_ES\",\n" +
            "            \"serviceParams\":[\n" +
            "                {\n" +
            "                    \"code\":\"SELF_SEND\"\n" +
            "                }\n" +
            "            ]\n" +
            "        },\n" +
            "        \"packageParams\":[\n" +
            "            {\n" +
            "                \"length\":\"24\",\n" +
            "                \"width\":\"10\",\n" +
            "                \"height\":\"5\",\n" +
            "                \"weight\":\"10\",\n" +
            "                \"itemParams\":[\n" +
            "                    {\n" +
            "                        \"itemId\":\"373871\",\n" +
            "                        \"quantity\":\"1\",\n" +
            "                        \"englishName\":\"1000 pieces adult kids paper jigsaw puzzles e\",\n" +
            "                        \"chineseName\":\"75*50cm 1000片拼图 成人儿童大型拼图益智减压玩\",\n" +
            "                        \"unitPrice\":\"2\",\n" +
            "                        \"unitPriceCurrency\":\"USD\",\n" +
            "                        \"weight\":\"2\",\n" +
            "                        \"features\":{\n" +
            "                            \"itemFlag\":\"373871\"\n" +
            "                        }\n" +
            "                    }\n" +
            "                ]\n" +
            "            }\n" +
            "        ],\n" +
            "        \"sellerParam\":{\n" +
            "            \"havanaId\":\"2210468704030\"\n" +
            "        },\n" +
            "        \"senderParam\":{\n" +
            "            \"userNick\":\"aliqatest01\",\n" +
            "            \"name\":\"Zhang Yunlu\",\n" +
            "            \"telephone\":\"18098956715\",\n" +
            "            \"mobilePhone\":\"18098956715\",\n" +
            "            \"country\":\"BE\",\n" +
            "            \"province\":\"Guangdong\",\n" +
            "            \"city\":\"Shenzhen\",\n" +
            "            \"detailAddress\":\"Test-5th Floor,Diguang Digital Technology Park,GuangmingDistrict Jiazitang 9th Road\",\n" +
            "            \"email\":\"<EMAIL>\",\n" +
            "            \"zipcode\":\"1000\"\n" +
            "        },\n" +
            "        \"receiverParam\":{\n" +
            "            \"name\":\"Kai Arne Sandhaugo\",\n" +
            "            \"telephone\":\"47 99397811\",\n" +
            "            \"mobilePhone\":\"47 99397811\",\n" +
            "            \"country\":\"ES\",\n" +
            "            \"province\":\"CORDOBA\",\n" +
            "            \"city\":\"CORDOBA\",\n" +
            "            \"detailAddress\":\"Vestsidevegen 1705 VOEC NO:2024926 Code:Paid--test\",\n" +
            "            \"zipcode\":\"14001\",\n" +
            "            \"email\":\"<EMAIL>\"\n" +
            "        },\n" +
            "        \"bizSource\":\"OPEN\",\n" +
            "        \"features\":{\n" +
            "            \"shopName\":\"shopBBB\",\n" +
            "            \"secondSellerId\":\"sellerTest\"\n" +
            "        }\n" +
            "    }\n" +
            "}\n";


    private static final String eawbpreStr = "{\"ctCode\":\"502\",\"customerOrderCode\":\"****************\",\"eawbBusinessmode\":\"A\",\"eawbCctype\":\"N\",\"eawbChannelCode\":\"L_AE_STANDARD_SINOTRANS_CTT_PORTUGAL_BAT\",\"eawbCheckstatus\":\"N\",\"eawbConsigneeAccountname\":\"David Pinto\",\"eawbConsignmentno\":\"****************\",\"eawbCustcurrency\":\"502\",\"eawbCustdeclval\":22.28,\"eawbCustprodenname\":\"usb car charger\",\"eawbCustprodname\":\"车载MP3播放器\",\"eawbCustregistrationcode\":\"**********\",\"eawbDeclarechargeable\":0.09,\"eawbDeclaregrossweight\":0.09,\"eawbDeclarevalue\":22.28,\"eawbDeclarevolume\":0.09,\"eawbDeliverAddress\":\"Rua Sao Cipriano,588\",\"eawbDeliverContact\":\"David Pinto\",\"eawbDeliverEmail\":\"<EMAIL>\",\"eawbDeliverPhone\":\"*********\",\"eawbDeliverPostcode\":\"4835-461\",\"eawbDepartcity\":\"dong guan shi\",\"eawbDepartcountry\":\"CN\",\"eawbDepartstate\":\"guang dong sheng\",\"eawbDestcity\":\"Guimaraes\",\"eawbDestcountry\":\"PT\",\"eawbDestination\":\"MAD\",\"eawbDeststate\":\"Tabuadelo\",\"eawbEcommerce\":\"GSDP\",\"eawbEntrustcode\":\"WY140619001\",\"eawbHandletime\":*************,\"eawbHasBattery\":\"true\",\"eawbHscode\":\"**********\",\"eawbIetype\":\"E\",\"eawbInner\":\"N\",\"eawbInsuranceservice\":\"N\",\"eawbKeyentrytime\":1629581092000,\"eawbKjtype\":\"D\",\"eawbPartition\":\"0\",\"eawbPaymentmode\":\"M\",\"eawbPickupAddress\":\"~~~No.6 The Third Section of Huangdong Golden Phoenix Road\",\"eawbPickupContact\":\"Yan Chen\",\"eawbPickupPhone\":\"***********\",\"eawbPickupPostcode\":\"523690\",\"eawbPickupTime\":*************,\"eawbPpcc\":\"PP\",\"eawbPreservation\":\"N\",\"eawbPrintcode\":\"***********\",\"eawbProducturl\":\"http://www.aliexpress.com/item//33020008137.html\",\"eawbQuantity\":1,\"eawbReference1\":\"DY509720683PT\",\"eawbReference2\":\"SN00463789659515\",\"eawbRefundAddress\":\"~~~金凤凰第二工业区 金凤凰大道黄洞段三路6号 润步科技C栋二楼\",\"eawbRefundCity\":\"东莞市\",\"eawbRefundCountry\":\"中国\",\"eawbRefundDistrict\":\"凤岗镇\",\"eawbRefundEmail\":\"<EMAIL>\",\"eawbRefundName\":\"陈燕\",\"eawbRefundPhone\":\"***********\",\"eawbRefundPrivince\":\"广东省\",\"eawbRefundWangwangId\":\"cn1519583555bqlu\",\"eawbRefundZipcode\":\"523690\",\"eawbSelfinsurance\":\"N\",\"eawbServiceItemid\":\"AE_4PL_STANDARD\",\"eawbServicetype\":\"CAINIAO_CTT_PRT\",\"eawbServicetypeOriginal\":\"L_AE_STANDARD_SINOTRANS_CTT_PORTUGAL_BAT\",\"eawbShipperAccount\":\"********\",\"eawbShipperAccountname\":\"Yan Chen\",\"eawbShipperCaccountname\":\"菜鸟物流\",\"eawbShipperWangwangId\":\"cn1519583555bqlu\",\"eawbSoCode\":\"********\",\"eawbSpecification\":\"FM Transmitters\",\"eawbStatus\":\"ON\",\"eawbSyscode\":-**********,\"eawbTotalchargeableweight\":0.12,\"eawbTotalgrossweight\":0.12,\"eawbTotalpieces\":1,\"eawbTotalvolume\":0.12,\"eawbTransmodeid\":\"CN_PRT\",\"eawbTrunkCode\":\"TRUNK_13453341\",\"eawbType\":\"INTERNATIONAL\",\"eawbUndeliveryOption\":\"1\",\"eawbUnit\":\"个\",\"eawbUnitcode\":\"035\",\"eawbtransmodeidoriginal\":\"CN_PRT\",\"estCode\":\"ND\",\"pkgOrdersAmount\":18,\"pkgPrintcode\":\"PTCTT0000019564\",\"pkgWeight\":6.15,\"updateStatus\":\"package\"}";

    private static final String expressItemsStr = "[{\"eawbPrintcode\":\"***********\",\"eawbSyscode\":-**********,\"ecCustdeclval\":22.28,\"ecCustdeclvalEdit\":22.28,\"ecCustprodenname\":\"Vehicle MP3\",\"ecCustprodennameEdit\":\"Vehicle MP3\",\"ecCustprodname\":\"车载MP3\",\"ecHandletime\":*************,\"ecHscode\":\"**********\",\"ecProducturl\":\"http://www.aliexpress.com/item//33020008137.html\",\"ecQuantity\":1,\"ecSpecification\":\"FM Transmitters\",\"ecSyscode\":-1596511073,\"pid\":\"33020008137\"}]";

    public static void main(String[] args) {

        GlobalParcelNetworkPrintDataPdfInfoRequest globalParcelNetworkPrintDataPdfInfoRequest = new GlobalParcelNetworkPrintDataPdfInfoRequest();
        globalParcelNetworkPrintDataPdfInfoRequest.setPrintType(1);
        globalParcelNetworkPrintDataPdfInfoRequest.setUserId(havanaId);
        globalParcelNetworkPrintDataPdfInfoRequest.setOrderCode("LP00465308964536");
        GlobalParcelNetworkPrintDataPdfInfoResponse globalParcelNetworkPrintDataPdfInfoResponse = CaiNiaoCAndDUtil.globalParcelNetworkPrintDataPdfInfo(logisticProviderId,globalParcelNetworkPrintDataPdfInfoRequest);
        System.out.println(globalParcelNetworkPrintDataPdfInfoResponse.getSuccess());

//        MailnoQueryServiceRequest mailnoQueryServiceRequest = new MailnoQueryServiceRequest();
//        mailnoQueryServiceRequest.setBizSource("OPEN");
//        mailnoQueryServiceRequest.setCnId(havanaId);
//        mailnoQueryServiceRequest.setOrderCode("LP00465308964536");
//        MailnoQueryServiceResponse mailnoQueryServiceResponse = CaiNiaoCAndDUtil.mailnoQueryService(logisticProviderId,mailnoQueryServiceRequest);
//        System.out.println(mailnoQueryServiceResponse.getSuccess());

//        Eawbpre eawbpre = JSONObject.parseObject(eawbpreStr,Eawbpre.class);
//        List<Expressitem> expressitems = JSONObject.parseArray(expressItemsStr,Expressitem.class);
//        CainiaoGlobalTakingOrderRequest cainiaoGlobalTakingOrderRequest = decorateCainiaoGlobalTakingOrderRequestByEawbpre(eawbpre,expressitems);
//        CainiaoGlobalTakingOrderRequest cainiaoGlobalTakingOrderRequest = JSONObject.parseObject(cainiaoGlobalTakingOrderRequestJsonStr,CainiaoGlobalTakingOrderRequest.class);
//        System.out.println(JSONObject.toJSONString(cainiaoGlobalTakingOrderRequest));
//        CainiaoGlobalTakingOrderResponse cainiaoGlobalTakingOrderResponse = CaiNiaoCAndDUtil.cainiaoGlobalTakingOrder(logisticProviderId,cainiaoGlobalTakingOrderRequest);
//        System.out.println(cainiaoGlobalTakingOrderResponse.getSuccess());

    }


}