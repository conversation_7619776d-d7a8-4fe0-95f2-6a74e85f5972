package com.sinoair.labe;

import com.itextpdf.text.DocumentException;
import com.sinoair.ceop.service.impl.WishCreatLabelServiceImpl;
import com.sinoair.core.labelsupport.base.exception.SinoairException;
import com.sinoair.core.labelsupport.domain.dto.LabelDTO;
import com.sinoair.core.labelsupport.label.SinoairLabelEngine;
import com.sinoair.core.labelsupport.utils.LogUtil;
import com.sinoair.core.utils.FileUtil;
import com.sinoair.core.utils.PropertiesUtil;
import org.apache.commons.codec.binary.Base64;

import java.io.File;
import java.io.FileOutputStream;
import java.io.IOException;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-09-02
 * @time: 14:40
 * @description: To change this template use File | Settings | File Templates.
 */
public class LabelTestPrint {
    public static void main(String[] args) throws DocumentException, IOException, SinoairException {
        //testPdf();
//        testWishB2bBox();
        String str = "";
//        ;
        File f = FileUtil.base64GenerateFile(str);
        System.out.println(f.getAbsolutePath());
        //法国邮政
//        Templatecolombia_A();
        //法国prive
        //loadSINOFRTRA_B();
        //loadwishCTPOSTOM_A();
    }

    private static void testWishB2bBox() throws DocumentException, IOException, SinoairException {
        String code = "1";
        String padded = String.format("%07d", Integer.parseInt(code));
        String newPkgPrintcode = "HUSNW100000"+padded;

        Map<String,Object> map = new HashMap<>();
        LabelDTO label = new LabelDTO();
        label.setReferenceNo(newPkgPrintcode);
        label.setServiceCode("SNW");
        map.put("title","(HU)HUNGARY");
        map.put("pkgPrintcode",newPkgPrintcode );
        map.put("CountryInCNES","匈牙利");
        label.setExtendData(map);
        SinoairLabelEngine.initLabel(label);
        String bs =  label.getLabelBinStr();

        File file = FileUtil.base64GenerateFile(bs);
        String filepath = file.getAbsolutePath();
        System.out.println(filepath);
    }

    private static void testPdf() throws DocumentException, IOException, SinoairException {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("TEST");
        label.setTrackingNo("123456");
        SinoairLabelEngine.initLabel(label);
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        fileOutputStream = new FileOutputStream("d://TEST.pdf");
        fileOutputStream.write(out);
        fileOutputStream.close();
        LogUtil.out("生成文件：d://TEST.pdf");
        fileOutputStream.close();
    }

    /**
     * 法国标准 法邮类型面单
     */
    private static void loadSINOFRTRA_A() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("SINOFRTRA");
        label.setSequenceNo("1");
        label.setConsigneeFullName("Carole Gay");
        label.setConsigneeAddr1("4 allée des orchidées");
        label.setConsigneeState("Caumont sur durance");
        label.setConsigneeZipCode("84510");
        label.setConsigneePhone("06 75 77 29 92");
        label.setPackageTotalWeight(Double.valueOf(1500));
        label.setReferenceNo("BG-006185359_1hVc75");
        label.setConsigneeFullName("Carole Gay");
        label.setConsigneeCity("Notre Dame De Mesage");
        final HashMap<String, Object> extendData = new HashMap<>();
        extendData.put("labelType", "SINOFRTRA_A");
        extendData.put("sequenceNo4Colissimo", "**********");
        extendData.put("routing", "14130DH121475FFF");
        extendData.put("version", "**************");
        label.setExtendData(extendData);
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        LogUtil.out("追踪号：" + label.getTrackingNo());
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream("d://testtestSINOFRTRA_A.pdf");
            fileOutputStream.write(out);
            fileOutputStream.close();
            LogUtil.out("生成文件：d://testSINOFRTRA_A.pdf");
        } catch (java.io.IOException e) {
            try {
                fileOutputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
    }

    /**
     * 法国标准 prive类型面单
     */
    private static void loadSINOFRTRA_B() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("SINOFRTRA");
        label.setSequenceNo("1");
        label.setConsigneeFullName("中文xMan xMan xMan xMan xMan xMan xMan xMan xMan xMan xMan xMan xMan xMan ");
        label.setConsigneeAddr1("29BIS AVENUE DU 1ER MAI");
        label.setConsigneeState("TARNOS");
        label.setConsigneeCity("TARNOS");
        label.setConsigneeZipCode("92700");
        label.setConsigneePhone("012345678901234567890123456789012345678901234567890123456789");
        label.setPackageTotalWeight(Double.valueOf(521));
        label.setReferenceNo("BG-006189833_1hVnut");
        final HashMap<String, Object> extendData = new HashMap<>();
        extendData.put("labelType", "SINOFRTRA_B");
        extendData.put("sequenceNo4Colissimo", null);
        extendData.put("routing", "92700DH189255OOF");
        extendData.put("version", "**************");
        label.setExtendData(extendData);

        label.setReturnAddr1("SINOTRANS WB-D");
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        LogUtil.out("追踪号：" + label.getTrackingNo());
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream("d://testSINOFRTRA_B.pdf");
            fileOutputStream.write(out);
            fileOutputStream.close();
            LogUtil.out("生成文件：d://testSINOFRTRA_B.pdf");
        } catch (java.io.IOException e) {
            try {
                fileOutputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
    }

    private static void loadSINOESNON() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("SINOESNON");
        label.setSequenceNo("8");
        label.setConsigneeFullName("belen rodriguez");
        label.setConsigneeAddr1("Avenida Adoratrices N59 Avenida Adoratrices N50 4a");
        label.setConsigneeState("Huelva");
        label.setConsigneeCity("Huelva");
        label.setConsigneeZipCode("21004");
        label.setConsigneePhone("0101234567");
        label.setReferenceNo("F3289118111403HN");
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        LogUtil.out("追踪号：" + label.getTrackingNo());
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream("d://testSINOESNON.pdf");
            fileOutputStream.write(out);
            fileOutputStream.close();
            LogUtil.out("生成文件：d://testSINOESNON.pdf");
        } catch (java.io.IOException e) {
            try {
                fileOutputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
    }

    /**
     * 西班牙挂号
     */
    private static void loadSINOESTRA() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("SINOESTRA");
        label.setSequenceNo("1999999");
        label.setConsigneeFullName("belen rodriguez");
        label.setConsigneeAddr1("Avenida Adoratrices N59 Avenida Adoratrices N50 4a");
        label.setConsigneeState("Huelva");
        label.setPackageTotalWeight(Double.valueOf(521));
        label.setConsigneeCity("Huelva");
        label.setConsigneeZipCode("21004");
        label.setConsigneePhone("0101234567");
        label.setReferenceNo("F32891181110001PAQ");
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        LogUtil.out("追踪号：" + label.getTrackingNo());
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream("d://testSINOESTRA.pdf");
            fileOutputStream.write(out);
            fileOutputStream.close();
            LogUtil.out("生成文件：d://testSINOESNON.pdf");
        } catch (java.io.IOException e) {
            try {
                fileOutputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
    }

    private static void loadSINOITTRA() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("SINOITTRA");
        label.setSequenceNo("2");
        label.setConsigneeFullName("michela daina");
        label.setConsigneeAddr1("avia provinciale 78 a a a a a a a a a  a a a a a  a a a a a a  a a a a a a a a a  a a a a a a a a a a  aa aa a a a a a a");
        label.setConsigneeState("Huelva");
        label.setConsigneeCity("DALMINE");
        label.setConsigneeZipCode("24044");
        label.setConsigneePhone("+00390-1 0139234567");
        label.setReferenceNo("F3289118111403HN");
        Map<String, Object> map = new HashMap<>();
        List<String> list = new ArrayList<>();
        list.add("T-shirt,T-shirt,T-shirt,T-shirt,T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt，T-shirt");
        map.put("skuDescList", list);
        label.setExtendData(map);
        label.setPackageTotalWeight(230.3);
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        LogUtil.out("追踪号：" + label.getTrackingNo());
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream("d://testSINOITTRA.pdf");
            fileOutputStream.write(out);
            fileOutputStream.close();
            LogUtil.out("生成文件：d://testSINOESNON.pdf");
        } catch (java.io.IOException e) {
            try {
                fileOutputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
    }

    private static void loadwishCTPOSTOM_A() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("wishCZPOSTOM");
        label.setSequenceNo("2");
        label.setConsigneeFullName("číslo kreditu");
        label.setConsigneeAddr1("avia provinciale 78 a a a a a a a ");
        label.setConsigneeState("Huelva");
        label.setConsigneeCity("DALMINE");
        label.setConsigneeZipCode("24044");
        label.setConsigneePhone("+00390-1 0139234567");
        label.setReferenceNo("F3289118111403HN");
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        LogUtil.out("追踪号：" + label.getTrackingNo());
        byte[] out = Base64.decodeBase64(label.getLabelBinStr().getBytes());
        FileOutputStream fileOutputStream = null;
        try {
            fileOutputStream = new FileOutputStream("D:\\sdk\\label\\testCZPOSTOM.pdf");
            fileOutputStream.write(out);
            fileOutputStream.close();
            LogUtil.out("生成文件：D:\\sdk\\label\\testCZPOSTOM.pdf");
        } catch (java.io.IOException e) {
            try {
                fileOutputStream.close();
            } catch (IOException e1) {
                e1.printStackTrace();
            }
            e.printStackTrace();
        }
    }

    //哥伦比亚面单
    private static void Templatecolombia_A() {
        LabelDTO label = new LabelDTO();
        label.setServiceCode("colombia");
        label.setLabelType("CORREO CERTIFICADO NACIONAL E-COMMERCE");
        label.setProductShortname("13437008");
        label.setDate("2021-03-03 11:20:00");
        label.setTaxid("0000000");
        label.setReference2("WOSP000000001");
        label.setSellerPhone("00000001");
        label.setSellerZipCode("*********");
        label.setOperationSender("1111669");
        label.setConsigneeFullName("Jose Rivera Fuente");
        label.setConsigneeAddr1("Cra #No.26-57");
        label.setConsigneePhone("1234567890");
        label.setConsigneeZipCode("*********");
        label.setConsigneeCity("MANIZALES_CALDAS");
        label.setConsigneeState("CALDAS");
        label.setOperationReceiver("5555370");
        label.setPackageTotalWeight(65.0);
        label.setPackageTotalValue(10.0);
        label.setCustprodenname("watch");
        label.setReferenceNo("WU000000368CO");
        label.setSequenceNo("11116695555370WU000000368CO");
        try {
            SinoairLabelEngine.initLabel(label);
        } catch (SinoairException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } catch (DocumentException e) {
            e.printStackTrace();
        }
        String base64 = label.getLabelBinStr();
        File file = FileUtil.base64ToFile(base64);
        System.out.println("file---------" + file.getPath());
    }

}
