package com.sinoair.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.domain.Constant;
import com.sinoair.ceop.domain.SurelineConstant;
import com.sinoair.core.utils.sureline.SurelineFileUtil;
import org.junit.Test;

import java.io.File;
import java.util.UUID;

/**
 * @author: XueQB
 * @date: 2020/7/9 16:52
 */
public class FileUtilTest {

    @Test
    public void uploadFileContainChineseFileName() {

        String uuid = FileUtil.uploadFileContainChineseFileName(
                new File("D:\\temp\\浦东清关报文_86020200331.csv"), null, Constant.FILE_SERVER_UPLOAD_URL);
        System.out.println(Constant.FILE_SERVER_UPLOAD_URL + "---" + uuid);
    }

    @Test
    public void testCreateFile() {
        FileUtil.createEmptyFile(UUID.randomUUID().toString() + "-主单号为空.txt");
    }


    /**
     * 测试zpl转为pdf 并上传到文件服务器
     *
     * @throws Exception
     */
    @Test
    public void testZpl2PdfAndUploadEcdown() throws Exception {
        String zplstr = "\u0002M~JO\n" +
                "^XA^EG^XZ\n" +
                "^XA\n" +
                "^PMN\n" +
                "^MUD\n" +
                "^LH15,0\n" +
                "^PR6\n" +
                "^JMA\n" +
                "^FO10,946,^GB616,137,0^FS\n" +
                "^FO10,703,^GB616,244,0^FS\n" +
                "^FO24,743,^A0N,43^FR^FDPost Office Praha 025 (Depo Praha 701)^FS\n" +
                "^FO24,857,^A0N,43^FR^FDSazecska 603/9^FS\n" +
                "^FO24,903,^A0N,31^FR^FDCZ^FS\n" +
                "^FO112,1052,^A0N,24^FR^FD753111171353241^FS\n" +
                "^FO18,953,^A0N,24^FR^FDContact^FS\n" +
                "^FO18,978,^A0N,24^FR^FDPhone^FS\n" +
                "^FO18,1003,^A0N,24^FR^FDNote^FS\n" +
                "^FO112,953,^A0N,24^FR^FDPost Office Praha 025 (Depo Praha 701)^FS\n" +
                "^FO112,978,^A0N,24^FR^FD+*********** 712^FS\n" +
                "^FO10,444,^GB616,260,0^FS\n" +
                "^FO18,1028,^A0N,24^FR^FDNote^FS\n" +
                "^FO625,444,^GB162,639,0^FS\n" +
                "^FO100,903,^A0N,31^FR^FD22500^FS\n" +
                "^FO280,903,^A0N,31^FR^FDPrague-Malesice^FS\n" +
                "^FO630,514,^A0R,18^FR^FD4460^FS\n" +
                "^FO630,607,^A0R,18^FR^FDGRACE-HOLLOGNE^FS\n" +
                "^FO754,451,^A0R,18^FR^FDSender^FS\n" +
                "^FO727,451,^A0R,24^FR^FDSINOTRANS AIR TRANSPORTATION BELGIAN COMPANY^FS\n" +
                "^FO754,572,^A0R,18^FR^FDCustomer ID^FS\n" +
                "^FO754,692,^A0R,18^FR^FD0560012745^FS\n" +
                "^FO754,840,^A0R,18^FR^FDContact ID^FS\n" +
                "^FO754,946,^A0R,18^FR^FD056aaacL2g^FS\n" +
                "^FO654,451,^A0R,18^FR^FDRUE SAINT EXUPERY 9^FS\n" +
                "^FO630,451,^A0R,18^FR^FDBE^FS\n" +
                "^FO570,12,^GB224,90,90^FS\n" +
                "^FO248,13,^GB71,89,71^FS\n" +
                "^FO4,5,^GB790,9,9^FS\n" +
                "^FO27,120,^A0N,62^FR^FD1616^FS\n" +
                "^FO183,139,^A0N,43^FR^FD22500^FS\n" +
                "^FO25,28,^A0N,74^FR^FDHUM^FS\n" +
                "^FO450,30,^A0N,74^FR^FDCZ^FS\n" +
                "^FO7,102,^GB787,6,6^FS\n" +
                "^FO263,25,^A0N,80^FR^FD0^FS\n" +
                "^FO216,110,^A0N,18^FR^FDZipCode^FS\n" +
                "^FO384,110,^A0N,18^FR^FDYour GLS Track ID^FS\n" +
                "^FO387,147,^A0N,34^FR^FDZI6G3MQT^FS\n" +
                "^FO8,184,^GB787,6,6^FS\n" +
                "^FO8,395,^GB783,9,9^FS\n" +
                "^FO50,217,^BY4^BXN,4,200^FR^FDABE 610CZ00160560012745056aaacL2gZI6G3MQTCCe         0HUM161622500  03000002001*********                                   ^FS\n" +
                "^FO595,214,^BY4^BXN,4,200^FR^FDA|Post Office Praha 025 (Depo Pra|Sazecska 603/9|Prague-Malesice||753111171353241||                              ^FS\n" +
                "^FO8,190,^GB24,8,8^FS\n" +
                "^FO8,196,^GB8,16,8^FS\n" +
                "^FO8,372,^GB8,16,8^FS\n" +
                "^FO8,387,^GB24,8,8^FS\n" +
                "^FO229,387,^GB24,8,8^FS\n" +
                "^FO244,196,^GB8,16,8^FS\n" +
                "^FO228,190,^GB24,8,8^FS\n" +
                "^FO245,372,^GB8,16,8^FS\n" +
                "^FO580,28,^A0N,80^FR^FD0016^FS\n" +
                "^FO303,352,^A0N,37^FR^FD612077583097^FS\n" +
                "^FO720,416,^A0N,18^FR^FDB2.00.0^FS\n" +
                "^FO616,416,^A0N,18^FR^FD17112020^FS\n" +
                "^FO168,416,^A0N,18^FR^FD18.11.2020^FS\n" +
                "^FO32,408,^A0N,31^FR^FDBE 610^FS\n" +
                "^FO342,408,^A0N,37^FR^FD30.00^FS\n" +
                "^FO131,407,^A0N,31^FR^FDdi^FS\n" +
                "^FO273,416,^A0N,18^FR^FD08:09^FS\n" +
                "^FO481,416,^A0N,18^FR^FD1^FS\n" +
                "^FO525,416,^A0N,18^FR^FD2^FS\n" +
                "^FO510,416,^A0N,18^FR^FD/^FS\n" +
                "^FO572,416,^A0N,18^FR^FDRTG^FS\n" +
                "^FO268,221,^BY3,2.0,125,^B2N,125,N,N^FR^FD612077583097^FS\n" +
                "^FO624,135,^A0N,43^FR^FDF^FS\n" +
                "^FO735,135,^A0N,43^FR^FDX^FS\n" +
                "^FO425,407,^A0N,37^FR^FDkg^FS\n" +
                "^FO18,1090,^A0N,20^FR^FDInformace o ochrane osobních údaju v síti GLS Group naleznete na^FS\n" +
                "^FO18,1115,^A0N,20^FR^FDgls-group.eu/dataprotection^FS\n" +
                "^PQ1\n" +
                "^XZ\n" +
                "\\\\\\\\\\GLS\\\\\\\\\\T050:Shipping Software|T051:v1.5.2|T330:22500|T400:612077583097|T545:18.11.2020|T805:*********|T800:Sender|T810:SINOTRANS AIR TRANSPORTATION BELGIAN COMPANY|T811:|T812:|T820:RUE SAINT EXUPERY 9|T821:BE|T822:4460|T823:GRACE-HOLLOGNE|T854:753111171353241|T860:Post Office Praha 025 (Depo Praha 701)|T861:|T862:|T863:Sazecska 603/9|T864:Prague-Malesice|T759:Post Office Praha 025 (Depo Praha 701)|T758:+*********** 712|T1229:<EMAIL>|T920:|T921:|T8700:BE 610|T8904:1|T8905:2|T8914:056aaacL2g|T8915:0560012745|T080:V214_17_0002|T520:17112020|T510:di|T500:BE 610|T103:BE 610|T560:BE01|T8797:IBOXCUS|T540:18.11.2020|T541:08:09|T100:CZ|CTRA2:CZ|T1945:Informace o ochrane osobních údaju v síti GLS Group naleznete na|T1946:gls-group.eu/dataprotection|T210:|ARTNO:Standard|T530:30.00|T205:X|T206:EBP|ALTZIP:22500|FLOCCODE:CZ0016|TOURNO:1616|T320:1616|TOURTYPE:21102|SORT1:0|T310:0|T331:22500|T890:9203|ROUTENO:1164244|ROUTE1:HUM|T110:HUM|FLOCNO:1082|T101:0016|T105:CZ|T300:20301605|T207:FDF|T202:F|NDI:|T8970:A|T8971:A|T8975:*********|T8980:CC|T8974:e|T8916:612077583097|T8950:Tour|T8951:ZipCode|T8952:Your GLS Track ID|T8953:Product|T8954:Service Code|T8955:Delivery address|T8956:Contact|T8958:Contact|T8957:Customer ID|T8959:Phone|T8960:Note|T8961:Parcel|T8962:Weight|T8965:Contact ID|T8913:ZI6G3MQT|T8972:ZI6G3MQT|T8902:ABE 610CZ00160560012745056aaacL2gZI6G3MQTCCe         0HUM161622500  03000002001*********                                   |T8903:A¬Post Office Praha 025 (Depo Pra¬Sazecska 603/9¬Prague-Malesice¬¬753111171353241¬¬                              |T102:CZ0016|PRINTINFO:|PRINT1:|RESULT:E000:612077583097|T565:080938|PRINT0:xxGLSzebrazpl200.int01|/////GLS/////";
        String trackingNum = "TTTT";
        //把ZPL转为pdf
        File zplFile = FileUtil.createFileUTF8("GLSLabel", new StringBuffer(zplstr), trackingNum);
        byte[] pdf = HttpUtils.zpl2pdf(zplFile);
        File file = SurelineFileUtil.savePdfFile("GLSLabel", pdf, trackingNum);

        JSONObject uploadResult_pdf = JSONObject.parseObject(SurelineFileUtil.uploadFile(file, null, SurelineConstant.SINOAIR_EC_UPLOAD));
        String uuid_pdf = uploadResult_pdf.getString("uuid");

        if (uuid_pdf == null || "".equals(uuid_pdf)) {
            String msg = "This order (" + trackingNum + ") save pdf label failed.";
            System.out.println(msg);
        } else {
            FileUtil.deleteFile(file);
            //uuid_pdf
            System.out.println(SurelineConstant.SINOAIR_EC_DOWNLOAD + uuid_pdf);
        }
    }
}