## ceos 移动端接口规范

http://xxx.xxx.xxx<:port>/mobile/xxx 请求方式：POST
>
请求模板： DATA= { token 用户标识 timestamp 请求时间戳：2016-08-11 13:34:11 reqData 消息体：----- (JSON object)
}

>
响应模板： respCode 响应编码： 0000：请求成功 0001：请求失败 0002：请求内容不合法 0003：token错误 0004：验签失败 0005：reqData内容不合法 9999：服务器异常 timestamp
响应时间戳：2016-08-11 13:34:11 respData 响应消息体：---(json object)
message 消息状态描述

token校验： des3 用户id_用户名_登录时间

>
一、用户登录
http://xxx.xxx.xxx<:port>/mobile/mLogin 请求：userName:用户名 password:密码

    请求参数 DATA 
    参数内容如下：
    demo:
     请求：   {
                "token":"",
                 "reqData":{"userName":"xxxx","password":"xxxxxx"},
                 "timestamp":"2016-08-11 13:34:11"
               }
     响应：    {"respCode":"0000","respData":{"login_token":"ffs3ggyY7QkoEm2MwpaNzj4M6pG17BwqqFB4JYeFaOU=\n"},"message":"SUCCESS","timestamp":"2016-10-18 18:38:25"}

二、分拣装袋

    >>>扫描的是袋子号（L签）
    http://xxx.xxx.xxx<:port>/mobile/isPackage
    请求：   {
                    "token":"",
                     "reqData":{"pkgPrintCode":"xxxx","trunkingNumber":"xxxxxx"},
                     "timestamp":"2016-10-20 08:34:11"
                   }

         响应：  {"respCode":"0000",

                 "respData":{
                             "EawbTransmodeid":"xxxxxx",
                             "Transmodename":"xxxxxx",
                             "EawbOutboundSacId":"xxxxxx",
                             "EtrCode":"xxxxxx",
                             "EawbDestcountry":"xxxxxx",
                             "commonSortPackageVO": [
                                        "EawbPrintcode":"xxxxxx",
                                        "PkgPieces":"xxxxxx",
                                        "EawbTotalpieces":"xxxxxx",
                                        "EawbTotalgrossweight":"xxxxxx",
                                        "EawbDestcountry":"xxxxxx",
                                        "EawbReference1":"xxxxxx"]
                           },
                 "message":"SUCCESS",

                 "timestamp":"2016-10-18 18:38:25"
                 }

    >>>扫描的不是袋子号（跟踪号）
    http://xxx.xxx.xxx<:port>/mobile/isPackage
     请求：   {
                "token":"",
                 "reqData":{"pkgPrintCode":"xxxx","trunkingNumber":"xxxxxx"},
                 "timestamp":"2016-10-20 08:34:11"
               }

     响应：  {"respCode":"0000",
             "respData":{
                         "Transmodename":"xxxxxx",
                         "EawbDestcountry":"xxxxxx",
                         "commonSortPackageVO": [
                                    "EawbPrintcode":"xxxxxx",
                                    "EawbTotalgrossweight":"xxxxxx",
                                    "EawbReference1":"xxxxxx"]
                        },

             "message":"SUCCESS",

             "timestamp":"2016-10-18 18:38:25"
             }

三、荷邮分拣装袋

袋签的规则 以NL开头 var eawb_lablePatternNL = /^NL[a-z0-9A-Z]*$/;//NL荷邮换MBX袋签

小包的规则 以LX开头

荷兰邮政分拣装袋之前需要先执行该操作 url： /mobile/updateEawb4HY 参数 trackingNumber //扫描的跟踪号 serviceType 见 下面的 1 2 3

    >>> 请求：  
        {
        "token":"",
        "reqData":{"trackingNumber":"LX70829152028004","serviceType":"DISTRIBUTOR_MBXPXB"},
        "timestamp":"2016-10-20 08:34:11"
        }

原始的GPT DISTRIBUTOR_11180269

1 箱 DISTRIBUTOR_MBXPXB 中外运-荷邮MBXPXB 0-32 mm

<del>
2 超
DISTRIBUTOR_MBXPXN
中外运-荷邮MBXPXN  32-125 mm  </del>

3 裹 DISTRIBUTOR_MBXPXP 中外运-荷邮MBXPXP 125mm 以上

* 注： 2018年3月29日 因业务变更删除 超 这种类型

返回值：

* 0000 success

* S08 无追踪号

> 0002
> * server exception
> * 系统异常：搜索到多件，请联系管理员！
> * 操作异常：请选择装袋类型
----------------------------------

## 口岸配舱

### 0.获取渠道列表

获取当前用户下的所有渠道列表。 请求地址： /mobile/assignment/getTransmodes?DATA=
> 请求参数
<pre>
{
    "token": "rjwo1wq9e6p1Q7MDRv+hjSgSbYzClo3O7Hov5esPkGkd+z9Lawtj9Q==",
    "reqData": { },
    "timestamp": "2016-10-28 13:34:11"
}
</pre>
> 响应返回值
<pre>
{
    "respCode": "0000",
    "respData": {
        "transmodeList": [
            {
                "transline": "FR",
                "transmode": "FR",
                "sacId": "SNR",
                "transmodeid": "FRMB",
                "transmodename": "法国邮政",
                "stutas": "ON",
                "btCode": null,
                "transrout": null
            },
            {
                "transline": "FR",
                "transmode": "FR",
                "sacId": "SNR",
                "transmodeid": "FRM",
                "transmodename": "法国专线",
                "stutas": "ON",
                "btCode": null,
                "transrout": null
            },
            {
                "transline": "NETHERLANDS",
                "transmode": "NL",
                "sacId": "SNR",
                "transmodeid": "POSTNL",
                "transmodename": "荷兰邮政",
                "stutas": "ON",
                "btCode": null,
                "transrout": null
            },
            {
                "transline": "USA",
                "transmode": "US",
                "sacId": "SNR",
                "transmodeid": "USA_IBC",
                "transmodename": "美国IBC",
                "stutas": "ON",
                "btCode": null,
                "transrout": "USA"
            },
            {
                "transline": "AMERICA",
                "transmode": "AM",
                "sacId": "SNR",
                "transmodeid": "MAILAM",
                "transmodename": "美洲邮政",
                "stutas": "ON",
                "btCode": null,
                "transrout": null
            },
            {
                "transline": "UK",
                "transmode": "UK",
                "sacId": "SNR",
                "transmodeid": "UKGB",
                "transmodename": "英国专线",
                "stutas": "ON",
                "btCode": null,
                "transrout": "GB"
            },
            {
                "transline": "CHILE",
                "transmode": "CHI",
                "sacId": "SNR",
                "transmodeid": "CHILE",
                "transmodename": "智利邮政",
                "stutas": "ON",
                "btCode": null,
                "transrout": "CHILE"
            }
        ]
    },
    "message": "SUCCESS",
    "timestamp": "2016-11-02 10:05:19"
}
</pre>

### 1.获取总单和袋子

获取总单列表和该总单下的可配舱的袋子列表。

请求地址1：/mobile/assignment/getEaCodes?DATA= 请求地址2：/mobile/assignment/getPkgs?DATA=
> 请求参数
<pre>
{
    "token": "rjwo1wq9e6p1Q7MDRv+hjSgSbYzClo3O7Hov5esPkGkd+z9Lawtj9Q==",
    "reqData": { "transmodeid": "UKGB"},
    "timestamp": "2016-10-28 13:34:11"
}
</pre>
> 响应返回值
<pre>
{
    "respCode": "0000",
    "respData": {
        "packagesList": [
            {
                "pkgSyscode": 12514376,
                "pkgPrintcode": "L20161025001",
                "etrCode": null,
                "eptCode": null,
                "pkgRemark": null,
                "pkgEIdHandler": null,
                "pkgHandletime": 1477795010000,
                "eaCode": null,
                "sacId": null,
                "pkgSacCode": null,
                "pkgStatus": null,
                "pkgOpenstatus": null,
                "trsTransmodeid": "USA_IBC",
                "eawbOutboundSacId": null,
                "destName": "美国IBC",
                "btCode": null,
                "pkgPieces": 2,
                "pkgWeight": 22,
                "pkgActualweight": 22,
                "pkgPrintcodeReference": null,
                "pkgPrintStatus": null,
                "eawbPartition": null,
                "servicetype": null,
                "eaSyscode": null
            },
            {
                "pkgSyscode": 12514377,
                "pkgPrintcode": "L20161025002",
                "etrCode": null,
                "eptCode": null,
                "pkgRemark": null,
                "pkgEIdHandler": null,
                "pkgHandletime": 1477795045000,
                "eaCode": null,
                "sacId": null,
                "pkgSacCode": null,
                "pkgStatus": null,
                "pkgOpenstatus": null,
                "trsTransmodeid": "USA_IBC",
                "eawbOutboundSacId": null,
                "destName": "美国IBC",
                "btCode": null,
                "pkgPieces": 2,
                "pkgWeight": 24,
                "pkgActualweight": 24,
                "pkgPrintcodeReference": null,
                "pkgPrintStatus": null,
                "eawbPartition": null,
                "servicetype": null,
                "eaSyscode": null
            },
            {
                "pkgSyscode": 12514381,
                "pkgPrintcode": "L20161025003",
                "etrCode": null,
                "eptCode": null,
                "pkgRemark": null,
                "pkgEIdHandler": null,
                "pkgHandletime": 1477810125000,
                "eaCode": null,
                "sacId": null,
                "pkgSacCode": null,
                "pkgStatus": null,
                "pkgOpenstatus": null,
                "trsTransmodeid": "USA_IBC",
                "eawbOutboundSacId": null,
                "destName": "美国IBC",
                "btCode": null,
                "pkgPieces": 4,
                "pkgWeight": 46,
                "pkgActualweight": 46,
                "pkgPrintcodeReference": null,
                "pkgPrintStatus": null,
                "eawbPartition": null,
                "servicetype": null,
                "eaSyscode": null
            },
            {
                "pkgSyscode": 12514382,
                "pkgPrintcode": "L20161025010",
                "etrCode": null,
                "eptCode": null,
                "pkgRemark": null,
                "pkgEIdHandler": null,
                "pkgHandletime": 1477814376000,
                "eaCode": null,
                "sacId": null,
                "pkgSacCode": null,
                "pkgStatus": null,
                "pkgOpenstatus": null,
                "trsTransmodeid": "USA_IBC",
                "eawbOutboundSacId": null,
                "destName": "美国IBC",
                "btCode": null,
                "pkgPieces": 4,
                "pkgWeight": 46,
                "pkgActualweight": 46,
                "pkgPrintcodeReference": null,
                "pkgPrintStatus": null,
                "eawbPartition": null,
                "servicetype": null,
                "eaSyscode": null
            }
        ],
        "notFinishExpressAssignmentList": [
            {
                "eaSyscode": 209589,
                "eaCode": "555-12345675",
                "flightNumber": "12",
                "oriSacId": "HGH",
                "sacId": "HGH",
                "eaTotalEawbs": 3,
                "eaTotalMawbs": 12,
                "mawbPWeight": 999999,
                "mawbCWeight": 1,
                "flightDate": null,
                "eta": null,
                "destSacId": "DDD",
                "remark": null,
                "eaHandletime": 1476176679000,
                "eaStatus": "2",
                "transmodeid": "USA_IBC",
                "packagesList": [ ],
                "messages": ""
            },
            {
                "eaSyscode": 209606,
                "eaCode": "555-12345683",
                "flightNumber": "2",
                "oriSacId": "HGH",
                "sacId": "HGH",
                "eaTotalEawbs": 3,
                "eaTotalMawbs": 3,
                "mawbPWeight": 2,
                "mawbCWeight": 36,
                "flightDate": null,
                "eta": null,
                "destSacId": "DDD",
                "remark": null,
                "eaHandletime": 1476949104000,
                "eaStatus": "2",
                "transmodeid": "USA_IBC",
                "packagesList": [ ],
                "messages": ""
            },
            {
                "eaSyscode": 209591,
                "eaCode": "999-97297636",
                "flightNumber": "CA001",
                "oriSacId": "HGH",
                "sacId": "HGH",
                "eaTotalEawbs": 2,
                "eaTotalMawbs": 1,
                "mawbPWeight": 21,
                "mawbCWeight": 21,
                "flightDate": null,
                "eta": null,
                "destSacId": "LAX",
                "remark": null,
                "eaHandletime": 1476686468000,
                "eaStatus": "2",
                "transmodeid": "USA_IBC",
                "packagesList": [ ],
                "messages": ""
            },
            {
                "eaSyscode": 209625,
                "eaCode": "695-09663220",
                "flightNumber": "BR0012",
                "oriSacId": "HGH",
                "sacId": "HGH",
                "eaTotalEawbs": 10,
                "eaTotalMawbs": 10,
                "mawbPWeight": 841.9,
                "mawbCWeight": 841.9,
                "flightDate": 1477584000000,
                "eta": 1477584000000,
                "destSacId": "LAX",
                "remark": null,
                "eaHandletime": 1477639875000,
                "eaStatus": "2",
                "transmodeid": "USA_IBC",
                "packagesList": [ ],
                "messages": ""
            }
        ]
    },
    "message": "SUCCESS",
    "timestamp": "2016-11-02 10:17:47"
}
</pre>

### 2.保存该总单

保存当前选择的总单和已经选中的该总单下的袋子列表。 请求地址：/mobile/assignmentsaveExpressassignment?DATA=
> 请求参数
<pre>
{
    "token": "rjwo1wq9e6p1Q7MDRv+hjSgSbYzClo3O7Hov5esPkGkd+z9Lawtj9Q==",
    "reqData": {
        "eaSyscode": 209589,//总单ID
        "pkgSyscodes": [12514377,12514376]//袋子ID列表
        },
    "timestamp": "2016-10-28 13:34:11"
}
</pre>


> 返回值
>> 配舱成功 <br>
> > 配舱失败。<br>
<b>[注] 是否重新请求总单列表</b>
<pre>

</pre>


<pre>
  
 关务清关 绑定大包关系接口
http://localhost:8080/mobile/mBoundPackage

eaCode:支持多个总单上传
pkgPrintcodes：支持多个大包上传

{
    "token": "ffs3ggyY7QnOzYeF5zMgTglAyeKld7XX004s2tFRkk0",
    "reqData": {
        "truckNumber": "港B88888010",
        "eaCode": "777-98989898,",
        "pkgPrintcodes": "L60020150601,L00006195171"
    },
    "timestamp": "2017-09-27 13:34:11"
}
</pre>

## 菜鸟中东线大包入库和调用interchange

    功能：中东转运=大包入库+调用interchange（生成发票\向aramex下单）
    
    http://localhost:8080/mobile/mPushAramexPackage
    
    DATA的值为：
    <pre>
    {
        "token": "ffs3ggyY7QnOzYeF5zMgTglAyeKld7XX004s2tFRkk0",
        "reqData": {
            "flag": "ALL"|"ONE",
            "pkgCode": "JAE1810250009,",
            "ref1s": ["86,L00006195171"],
            "changeServiceType": "DISTRIBUTOR_IQS"|"TRUNK_13451487"
        },
        "timestamp": "2018-10-26 13:34:11"
    }
    
    </pre>
    
    flag 默认值 ALL
    pakCode 大包号
    ref1s 默认为空数组
    changeServiceType 
        DISTRIBUTOR_IQS=IQS线路
        TRUNK_13451487=中东aramex
        
        
    返回值：
    
        0000  SUCCESS
        9999  SERVER EXCEPTION
        0001  
            请选择要转换为哪个线路！
            请扫描单号！
            没有大包号！
            不支持的线路！
            开发中尚未实现！
            已经部分入库了，不支持该操作！
            转到IQS失败！
            调用interchange失败！    

## 菜鸟中东线 中东分拣

功能：中东分拣：初次分拣线路

http://localhost:8080/mobile/mSelectLine

DATA的值为：
<pre>
{
    "token": "ffs3ggyY7QnOzYeF5zMgTglAyeKld7XX004s2tFRkk0",
    "reqData": {
        "no": "8600001"
    },
    "timestamp": "2018-10-26 13:34:11"
}

</pre>

返回值：
<pre>   
    {
        "respCode": "0000",
        "respData": {
            "destcountry":"SA|AE",
            "servicetype": "IQS"|"Aramex",
            "partition":"JED|RUH|……"
        },
        "message": "SUCCESS",
        "timestamp": "2016-11-02 10:17:47"
    }
</pre>
返回值：

    0000  SUCCESS
    9999  SERVER EXCEPTION
    
    0005  
        请扫描单号！
    0001  
        查无此订单！
    ERR-001 
        黑名单ID，请检查！
        含违禁品请拦截，（中文品名）
        无品名，请拦截！
        高货值，请拦截

## 菜鸟中邮-俄邮

### 0.1 板载信息获取

功能：输入板号后获取板上的信息 如果有暂存的信息，需要保存这些大包 URL：http://localhost:8080/mobile/mScanChinapostPackage/getInfo

请求报文：
<pre>
{
	"token": "C2QSWkl7O5jyv0o/ZmNGX4kbMvTQfQCd0vpi4txeyKNMWZq5FLe4IA==\n",
	"reqData": {
		"packages": [{
			"pkgCode": "CNWUHDJPTYOAAEM85223005100025",
			"pkgSource": "NCP|CP"
		}, {
			"pkgPrintcode": "CNWUHDJPTYOAAEM85223005100025",
			"pkgSource": "NCP|CP"
		}],
		"palletSyscode": "",
		"palletPrintcode": "PP01",
		"palletCount": "",
		"mode": "IN"
	},
	"timestamp": "2018-10-26 13:34:11"
}

</pre>
返回值：
<pre>
{
	"respCode": "0000",
	"respData": {
		"packages": [{
			"pkgCode": "CNWUHDJPTYOAAEM85223005100025",
			"pkgSource": "NCP|CP"
		}, {
			"pkgPrintcode": "CNWUHDJPTYOAAEM85223005100025",
			"pkgSource": "NCP|CP"
		}],
		"palletSyscode": "123",
		"palletPrintcode": "PP01",
		"palletCount": "2"
	},
	"message": "SUCCESS",
	"timestamp": "2019-10-14 13:08:05"
}
</pre>

### 0.2 中邮大包扫描

功能：扫描邮政的大包号CN35入库，不做大包扫描入库动作
http://localhost:8080/mobile/mScanChinapostPackage/new

DATA的值为：
<pre>
{
	"token": "C2QSWkl7O5jyv0o/ZmNGX4kbMvTQfQCd0vpi4txeyKNMWZq5FLe4IA==\n",
	"reqData": {
		"packages": [{
			"pkgCode": "CNWUHDJPTYOAAEM85223005100025",
			"pkgSource": "NCP|CP"
		}],
		"palletSyscode": "123",
		"palletPrintcode": "PP01",
		"palletCount": "10",
		"mode": "IN/RTN",
		"checkCNData":"YES/NO",
        "palletType":"Y",
        "palletDest":"OVB"
	},
	"timestamp": "2018-10-26 13:34:11"
}

</pre>

返回值：
<pre>
{
	"respCode": "0000",
	"respData": {
		"palletSyscode": "123",
		"palletPrintcode": "PP01",
		"palletCount": "2"
	},
	"message": "SUCCESS",
	"timestamp": "2019-10-14 13:08:05"
}
</pre>

### 1 大包入库

功能：扫描邮政的大包号CN35入库

#### 历史

> <b>启用</b> 2020年12月31日 定时任务调用做大包入库动作
>
> <b>作废</b> 2019年11月6日 使用新的接口 --> mobile/mScanChinapostPackage/new

http://localhost:8080/mobile/mScanChinapostPackage

DATA的值为：
<pre>
{
    "token": "C2QSWkl7O5jyv0o/ZmNGX4kbMvTQfQCd0vpi4txeyKNMWZq5FLe4IA==\n",
    "reqData": {
        "pkgPrintcode": "CNWUHDJPTYOAAEM85223005100025",
    },
    "timestamp": "2018-10-26 13:34:11"
}

</pre>

返回值：
<pre>       
    {"respCode":"0001","respData":"JAE1702200036","message":"没有预报，请联系中邮","timestamp":"2019-10-14 13:08:05"}
    {"respCode":"0001","respData":"CNWUHDJPTYOAAEM85223005100025","message":"没有要入库的订单！","timestamp":"2019-10-14 13:10:57"}
</pre>

### 2 大包异常退货

### 作废 2019年11月6日

功能：安检发现违禁品，使用大包异常退货操作大包，并退还邮政大包

http://localhost:8080/mobile/mScanChinapostPackageException

DATA的值为：
<pre>
{
    "token": "C2QSWkl7O5jyv0o/ZmNGX4kbMvTQfQCd0vpi4txeyKNMWZq5FLe4IA==\n",
    "reqData": {
        "pkgPrintcode": "CNWUHDJPTYOAAEM85223005100025",
    },
    "timestamp": "2018-10-26 13:34:11"
}

</pre>

返回值：
<pre>       
   {"respCode":"0001","respData":"CNWUHDJPTYOAAEM85223005100025","message":"该大包下无数据！","timestamp":"2019-10-14 13:56:48"}
</pre>         

### 3 获取下拉选项的参数

功能：登录后获取扫描配置的各种参数 规则: 目前只有线路=中邮俄线的，货源=中邮 需要选择板目的地，其他线路没有

http://localhost:8080/mobile/mGetConfig

DATA的值为：
<pre>
{
	"token": "r7STNzKWLifBGU8jfdBpFo3W/pt56dGh3DEI9mBuhmLKj7b9R+Jp8w==\n",
	"reqData": {},
	"timestamp": "2019-03-13 16:34:11"
}
</pre>

返回值：
<pre>       
  {
    "respCode": "0000",
    "respData": {
        "PKG_SOURCE": [
            {
                "value": "中邮",
                "code": "CP"
            },
            {
                "value": "非中邮",
                "code": "NCP"
            },
            {
                "value": "中外运",
                "code": "SPKG"
            },
            {
                "value": "美线EUB",
                "code": "USEUB"
            }
        ],
        "PALLET_DEST": "VKO,OVB,KZN,VVO,LGG,MAD",
        "PALLET_TYPE": "Q7,Y,L6"
    },
    "message": "SUCCESS",
    "timestamp": "2021-02-01 15:54:56"
}
</pre>         

