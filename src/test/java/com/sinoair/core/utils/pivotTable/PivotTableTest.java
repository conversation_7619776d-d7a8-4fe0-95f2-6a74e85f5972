package com.sinoair.core.utils.pivotTable;

import com.alibaba.fastjson.JSONObject;
import org.junit.Test;

import java.math.RoundingMode;
import java.util.Arrays;
import java.util.List;

/**
 * @author: 大雄
 */
public class PivotTableTest {

    @Test
    public void createView() {

        List<TestData> testData = Arrays.asList(
                new TestData("aaa", "bbb1", "cccc", null),
                new TestData("aaa", "bbb1", "cccc", 1.1d),
                new TestData("aaa", "bbb1", "xxx", 1.2d),
                new TestData("aaa", "bbb1", "xxx", 1.4d),
                new TestData("aab", "bbb2", "cccc", 1.1d),
                new TestData("aab", "bbb3", "cccc", 1.1d),
                new TestData("aac", "bbb1", "cccc", null)
                );

        PivotTable view = new PivotTableBuilder<TestData>()
                .addRowName("text1")
                .addRowName("text2")
                .addValueConfig(PivotTableValueConfig.createCountConfig("count1"))
                .addValueConfig(PivotTableValueConfig.createSumConfig("value1",1, RoundingMode.HALF_UP)).data(testData).build().createView();
        System.out.println(JSONObject.toJSONString(view));

    }
}

class TestData {
    @PivotTableField("text1")
    private String text1;
    @PivotTableField("text2")
    private String text2;
    @PivotTableField("count1")
    private String text3;
    @PivotTableField("value1")
    private Double value;

    public TestData(String text1, String text2, String text3, Double value) {
        this.text1 = text1;
        this.text2 = text2;
        this.text3 = text3;
        this.value = value;
    }

    public String getText1() {
        return text1;
    }

    public void setText1(String text1) {
        this.text1 = text1;
    }

    public String getText2() {
        return text2;
    }

    public void setText2(String text2) {
        this.text2 = text2;
    }

    public String getText3() {
        return text3;
    }

    public void setText3(String text3) {
        this.text3 = text3;
    }

    public Double getValue() {
        return value;
    }

    public void setValue(Double value) {
        this.value = value;
    }
}
