package com.sinoair.core.wish;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.domain.vo.wish.api.WiseCancelOrderNotifyParamVO;
import com.sinoair.ceop.domain.vo.wish.api.WiseCancelOrderNotifyResponseVO;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-02-25
 * @time: 10:59
 * @description: To change this template use File | Settings | File Templates.
 */
public class WiseApiUtilTest extends CommonTestCase {

    private Logger logger = LoggerFactory.getLogger(WiseApiUtilTest.class);

    @Test
    public void cancelOrder() {
        WiseCancelOrderNotifyParamVO wiseCancelOrderNotifyParamVO = new WiseCancelOrderNotifyParamVO();
        wiseCancelOrderNotifyParamVO.setTrackingId("123");
        wiseCancelOrderNotifyParamVO.setLogisticsOrderCode("345");
        wiseCancelOrderNotifyParamVO.setCarryType(4);
        wiseCancelOrderNotifyParamVO.setCancelReason("测试");
        wiseCancelOrderNotifyParamVO.setApikey(WiseApiUtil.WISE_API_KEY);
        try {
            WiseCancelOrderNotifyResponseVO responseVO = WiseApiUtil.cancelOrder(wiseCancelOrderNotifyParamVO);
            logger.info(JSONObject.toJSONString(responseVO));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}