package com.sinoair;


import com.alibaba.fastjson.JSONArray;
import com.google.common.collect.Lists;
import com.sinoair.core.utils.FileUtil;
import com.sinoair.core.utils.PatternUtil;

import com.alibaba.fastjson.JSONObject;
import java.io.File;
import java.util.List;

/**
 * Created by JunFei on 2021/7/26.
 */
public class UtilTest {



    public static void main(String[] args) {
        try {
            String filePath = "D:\\360MoveData\\Users\\JuanFei\\Desktop\\2.txt";
            File file = new File(filePath);
            String fileContent = FileUtil.readFileContent(file);
            List<String> fileContentList = Lists.newArrayList(fileContent.split("\n"));
            for(int i=0;i<fileContentList.size();i++){
                String line = fileContentList.get(i);
                JSONObject jsonObject = JSONObject.parseObject(line);
                JSONObject packageObject = jsonObject.getJSONObject("package");
                System.out.print("大包号：："+packageObject.getString("pkgCode")+"---------------");
                JSONArray jsonArray = jsonObject.getJSONArray("orders");
                for(int j=0;j<jsonArray.size();j++){
                    JSONObject single = jsonArray.getJSONObject(j);
                    System.out.print("'"+single.getString("trackingno")+"'");
                    System.out.print(",");
                }
                System.out.println();
            }
        } catch (Exception je) {
            je.printStackTrace();
            System.out.println(je.toString());
        }
    }

}
