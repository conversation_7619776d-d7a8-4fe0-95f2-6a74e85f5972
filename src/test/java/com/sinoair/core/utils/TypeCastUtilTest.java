package com.sinoair.core.utils;

import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.*;

/**
 * Created by WangXX4 on 2016/6/24.
 */
public class TypeCastUtilTest {

    @Before
    public void setUp() throws Exception {

    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void formatString2Integer() throws Exception {
        assertEquals((Integer) 123, TypeCastUtil.formatString2Integer("123"));
        assertEquals((Integer) 0, TypeCastUtil.formatString2Integer(""));
        assertEquals((Integer) 0, TypeCastUtil.formatString2Integer(null));
        assertEquals((Integer) (-1), TypeCastUtil.formatString2Integer("-1"));
        assertEquals((Integer) 0, TypeCastUtil.formatString2Integer("null"));
        assertEquals((Integer) 0, TypeCastUtil.formatString2Integer("abc"));
        assertEquals((Integer) 0, TypeCastUtil.formatString2Integer("0"));
        assertEquals((Integer) 0, TypeCastUtil.formatString2Integer("1.2"));

    }

    @Test
    public void formatString2BigDecimal() throws Exception {
        assertEquals(BigDecimal.valueOf(123.0), TypeCastUtil.formatDouble2BigDecimal(Double.valueOf(123)));
        assertEquals(BigDecimal.valueOf(0.0), TypeCastUtil.formatDouble2BigDecimal(Double.valueOf(0)));
        assertEquals(BigDecimal.valueOf(-10.0), TypeCastUtil.formatDouble2BigDecimal(Double.valueOf(-10)));
        assertEquals(BigDecimal.valueOf(-10.2), TypeCastUtil.formatDouble2BigDecimal(Double.valueOf(-10.2)));
    }


    @Test
    public void formatDouble2BigDecimal() throws Exception {

    }

    @Test
    public void formatString2Double() throws Exception {

    }
}