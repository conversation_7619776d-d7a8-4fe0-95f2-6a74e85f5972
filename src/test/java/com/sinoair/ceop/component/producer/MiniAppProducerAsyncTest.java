package com.sinoair.ceop.component.producer;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.apache.rocketmq.client.exception.MQBrokerException;
import org.apache.rocketmq.client.exception.MQClientException;
import org.apache.rocketmq.client.producer.SendResult;
import org.apache.rocketmq.common.message.Message;
import org.apache.rocketmq.remoting.exception.RemotingException;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @Author: daxiong
 */

@Component
public class MiniAppProducerAsyncTest extends CommonTestCase {

    @Autowired
    private MiniAppProducerSync miniAppProducerSync;

    @Test
    public void sendMessageSync() {
        Message message = new Message("VOHGION-REFUND-ZT",
                "VOHGION-REFUND-ZT","{\"eawbDeliverAddress\":\"沙坪路1号3栋1楼转集运仓\",\"eawbDeliverContact\":\"张三\",\"eawbDeliverDistrict\":\"塘厦镇\",\"eawbDeliverMobile\":\"13711112222\",\"eawbDeliverPhone\":\"13711112222\",\"eawbDeliveryatholiday\":\"1\",\"eawbDestcity\":\"东莞市\",\"eawbDestcountry\":\"CN\",\"eawbDeststate\":\"广东省\",\"eawbHandletime\":1695628775941,\"eawbInner\":\"Voghion_direct_refund\",\"eawbKeyentrytime\":1695628775941,\"eawbPrintcode\":\"SE22003654934\",\"eawbReference2\":\"T16944280886055910\",\"eawbRefundAddress\":\"沙坪路1号3栋1楼转集运仓\",\"eawbRefundCity\":\"东莞市\",\"eawbRefundCountry\":\"CN\",\"eawbRefundDistrict\":\"塘厦镇\",\"eawbRefundMobile\":\"13711112222\",\"eawbRefundName\":\"张三\",\"eawbRefundPhone\":\"13711112222\",\"eawbRefundPrivince\":\"广东省\",\"eawbServicetype\":\"Voghion_direct_refund\",\"eawbServicetypeOriginal\":\"Voghion_direct_refund\",\"eawbSoCode\":\"ZSH373812\",\"eawbSyscode\":2200365493,\"eawbTransmodeid\":\"Voghion_refund\",\"sacId\":\"DSS\"}".getBytes());
        try {
            SendResult sendResult = miniAppProducerSync.sendMessageSync(message);
            System.out.println(JSONObject.toJSONString(sendResult));
            System.out.println(sendResult.getSendStatus());
        } catch (InterruptedException e) {
            e.printStackTrace();
        } catch (RemotingException e) {
            e.printStackTrace();
        } catch (MQClientException e) {
            e.printStackTrace();
        } catch (MQBrokerException e) {
            e.printStackTrace();
        }
    }
}
