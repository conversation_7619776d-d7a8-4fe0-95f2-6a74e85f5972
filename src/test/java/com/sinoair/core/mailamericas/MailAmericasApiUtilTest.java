package com.sinoair.core.mailamericas;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.dao.EdiRecordMapper;
import com.sinoair.ceop.dao.ExpressPropertyMapper;
import com.sinoair.ceop.domain.Constant;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.EdiRecordWithBLOBs;
import com.sinoair.ceop.domain.model.ExpressProperty;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.domain.vo.ReturnObject;
import com.sinoair.ceop.domain.vo.cainiao.candd.FileUploadResponse;
import com.sinoair.ceop.domain.vo.cainiao.candd.GlobalParcelNetworkPrintDataPdfInfoRequest;
import com.sinoair.ceop.domain.vo.cainiao.candd.GlobalParcelNetworkPrintDataPdfInfoResponse;
import com.sinoair.ceop.service.b2ceurope.B2CEuropeService;
import com.sinoair.ceop.service.transmode.serviceType.ServiceType;
import com.sinoair.core.cainiao.CaiNiaoCAndDUtil;
import com.sinoair.core.mailamericas.bags.MABagFileResponse;
import com.sinoair.core.mailamericas.cn35.MACreateCN35Param;
import com.sinoair.core.mailamericas.cn35.MACreateCN35ParamPackage;
import com.sinoair.core.mailamericas.cn35.MACreateCN35Response;
import com.sinoair.core.mailamericas.dispatch.MACloseDispatchParam;
import com.sinoair.core.mailamericas.dispatch.MACreateDispatchParam;
import com.sinoair.core.mailamericas.dispatch.MACreateDispatchResponse;
import com.sinoair.core.mailamericas.dispatch.MADispatchFileResponse;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.*;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.io.File;
import java.util.*;

import com.sinoair.ceop.service.transmode.serviceType.ServiceTypeWish_CZPOST_RM_STD_BAT;
import com.sinoair.ceop.service.transmode.serviceType.ServiceTypeWish_CZPOST_RM_STD;
/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2020/7/21
 * @time: 10:01
 * @description: To change this template use File | Settings | File Templates.
 */
public class MailAmericasApiUtilTest extends CommonTestCase {

    @Autowired
    private EawbpreMapper eawbpreMapper;

    @Autowired
    private EdiRecordMapper ediRecordMapper;

    @Autowired
    private ExpressPropertyMapper expressPropertyMapper;


    @Test
    public void Test(){
        String codeArray ="SM015548061CL\n" +
                "SM015548602CL";
        String[]  codeA = codeArray.split("\n");
        StringBuffer sb = new StringBuffer();
        for (String c:codeA){
            try {
                List<Eawbpre> eps = eawbpreMapper.selectEawbpreByReference1(c);
                if(eps!=null && eps.size()>0){
                    Eawbpre ep = eps.get(0);
                    GlobalParcelNetworkPrintDataPdfInfoRequest globalParcelNetworkPrintDataPdfInfoRequest = CaiNiaoCAndDUtil.decorateGlobalParcelNetworkPrintDataPdfInfoRequest(ep);
//        globalParcelNetworkPrintDataPdfInfoRequest.setPrintType(4);
                    GlobalParcelNetworkPrintDataPdfInfoResponse globalParcelNetworkPrintDataPdfInfoResponse = CaiNiaoCAndDUtil.globalParcelNetworkPrintDataPdfInfo(CaiNiaoCAndDUtil.getLogisticProviderId(ep.getEawbServicetype()),globalParcelNetworkPrintDataPdfInfoRequest);

                    String pathStr = CaiNiaoCAndDUtil.FILE_PATH + File.separator + "caiNiaoCAndD";
                    File pathFile = new File(pathStr);
                    if(!pathFile.exists()){
                        pathFile.mkdirs();
                    }
                    String filePath =pathStr + File.separator + ep.getEawbReference1() + ".pdf";
                    Boolean flag = Base64Utils.base64ToFile(globalParcelNetworkPrintDataPdfInfoResponse.getData(),filePath);

                    File file = new File(filePath);
                    String result = FileUtil.uploadFileContainChineseFileName(file,null, Constant.FILE_SERVER_UPLOAD_URL);
                    FileUploadResponse fileUploadResponse = JSONObject.parseObject(result, FileUploadResponse.class);

                    String url = Constant.FILE_SERVER_DOWNLOAD_URL + fileUploadResponse.getUuid();
                    sb.append(url+"::::::"+c+"\n");
                }
            }catch (Exception e){
                e.printStackTrace();
            }
        }
        System.out.println(sb.toString());
    }


    public Map<String,String> analyzeB2CResponse(String b2cResponse)throws Exception{
        if(StringUtil.isEmpty(b2cResponse)){
            return new HashMap<>();
        }
        Map<String,String> map = new HashMap<String,String>();
        Document doc = DocumentHelper.parseText(b2cResponse);
        Element rootelement = doc.getRootElement();
        //ResultCode
        Element resultElement = rootelement.element("Result");
        Element resultCodeElement = resultElement.element("ResultCode");
        map.put("ResultCode",resultCodeElement.getStringValue());
        //成功的话
        if("0".equalsIgnoreCase(map.get("ResultCode"))){

            //DeliveryPackage
            Element deliveryPackageElement = rootelement.element("DeliveryPackage");
            Element trackingNumberElement = deliveryPackageElement.element("TrackingNumber");
            map.put("TrackingNumber", trackingNumberElement.getStringValue());
            Element packageBarCodeElement = deliveryPackageElement.element("PackageBarcode");
            map.put("PackageBarcode", packageBarCodeElement.getStringValue());

            Element deliveryPackageDataElement  = deliveryPackageElement.element("DeliveryPackageData");
            Element dataBlobElement = deliveryPackageDataElement.element("DataBlob");
            map.put("DataBlob",dataBlobElement.getStringValue());

            Element deliveryElement  = resultCodeElement.element("DeliveryPackageData");
            if(deliveryElement == null){
                deliveryElement = rootelement.element("Delivery");
            }
            Element carrierElement = deliveryElement.element("Carrier");
            map.put("Carrier",carrierElement.getStringValue());
        }else{
            Element errorDataElement = rootelement.element("ErrorData");
            Element errorDescription = errorDataElement.element("ErrorDescription");
            map.put("ErrorDescription",errorDescription.getStringValue());
        }
        return map;
    }

    public Map<String,String> selectTrackingNumberType(String  serviceType){
        Map<String,String> map = new HashMap<>();
        List<ExpressProperty> eps_Reference1 =  expressPropertyMapper.selectByGroupAndKey("B2C_Reference1_Code",serviceType);
        if(eps_Reference1!=null && eps_Reference1.size()>0){
            map.put("reference1",eps_Reference1.get(0).getEpValue());
        }
        List<ExpressProperty> eps_Reference2 =  expressPropertyMapper.selectByGroupAndKey("B2C_Reference3_Code",serviceType);
        if(eps_Reference2!=null && eps_Reference2.size()>0){
            map.put("reference3",eps_Reference2.get(0).getEpValue());
        }
        return map;
    }

    @Test
    public void createDispatch() {
        //MA_CN0015MX	1
        createDispatchMA_CN0015MX_1();
    }

    // MA_CN0015MX	1
    private void createDispatchMA_CN0015MX_1(){
        String serviceType = "MA_CN0030CO";
        MACreateDispatchParam param = MailAmericasConfig.getDispatchParamInfo(serviceType);
        try {
            MACreateDispatchResponse response = MailAmericasApiUtil.createDispatch(param,serviceType);
            System.out.println(JSONObject.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void closeDispatch() {

        MACloseDispatchParam param = new MACloseDispatchParam();
        param.setOrderId(293612L);
        //SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ssz");
        String time = DateUtil.parseString("yyyy-MM-dd'T'HH:mm:ssXXX",new Date());
        param.setDatetime(time);
        try {
            boolean b = MailAmericasApiUtil.closeDispatch(param);
            System.out.println(b);
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void createCN35() {
        MACreateCN35Param param = new MACreateCN35Param();
        param.setOrderId(293612L);
        param.setBagWeight("0.5");
        param.setLabelFormat("pdf");
        param.setIsLast(false);
        param.setExternalId("LS1017920383");
        List<MACreateCN35ParamPackage> packages = new ArrayList<>();
        MACreateCN35ParamPackage p1 = new MACreateCN35ParamPackage();
        p1.setTracking("ML032707585MH");
        p1.setVerifiedWeight(0.2);
        packages.add(p1);
        MACreateCN35ParamPackage p2 = new MACreateCN35ParamPackage();
        p2.setTracking("ML032699990MH");
        p2.setVerifiedWeight(0.2);
        packages.add(p2);
        param.setPackages(packages);
        System.out.println(JSONObject.toJSONString(param));
        try {
            MACreateCN35Response response = MailAmericasApiUtil.createCN35(param);
            System.out.println(JSONObject.toJSONString(response));
            System.out.println(MailAmericasApiUtil.filterPdfBase64(response.getCn35Label()));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Test
    public void getCN33() {
        Long orderId = 93057L;
        String cn35 = "AWBAWBMXMEXDAUR04029001110020";
        try {
            MABagFileResponse response = MailAmericasApiUtil.getCN33(cn35,orderId);
            System.out.println(JSONObject.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void getCN31() {
        Long orderId = 93036L;
        try {
            MADispatchFileResponse response = MailAmericasApiUtil.getCN31(orderId);
            System.out.println(JSONObject.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

    @Test
    public void getCN38() {
        Long orderId = 93036L;
        try {
            MADispatchFileResponse response = MailAmericasApiUtil.getCN38(orderId);
            System.out.println(JSONObject.toJSONString(response));
        } catch (Exception e) {
            e.printStackTrace();
        }

    }

}