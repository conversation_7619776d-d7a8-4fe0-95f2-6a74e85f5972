package com.sinoair.core.utils;

import com.cainiao.logisticscloud.dss.sdk.common.model.UploadResult;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.CloseableHttpResponse;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.junit.Test;

import java.io.File;
import java.io.IOException;

/**
 * # FILE INFO #
 * # Created with Intellij IDEA #
 * # Author: Liu·Ce #
 * # Date: 2017/7/21 #
 * # Time: 15:47 #
 */

public class AliDssUtilTest {
    @Test
    public void uplodTest() {
        UploadResult result = AliDssUtil.uploadFileToDss("F:\\testFile.jpg");
        System.out.println(result.getObjectName());
//        CloseableHttpClient client= HttpClients.createDefault();
//        HttpGet get=new HttpGet("http://www.baidu.com");
//        try {
//            CloseableHttpResponse response =client.execute(get);
//            System.out.print(response.getEntity().getContent());
//        } catch (IOException e) {
//            e.printStackTrace();
//        }

    }
}
