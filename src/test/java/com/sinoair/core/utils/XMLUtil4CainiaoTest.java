package com.sinoair.core.utils;

import com.sinoair.ceop.domain.vo.cainiao.LogisticsOrder;
import com.sinoair.ceop.domain.vo.cainiao.People;
import com.sinoair.ceop.domain.vo.cainiao.Pickup;
import org.junit.Test;

/**
 * Created by XueQB on 2016/9/22.
 */
public class XMLUtil4CainiaoTest {

    @Test
    public void getLogisticsOrder() throws Exception {
        String xml = "<request>\n" +
                "    <logisticsOrderCode>LP00038358296388</logisticsOrderCode>\n" +
                "       <customs>\n" +
                "           <declarePriceTotal>32</declarePriceTotal>\n" +
                "       </customs>" +
//                "    <sender>\n" +
//                "        <imID>33dsds</imID>\n" +
//                "        <name>刘小军</name>\n" +
//                "        <phone>12321321</phone>\n" +
//                "        <mobile>13282158037</mobile>\n" +
//                "        <zipCode>32332</zipCode>\n" +
//                "        <address>\n" +
//                "            <country>西班牙</country>\n" +
//                "        </address>\n" +
//                "    </sender>\n" +
                "    <receiver>\n" +
                "        <imID>aaa</imID>\n" +
                "        <name>dsvdsv</name>\n" +
                "        <phone>242442</phone>\n" +
                "        <mobile>13243565</mobile>\n" +
                "        <email>eeee</email>\n" +
                "        <zipCode>3243435</zipCode>\n" +
                "        <address>\n" +
                "            <country>22</country>\n" +
                "            <province>Alaska</province>\n" +
                "            <city>sdvsdvsd</city>\n" +
                "            <detailAddress>sdcsv sdvsdv</detailAddress>\n" +
                "        </address>\n" +
                "    </receiver>\n" +
                "    <parcel>\n" +
                "        <weight>354.765868</weight>\n" +
                "        <weightUnit>g</weightUnit>\n" +
                "        <suggestedWeight>354765868</suggestedWeight>\n" +
                "        <price>1</price>\n" +
                "        <priceUnit>CENT</priceUnit>\n" +
                "<bigBagID>111</bigBagID><parcelQuantity>111</parcelQuantity><bigBagWeight>11.3</bigBagWeight>" +
                "        <goodsList>\n" +
                "            <goods>\n" +
                "                <productID>2201019036053</productID>\n" +
                "                <name>牛仔裤</name>\n" +
                "                <cnName/>\n" +
                "                <categoryName>牛仔裤</categoryName>\n" +
                "                <categoryCNName/>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>1</price>\n" +
                "                <itemPrice>4108</itemPrice>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <declarePrice>1</declarePrice>\n" +
                "                <quantity>1</quantity>\n" +
                "                <url/>\n" +
                "            </goods>\n" +
                "        </goodsList>\n" +
                "    </parcel>\n" +
                "    <returnParcel>\n" +
                "        <imID>aa3</imID>\n" +
                "        <name>dsvdsv</name>\n" +
                "        <phone>242442</phone>\n" +
                "        <mobile>13243565</mobile>\n" +
                "        <undeliverableOption>2</undeliverableOption>\n" +
                "        <zipCode>3243435</zipCode>\n" +
                "        <address>\n" +
                "            <country>中国</country>\n" +
                "            <province>Alaska</province>\n" +
                "            <city>sdvsdvsd</city>\n" +
                "            <district>yuhang</district>\n" +
                "            <detailAddress>sdcsv sdvsdv</detailAddress>\n" +
                "        </address>\n" +
                "    </returnParcel>\n" +
                "    <trackingNumber>RS596963996GB</trackingNumber>\n" +
                "    <preCPResCode>STORE12016306</preCPResCode>\n" +
                "    <nextCPResCode/>\n" +
                "    <routingTrial>1</routingTrial>\n" +
                "    <bizType>AE_OVERSEA_WAREHOUSE_CONSIGN</bizType>\n" +
                "</request>";
        LogisticsOrder logisticsOrder = XMLUtil4Cainiao.getLogisticsOrder(xml);
        System.out.println(logisticsOrder.getLogisticsOrderCode());
        String weight = logisticsOrder.getParcel().getWeight().toString();
        System.out.println(weight);
        String Itotlevalue = logisticsOrder.getCustoms().getDeclarePriceTotal().toString();
        System.out.println(Itotlevalue);
        String packageCode = logisticsOrder.getParcel().getBigBagID().toUpperCase();
        System.out.println(packageCode);
        String ordersInPackage = logisticsOrder.getParcel().getParcelQuantity().toString();//大包内小包数量
        System.out.println(ordersInPackage);
        String senderName = logisticsOrder.getSender().getName();
        System.out.println(senderName);


    }


    @Test
    public void  test001(){

        People people = new Pickup();
        Pickup people1 = (Pickup) people;

        Pickup p = new Pickup();
//        p = (Pickup) people;
        People newPeople = p instanceof People ? ((People) p) : null;
//        People newPeople = (People) p;



    }

    @Test
    public void reFormatEscapeSequence() throws Exception {
        String s = "J&T>ss<ddd''sfd/";
        System.out.println(XMLUtil.reFormatEscapeSequence(s));
    }
}