package com.sinoair.core.api.imile;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.core.api.imile.order.IMileCreateOrderParam;
import com.sinoair.core.api.imile.order.IMileCreateOrderRespData;
import com.sinoair.core.api.imile.track.IMileTrackGetOneParam;
import com.sinoair.core.api.imile.track.IMileTrackGetOneRespData;

/**
 * @Author: 大雄
 * @Date: 2023/1/10 15:16
 * @Description:
 */
public class IMileApiTest {

    public static void testCreateOrder(String code) {
        IMileCreateOrderParam param = new IMileCreateOrderParam();
        param.setOrderCode(code);
        param.setOrderType("100");
        param.setAgingCode("EX");
        param.setConsignor("Sinotrans Express");
        param.setConsigneeContact("大雄");
        param.setConsigneePhone("17600000000");
        param.setConsigneeCountry("MEX");
        param.setConsigneeCity("Iztapalapa");
        param.setConsigneeAddress("MEX Ciudad de Mexico Iztapalapa Calzada Ermita iztapalapa 2955 Locales 43-47, 2da Amp San Miguel, 09360 Ciudad de México, CDMX");
        param.setConsigneeZipCode("9110");
        param.setGoodsValue("100");
        param.setCollectingMoney("0");
        param.setPaymentMethod("100");
        param.setTotalCount("1");
        param.setTotalWeight("10");
        param.setTotalVolume("27000");
        param.setSkuTotal("2");
        param.setSkuName("超重神秘盲盒*1");
        param.setBatterType("Normal");
        param.setCurrency("Local");
        IMileCreateOrderRespData order = IMileApi.createOrder(param);
        System.out.println(JSONObject.toJSONString(order));
    }

    public static void testGetOneTrack(String code) {
        IMileTrackGetOneParam param = new IMileTrackGetOneParam();
        param.setOrderNo(code);
        param.setOrderType("1");
        param.setLanguage("1");
        IMileTrackGetOneRespData oneTrack = IMileApi.getOneTrack(param);
        System.out.println(JSONObject.toJSONString(oneTrack));
    }

    public static void main(String[] args) {
        String orderNO = "SNR"+System.currentTimeMillis();
        System.out.println(orderNO);
        // testCreateOrder(orderNO);
        // {"expressNo":"6011123172047","imileAwb":""}

        testCreateOrder("6011123888504");

    }
}