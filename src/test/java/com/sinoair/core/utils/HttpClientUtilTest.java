package com.sinoair.core.utils;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.domain.Constant;
import com.sinoair.ceop.domain.vo.viaeurope.ViaLineItems;
import com.sinoair.ceop.domain.vo.viaeurope.ViaOrder;
import com.sinoair.ceop.domain.vo.viaeurope.ViaRequestVo;
import com.sinoair.ceop.domain.vo.viaeurope.ViaWeights4Labels;
import org.apache.commons.logging.Log;
import org.apache.commons.logging.LogFactory;
import org.apache.http.*;
import org.apache.http.client.HttpClient;
import org.apache.http.client.methods.HttpGet;
import org.apache.http.client.methods.HttpHead;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.client.params.ClientPNames;
import org.apache.http.client.params.CookiePolicy;
import org.apache.http.conn.ClientConnectionManager;
import org.apache.http.conn.scheme.PlainSocketFactory;
import org.apache.http.conn.scheme.Scheme;

import org.apache.http.conn.scheme.SchemeRegistry;
import org.apache.http.conn.ssl.SSLSocketFactory;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.DefaultHttpClient;
import org.apache.http.impl.conn.tsccm.ThreadSafeClientConnManager;
import org.apache.http.params.BasicHttpParams;
import org.apache.http.params.CoreProtocolPNames;
import org.apache.http.params.HttpParams;
import org.apache.http.params.HttpProtocolParams;
import org.apache.http.protocol.HTTP;
import org.apache.http.util.EntityUtils;
import org.junit.Test;
import sun.net.www.protocol.https.HttpsURLConnectionImpl;

import javax.net.ssl.HttpsURLConnection;
import javax.net.ssl.SSLContext;
import javax.net.ssl.TrustManager;
import javax.net.ssl.X509TrustManager;
import java.io.*;
import java.net.URL;
import java.security.KeyStore;
import java.security.cert.CertificateException;
import java.security.cert.X509Certificate;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Created by XueQB on 2017/1/17.
 */
public class HttpClientUtilTest {

    @Test
    public void head() throws Exception {
        Map<String, String> header = new HashMap<String, String>();
        header.put("Content-Type", "application/x-www-form-urlencoded");
        header.put("IBCCredentials", Constant.BASE6_USER);
        //获取头部
        Header[] headers = HttpClientUtil.head(Constant.TOKEN_URL, header, "Authority");
        if (headers != null && headers.length > 0) {
            //获取token
            String token = headers[0].getValue();
            System.out.println(token);
        }
    }

    public String testJsonData() {
        ViaOrder viaOrder = new ViaOrder();
        viaOrder.setClient_reference(no);
        viaOrder.setDisposition(ViaOrder.dispositionEnum.stock.toString());
        viaOrder.setBag_number("A-10");
        //viaOrder.setShipment_client_reference("HKGEXPRESS_JULY_25_UPS");
        ViaWeights4Labels viaWeights4Labels_1 = new ViaWeights4Labels();
        ViaWeights4Labels viaWeights4Labels_2 = new ViaWeights4Labels();
        viaWeights4Labels_1.setParcel_number(1);
        viaWeights4Labels_1.setWeight(10 * 1000);
        viaWeights4Labels_2.setParcel_number(2);
        viaWeights4Labels_2.setWeight(500);
        List<ViaWeights4Labels> viaWeights4Labelses = new ArrayList<ViaWeights4Labels>();
        viaWeights4Labelses.add(viaWeights4Labels_1);
        viaWeights4Labelses.add(viaWeights4Labels_2);
        viaOrder.setWeights_for_labels(viaWeights4Labelses);

        viaOrder.setCourier(ViaOrder.courierEnum.UPS.toString());
        viaOrder.setCourier_service(ViaOrder.courier_serviceEnum.DDP31_MULTI.toString());
        viaOrder.setRegistered(true);
        viaOrder.setName("John Doe");
        viaOrder.setCompany("Sinoair");
        viaOrder.setStreet("Müllerstraße 42");
        viaOrder.setCity("Berlin");
        viaOrder.setZip_code("10245");
        viaOrder.setCountry_code("DE");
        viaOrder.setEmail("<EMAIL>");
        viaOrder.setPhone("+4916012345678");

        String json = JSON.toJSONString(viaOrder);
        String json_result = "{ \"order\":" + json + "}";
        System.out.println(json_result);
        return json_result;

    }


    public String testJsonData_put() {
        ViaOrder viaOrder = new ViaOrder();
        //viaOrder.setClient_reference(no);
        viaOrder.setDisposition(ViaOrder.dispositionEnum.stock.toString());

        ViaLineItems ls1 = new ViaLineItems();
        ls1.setWeight(10 * 1000);
        ls1.setQty(1);
        ls1.setPrice(4500 * 1 * 10);
        //{"msgCode":"0000","message":"success","version":null,"token":null,"data":{"resp":{"errors":{"line_items":[{"price":["must be >= €3.50 per kg"]},{"price":["must be greater than or equal to 1","must be >= €0.50","must be >= €3.50 per kg","must be greater than or equal to 10"]}]}},"status":422}}
        ls1.setParcel_number(1);
        ls1.setTaric_code("6405901000");
        ls1.setDescription("shoes");


        //ViaLineItems ls2 = new ViaLineItems();
        //ls2.setWeight(500);
        //ls2.setQty(1);
        //ls2.setPrice(4500);
        //ls2.setParcel_number(2);
        //ls2.setTaric_code("8487905910");
        //ls2.setDescription("plastics key ring");

        List<ViaLineItems> lineItems = new ArrayList<ViaLineItems>();
        lineItems.add(ls1);
        //lineItems.add(ls2);
        viaOrder.setLine_items(lineItems);

        //viaOrder.setBag_number("L88888");
        viaOrder.setShipment_client_reference(eaCode);

        String json = JSON.toJSONString(viaOrder);
        String json_result = "{ \"order\":" + json + "}";
        System.out.println(json_result);
        return json_result;

    }

    @Test
    public void test4() {
        this.testJsonData();
    }


    String no = "TEST_1010101014";
    String eaCode = "999-49657226";
    String tempUrl = "http://************:8888/via/net";
    String token = "6dfc894b78641f738758df728920e25a";


    //下发订单
    @Test
    public void test7() throws IOException {
        String url = "https://app-sandbox.viaeurope.com/api/v2/orders";
        ViaRequestVo requestVo = new ViaRequestVo();
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Token token=\"" + token + "\"");
        header.put("Accept", "application/json");
        header.put("Content-Type", "application/json");
        requestVo.setHeader(header);
        requestVo.setHttpType("post");
        requestVo.setUrl(url);
        requestVo.setReqContent(JSONObject.parseObject(testJsonData()));
        StringEntity entity = new StringEntity(FastJsonUtils.toJSONString(requestVo), "UTF-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        System.out.println("【请求参数：】" + FastJsonUtils.toJSONString(requestVo));
        HttpPost httpPost = new HttpPost(tempUrl);
        httpPost.setEntity(entity);
        DefaultHttpClient client = new DefaultHttpClient();
        HttpResponse response = client.execute(httpPost);
        String result = EntityUtils.toString(response.getEntity());
        System.out.println(result);
        client.getConnectionManager().shutdown();


    }

    //获取订单打印地址
    @Test
    public void test8() throws IOException {

        HttpPost httpPost = new HttpPost(tempUrl);
        ViaRequestVo requestVo = new ViaRequestVo();
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Token token=\"" + token + "\"");
        header.put("Accept", "application/json");
        header.put("Content-Type", "application/json");
        header.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        requestVo.setHeader(header);
        requestVo.setHttpType("get");
        String urlgetLable = "https://app-sandbox.viaeurope.com/api/v2/orders/" + no + "/labels";
        requestVo.setUrl(urlgetLable);

        System.out.println(FastJsonUtils.toJSONString(requestVo));
        StringEntity entity = new StringEntity(FastJsonUtils.toJSONString(requestVo));
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        DefaultHttpClient client = new DefaultHttpClient();
        HttpResponse response = client.execute(httpPost);
        String result = EntityUtils.toString(response.getEntity());
        System.out.println(result);
        JSONObject jsonObject = JSONObject.parseObject(result);
        JSONObject data = jsonObject.getJSONObject("data");
        JSONArray resp = data.getJSONArray("resp");
        System.out.println(resp.size());
        System.out.println(resp.toJSONString());
        for (int i = 0; i < resp.size(); i++) {
            JSONObject jsonObject1 = resp.getJSONObject(i);
            System.out.println(jsonObject1.getString("pdf_url"));
        }

        client.getConnectionManager().shutdown();

    }

    //更新订单
    @Test
    public void test9() throws Exception {
        HttpPost httpPost = new HttpPost(tempUrl);
        ViaRequestVo requestVo = new ViaRequestVo();
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Token token=\"" + token + "\"");
        header.put("Accept", "application/json");
        header.put("Content-Type", "application/json");
        //header.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        requestVo.setHeader(header);
        requestVo.setHttpType("put");
        String urlgetLable = "https://app-sandbox.viaeurope.com/api/v2/orders/" + no;
        requestVo.setUrl(urlgetLable);
        requestVo.setReqContent(JSONObject.parseObject(testJsonData_put()));

        System.out.println(FastJsonUtils.toJSONString(requestVo));
        StringEntity entity = new StringEntity(FastJsonUtils.toJSONString(requestVo));
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        DefaultHttpClient client = new DefaultHttpClient();
        HttpResponse response = client.execute(httpPost);
        String result = EntityUtils.toString(response.getEntity());
        System.out.println(result);
        //JSONObject jsonObject = JSONObject.parseObject(result);
        //JSONObject data = jsonObject.getJSONObject("data");
        //JSONArray resp = data.getJSONArray("resp");
        //System.out.println(resp.size());
        //JSONObject jsonObject1 = resp.getJSONObject(0);
        //System.out.println(jsonObject1.getString("pdf_url"));
        //client.getConnectionManager().shutdown();
    }

    //锁定订单
    @Test
    public void test10() throws Exception {
        HttpPost httpPost = new HttpPost(tempUrl);
        ViaRequestVo requestVo = new ViaRequestVo();
        Map<String, String> header = new HashMap<String, String>();
        header.put("Authorization", "Token token=\"" + token + "\"");
        header.put("Accept", "application/json");
        header.put("Content-Type", "application/json");
        //header.put("User-Agent", "Mozilla/4.0 (compatible; MSIE 5.0; Windows NT; DigExt)");
        requestVo.setHeader(header);
        requestVo.setHttpType("put");
        String urlgetLable = "https://app-sandbox.viaeurope.com/api/v2/shipments/" + eaCode + "/wrap";
        requestVo.setUrl(urlgetLable);

        System.out.println(FastJsonUtils.toJSONString(requestVo));
        StringEntity entity = new StringEntity(FastJsonUtils.toJSONString(requestVo));
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        DefaultHttpClient client = new DefaultHttpClient();
        HttpResponse response = client.execute(httpPost);
        String result = EntityUtils.toString(response.getEntity());
        System.out.println(result);
    }

    @Test
    public void testGetPDFFromUrl() {
        String url = "https://viaeurope-sandbox.s3.eu-central-1.amazonaws.com/order/label/pdf/76c674e3-5f69-4ad5-843c-834ab1f0140b?X-Amz-Algorithm=AWS4-HMAC-SHA256&X-Amz-Credential=AKIAIXGLBOZY7YSU5EVA%2F20170509%2Feu-central-1%2Fs3%2Faws4_request&X-Amz-Date=20170509T065042Z&X-Amz-Expires=604800&X-Amz-SignedHeaders=host&X-Amz-Signature=3c147b26ad0d8ebfb4a8fe6d8b05c0ee5ab20d5e5ac229c79bb4a5882e200752";
        HttpClientUtil.getPDFFromUrl(url, "AA");
    }
}

