package com.sinoair.core.utils;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.dao.ExpressairwaybillMapper;
import com.sinoair.ceop.dao.ExpressitemMapper;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import com.sinoair.ceop.domain.model.Expressitem;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR> 2023-07-04 13:56
 */
public class DeYuanLogisticUtilTest extends CommonTestCase {

    private static Logger LOGGER = LoggerFactory.getLogger(DeYuanLogisticUtilTest.class);

    @Autowired
    private DeYuanLogisticUtil deYuanLogisticUtil;

    @Autowired
    private EawbpreMapper eawbpreMapper;

    @Autowired
    private ExpressairwaybillMapper expressairwaybillMapper;

    @Autowired
    private ExpressitemMapper expressitemMapper;

    @Test
    public void test() throws Exception {
        Eawbpre eawbpre = this.eawbpreMapper.queryEawbpreByEawbPrintCode("SE907775960033gb1");
        List<Expressitem> expressitemList = this.expressitemMapper.selectExpressitemsByEawbSyscode(eawbpre.getEawbSyscode());
        LOGGER.info("查询到的值：" + JSONObject.toJSONString(expressitemList));
        long currentTimestamp = System.currentTimeMillis() / 1000;
        String resStr = this.deYuanLogisticUtil.attainPre(currentTimestamp, eawbpre, expressitemList,"DY20120300216");
        LOGGER.info("响应结果:" + resStr);
        JSONObject jsonObject = JSONObject.parseObject(resStr);
        JSONObject body = jsonObject.getJSONObject("body");
        String lableKey = body.getString("lableKey");
        String pdfRes = this.deYuanLogisticUtil.attainPdf(currentTimestamp, lableKey, eawbpre);
        LOGGER.info("响应结果:" + pdfRes);
        Expressairwaybill eawb = expressairwaybillMapper.queryEawbSysCode(new BigDecimal("5961611255"));
        List<Expressairwaybill> eawbList = new ArrayList<>();
        eawbList.add(eawb);
        String sysWeightRes = this.deYuanLogisticUtil.syncSeconWeight(currentTimestamp, eawbList);
        LOGGER.info("二次同步重量接口返回:" + sysWeightRes);
        List<String> orderList = Arrays.asList("DYEMX1686253276415463426YQ","DYEMX1686202314531344385YQ","DYEFR1687257739137716225YQ","DYEFR1687261079601467393YQ","DYEGB1687014605787041794YQ","DYEDE1687262447850541057YQ","DYEGB1687015673916887041YQ","DYEDE1687263488415420418YQ","DYEIT1686971767913254914YQ","DYEIT1686970733849546753YQ","DYEES1686940087802466305YQ","DYEES1686969659528908802YQ","DYEMX1686938354346954753YQ","DYEUS1686935363953414145YQ","DYEUS1686934782748708865YQ","DYEMX1686936094812475394YQ","DYEMX1686912898770456577YQ","DYEMX1686903763047862274YQ","DYEMX1686666871099953154YQ","DYEMX1686667117278842882YQ","DYEMX1686622037591511041YQ","DYEUS1689925015917830146YQ","DYEUS1689922380103950338YQ");//labelkey
        String traceRes = this.deYuanLogisticUtil.queryBatchTrace(currentTimestamp, orderList);
        JSONObject resJsonObj = null;
        List<String> resList = new ArrayList<>();
        try {
            resJsonObj = JSONObject.parseObject(traceRes);
        } catch (Exception e) {
            e.printStackTrace();
        }
        JSONArray bodyArray = resJsonObj.getJSONArray("body");
        JSONObject jsonObject1 = (JSONObject) bodyArray.get(0);
        JSONArray trackPointsArray = jsonObject1.getJSONArray("trackPoints");
        for (Object o : trackPointsArray) {
            if (null == o) {
                continue;
            }
            JSONObject jo = (JSONObject) o;
            String desc = jo.getString("desc");
            resList.add(desc);
        }
        List<String> distinctList = resList.stream().distinct().collect(Collectors.toList());
        System.out.println("批量查询轨迹接口返回"+JSONObject.toJSONString(distinctList));
        System.out.println("===========================================================");
        for (String s: distinctList) {
            System.out.println(s);
        }
        System.out.println("===========================================================");
    }
}
