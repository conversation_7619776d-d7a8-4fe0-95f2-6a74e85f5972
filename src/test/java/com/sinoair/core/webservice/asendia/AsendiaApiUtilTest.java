package com.sinoair.core.webservice.asendia;

import com.google.common.collect.Lists;
import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.domain.model.Eawbpre;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.FastJsonUtils;
import com.sinoair.core.utils.FileUtil;
import com.sinoair.core.webservice.asendia.addOrder.OrderDetail;
import com.sinoair.core.webservice.asendia.addOrder.RequestPO;
import com.sinoair.core.webservice.asendia.addOrder.ResponsePO;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;
import com.sinoair.ceop.service.transmode.serviceType.ServiceTypeFIRST_FLIGHT01;

import java.io.File;
import java.util.List;
import static org.junit.Assert.*;

/**
 * Created by Wangcl on 2019/7/17
 */
public class AsendiaApiUtilTest extends CommonTestCase {

    @Autowired
    private EawbpreMapper eawbpreMapper;

    @Autowired
    private ServiceTypeFIRST_FLIGHT01 serviceTypeFIRST_flight01;

    @Test
    public void testAddOrder(){
        RequestPO requestPO=new RequestPO();
        OrderDetail orderDetail=new OrderDetail();
        orderDetail.setOrderNumber("WXX1234560081");
        orderDetail.setServiceType("SP-AP");
        orderDetail.setConsignee("Luis Figueira");
        orderDetail.setAddress1("Rue des Terreaux 4");
        orderDetail.setAddress2("Rue des Terreaux 4");
        orderDetail.setAddress3("");
        orderDetail.setCity("Epinay Sur Seine");
        orderDetail.setState("Epinay Sur Seine");
        orderDetail.setCountryCode("FR");
        orderDetail.setConsigneePhone("+852 123456");
        orderDetail.setZip("93880");
        orderDetail.setEmail("<EMAIL>");
        orderDetail.setWeight("0.12");
        orderDetail.setCustomsType("O");
        orderDetail.setDescription("phone case");
        orderDetail.setValue("10");
        orderDetail.setCurrency("USD");
        orderDetail.setQty("1");
        orderDetail.setQuantity("1");
        orderDetail.setRemark("remarks");

        requestPO.getOrderList().add(orderDetail);
        ResponsePO responsePO=AsendiaApiUtil.addOrder(requestPO);
        String result=FastJsonUtils.toJSONString(responsePO);
        System.out.println(result);
    }

    @Test
    public void testDownloadPdf(){
        com.sinoair.core.webservice.asendia.labelPrinting.RequestPO requestPO=new com.sinoair.core.webservice.asendia.labelPrinting.RequestPO();
        com.sinoair.core.webservice.asendia.labelPrinting.OrderDetail orderDetail=new com.sinoair.core.webservice.asendia.labelPrinting.OrderDetail();
        orderDetail.setTrackingNo("UX406966279CH");
        requestPO.getOrderList().add(orderDetail);
        String pdfUrl=AsendiaApiUtil.downloadPDF(requestPO);
        System.out.println(pdfUrl);
    }


    @Test
    public void Test(){
        String code = "";
        List<String> allCode = Lists.newArrayList(code.split("\n"));
        for(String c:allCode){
            try {
                Eawbpre ep = eawbpreMapper.selectByEawbReference2(c).get(0);
                String url = serviceTypeFIRST_flight01.acquirePdfUrl(ep);
                String labelBase64 = FileUtil.getBase64StrByFileUrl(url);
                File file = FileUtil.base64GenerateFile(labelBase64,c+".pdf");
                System.out.println(file.getAbsolutePath());
            }catch (Exception e){
                e.printStackTrace();
            }
        }
    }

}