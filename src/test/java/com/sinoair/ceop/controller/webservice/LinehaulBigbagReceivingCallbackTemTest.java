package com.sinoair.ceop.controller.webservice;

import com.sinoair.ceop.domain.model.CnWmsRecord;
import com.sinoair.ceop.domain.vo.ReturnObject;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.*;

/**
 * 大包对接入口测试类
 *
 * <AUTHOR> 2021-10-15 17:02
 */
public class LinehaulBigbagReceivingCallbackTemTest extends CommonTestCase {

    @Autowired
    LinehaulBigbagReceivingCallbackTem linehaulBigbagReceivingCallbackTem;

    @Test
    public void linehaulBigbagReceivingCallbackThread() {
        List<CnWmsRecord> cnWmsRecordList = new ArrayList<>();
        CnWmsRecord cnWmsRecord = new CnWmsRecord();
        cnWmsRecord.setCwrSyscode(418382L);
        cnWmsRecord.setOrderid("YYSNT2000001055561");
        cnWmsRecord.setRequestStatus("0");
        cnWmsRecordList.add(cnWmsRecord);
        ReturnObject returnObject = linehaulBigbagReceivingCallbackTem.linehaulBigbagReceivingCallbackThread(cnWmsRecordList);

    }
}