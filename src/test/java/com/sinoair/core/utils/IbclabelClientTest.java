package com.sinoair.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.itextpdf.text.*;
import com.itextpdf.text.Image;
import com.itextpdf.text.pdf.*;
import com.sinoair.core.zpl.IbclabelClient;
import org.junit.Ignore;
import org.junit.Test;

import javax.print.*;
import javax.print.attribute.DocAttributeSet;
import javax.print.attribute.HashDocAttributeSet;
import javax.print.attribute.HashPrintRequestAttributeSet;
import javax.swing.*;
import java.awt.*;
import java.awt.Font;
import java.awt.print.PrinterJob;
import java.io.*;
import java.util.*;


import java.io.FileOutputStream;
import java.io.OutputStream;
import java.util.Date;

import com.itextpdf.text.Document;
import com.itextpdf.text.Paragraph;
import com.itextpdf.text.pdf.PdfWriter;

/**
 * Created by DengQing on 2016/8/24.
 */
public class IbclabelClientTest {

    @Test
    public void getLabel() throws Exception {
        Long start = System.currentTimeMillis();
        /*String zplcode = "{\n" +
                "    \"items\": [\n" +
				"        {\n" +
				"            \"weight\": \"6.14\",\n" +
				"            \"weight_unit\": \"LB\",\n" +
				"            \"packaging\": \"BOX\",\n" +
				"            \"description\": \"4 tier cupcake stand\"\n" +
				"        },\n" +
				"        {\n" +
				"            \"weight\": \"6.12\",\n" +
				"            \"weight_unit\": \"LB\",\n" +
				"            \"packaging\": \"BOX\",\n" +
				"            \"description\": \"4 tier cupcake stand\"\n" +
				"        }\n" +
				"    ],\n" +
				"    \"customer\": {\n" +
				"        \"name\": \"Shenzhen Maxway Arts Crafts Co.ltd\",\n" +
				"        \"company\": \"Shenzhen Maxway Arts Crafts Co.ltd\",\n" +
				"        \"phone\": \"13027681958\",\n" +
				"        \"address1\": \"Golden State FC LLC 24300 Nandina Ave Moreno Valley CA 92551 US ONT8\",\n" +
				"        \"city\": \"LAX\",\n" +
				"        \"state\": \"CA\",\n" +
				"        \"postalCode\": \"92551\",\n" +
				"        \"countryCode\": \"US\"\n" +
				"    }\n" +
				"}";*/

        String zplcode = "{\n" +
                "    \"customer\": {\n" +
                "        \"address1\": \",4559 as sahafah,riyadh,Saudi Arabia,Saudi Arabia\",\n" +
                "        \"city\": \"LAX\",\n" +
                "        \"company\": \"salma alnafisah\",\n" +
                "        \"countryCode\": \"US\",\n" +
                "        \"name\": \"salma alnafisah\",\n" +
                "        \"phone\": \"13396541508\",\n" +
                "        \"postalCode\": \"92551\",\n" +
                "        \"state\": \"CA\"\n" +
                "    },\n" +
                "    \"items\": [\n" +
                "        {\n" +
                "            \"description\": \"4 tier cupcake stand\",\n" +
                "            \"packaging\": \"BOX\",\n" +
                "            \"weight\": \"0.82\",\n" +
                "            \"weight_unit\": \"LB\"\n" +
                "        }\n" +
                "    ]\n" +
                "}";
        JSONObject param = null;
        try {
            param = JSONObject.parseObject(zplcode);
        } catch (Exception e) {
            e.printStackTrace();
        }


        Map<String, Object> map = new HashMap<String, Object>();
        map.put("eawbConsignmentno", "86077390291");//86077390292 86077390291
        map.put("subEawbConsignmentno", "77390291");//77390292 77390291
        map.put("TST", "TST");
        map.put("params", param);

        Map map1 = IbclabelClient.getLabel(map);
        String res[] = (String[]) map1.get("result");
        System.out.println(res);
        Long end = System.currentTimeMillis();
        System.out.println((end - start) + "ms");
    }


	/*@Test
    public void printPdf() throws FileNotFoundException {
		JFileChooser fileChooser = new JFileChooser(); // 创建打印服务
		//int state = fileChooser.showOpenDialog(null);
		//if (state == JFileChooser.APPROVE_OPTION) {
		//File file = fileChooser.getSelectedFile(); // 获取选择的文件
		String filename = "E:\\EX16071200008P01.pdf";
		File file = new File(filename);
		// 构建打印请求属性集
		HashPrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();
		// 设置打印格式，因为未确定类型，所以选择autosense
		DocFlavor flavor = DocFlavor.INPUT_STREAM.AUTOSENSE;
		// 查找所有的可用的打印服务
		PrintService printService[] = PrintServiceLookup
				.lookupPrintServices(flavor, pras);
		// 定位默认的打印服务
		PrintService defaultService = PrintServiceLookup
				.lookupDefaultPrintService();
		System.out.println("打印机名:" + printService);
		// 显示打印对话框
		PrintService service = ServiceUI.printDialog(null, 200, 200,
				printService, defaultService, flavor, pras);
		if (service != null) {
			try {
				DocPrintJob job = service.createPrintJob(); // 创建打印作业
				FileInputStream fis = new FileInputStream(file); // 构造待打印的文件流
				DocAttributeSet das = new HashDocAttributeSet();
				Doc doc = new SimpleDoc(fis, flavor, das);
				job.print(doc, pras);
			} catch (Exception e) {
				e.printStackTrace();
			}
		}

	}*/


	/*@Test
    public void printTest(String str) throws FileNotFoundException {
		String filename = "E:/EX16071200008P01.pdf";
		File file = new File(filename);
		DocFlavor flavor = DocFlavor.INPUT_STREAM.AUTOSENSE;
		HashPrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();
		PrintService printService = null;
		printService = PrintServiceLookup.lookupPrintServices(flavor, pras)[0];
		System.out.println("打印机名:" + printService);
		// 创建打印作业
		DocPrintJob job = printService.createPrintJob();
		FileInputStream fis = new FileInputStream(file); // 构造待打印的文件流
		DocAttributeSet das = new HashDocAttributeSet();
		Doc doc = new SimpleDoc(fis, flavor, das);
		try {
			job.print(doc, pras); // 进行每一页的具体打印操作
		} catch (PrintException pe) {
			pe.printStackTrace();
		}

	}*/

	/*@Test
    public void PrintDemo() {
		JFileChooser fileChooser = new JFileChooser(); //创建打印作业
		int state = fileChooser.showOpenDialog(null);
		if (state == fileChooser.APPROVE_OPTION) {
			File file = new File("E:/86077391211.txt"); //获取选择的文件
			//构建打印请求属性集
			HashPrintRequestAttributeSet pras = new HashPrintRequestAttributeSet();
			//设置打印格式，因为未确定类型，所以选择autosense
			DocFlavor flavor = DocFlavor.INPUT_STREAM.AUTOSENSE;
			//查找所有的可用的打印服务
			PrintService printService[] = PrintServiceLookup.lookupPrintServices(flavor, pras);
			//定位默认的打印服务
			PrintService defaultService = PrintServiceLookup.lookupDefaultPrintService();
			//显示打印对话框
			PrintService service = ServiceUI.printDialog(null, 200, 200, printService,
					defaultService, flavor, pras);
			if (service != null) {
				try {
					DocPrintJob job = service.createPrintJob(); //创建打印作业
					FileInputStream fis = new FileInputStream(file); //构造待打印的文件流
					DocAttributeSet das = new HashDocAttributeSet();
					Doc doc = new SimpleDoc(fis, flavor, das);
					job.print(doc, pras);
				} catch (Exception e) {
					e.printStackTrace();
				}
			}
		}

	}*/

/*
    @Test
	public void GeneratePDF() {
		try {
			BaseFont bff = BaseFont.createFont(BaseFont.HELVETICA_BOLD, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
			BaseFont tBFArial = BaseFont.createFont(BaseFont.HELVETICA, BaseFont.WINANSI, BaseFont.NOT_EMBEDDED);
			OutputStream file = new FileOutputStream(new File("E:\\Test.pdf"));

			Document document = new Document();
			PdfWriter writer = PdfWriter.getInstance(document, file);

			//writer.setEncryption("dengqing".getBytes(), "user".getBytes(),
			//PdfWriter.ALLOW_PRINTING, PdfWriter.ENCRYPTION_AES_128);

			document.open();

			document.add(new Paragraph("Hello World, iText"));
			document.add(new Paragraph(new Date().toString()));

			PdfContentByte tPcd = writer.getDirectContent();
			tPcd.beginText();
			tPcd.setFontAndSize(bff, 9.5f);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "DELIVER TO", 9, 355, 0);

			tPcd.setFontAndSize(bff, 12);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "SINOHF AWB : " + "778988993", 120, 390, 0);

			Barcode128 sinoAWBcode2 = new Barcode128();
			sinoAWBcode2.setCode("eawb_consignmentno");
			sinoAWBcode2.setGenerateChecksum(true);
			sinoAWBcode2.setStartStopText(true);
			sinoAWBcode2.setX(0.34f);
			sinoAWBcode2.setN(18f);
			sinoAWBcode2.setAltText("");
			Image eParcleCode2 = sinoAWBcode2.createImageWithBarcode(tPcd, null, null);
			eParcleCode2.setAbsolutePosition(150, 320);
			eParcleCode2.scaleAbsolute(100, 50);
			document.add(eParcleCode2);

			tPcd.setFontAndSize(bff, 15);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "" + "destination", 160, 300, 0);//目的港三字码
			tPcd.setFontAndSize(bff, 8);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, " 收件人" + "nimei", 140, 260, 0);

			tPcd.setFontAndSize(bff, 8);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "DESCRIPTION OF CONTENTS :  " + "hahah", 9, 232, 0);//品名


			tPcd.setFontAndSize(bff, 8);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "QUANTITY :  " + "eawb_quantity" + "/" + "total_pieces", 9, 220, 0);//数量

			tPcd.setFontAndSize(bff, 11);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "strWeight" + " kg", 240, 232, 0);

			tPcd.setFontAndSize(tBFArial, 8);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, " " + "89w809er", 180, 195, 0);

			tPcd.setFontAndSize(tBFArial, 12);
			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "SINOHF AWB : " + "634549867594", 75, 85, 0);
			tPcd.setFontAndSize(bff, 8);

			tPcd.showTextAligned(PdfContentByte.ALIGN_LEFT, "SENDER", 9, 70, 0);
			tPcd.setFontAndSize(tBFArial, 8);
			tPcd.endText();

			document.close();
			file.close();
			printTest("E:\\Test.pdf");
		} catch (Exception e) {

			e.printStackTrace();
		}
	}*/


    @Test
    public void TTT() {
        String mr[] = {"Mr.marcos santana"};
        String adress[] = {"No.rua do sacramento casa 45 , casa terreo"};
        String Tel[] = {"Tel:55-7581020585"};
        String PostCode[] = {"PostCode:92551"};


        String[] Mrs = SplitDataUtil.convertStringArray(mr, 20);
        String[] adresss = SplitDataUtil.convertStringArray(adress, 20);
        String[] Tels = SplitDataUtil.convertStringArray(Tel, 20);
        String[] PostCodes = SplitDataUtil.convertStringArray(PostCode, 20);


        String gg[]=getMergeArray(Mrs,adresss);
        String ggg[]=getMergeArray(gg,Tels);
         String gggg[]= getMergeArray(ggg,PostCodes);
        for(int i=0;i<gggg.length;i++){
            System.out.println(gggg[i]);
        }


    }

    public String[] getMergeArray(String[] al,String[] bl) {
        String[] a = al;
        String[] b = bl;
        String[] c = new String[a.length + b.length];
        System.arraycopy(a, 0, c, 0, a.length);
        System.arraycopy(b, 0, c, a.length, b.length);
        return c;
    }
}
