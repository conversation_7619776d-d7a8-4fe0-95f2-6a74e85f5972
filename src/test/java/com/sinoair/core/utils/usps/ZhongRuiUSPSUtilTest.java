package com.sinoair.core.utils.usps;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.domain.vo.usps.zhongRui.*;
import com.sinoair.core.utils.DateUtil;
import junit.framework.TestCase;
import org.junit.Test;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;

public class ZhongRuiUSPSUtilTest{

    @Test
    public void tuffyOrderTest(){
        System.out.println(tuffyOrderStr);
        TuffyOrderRequest tuffyOrderRequest = JSONObject.parseObject(tuffyOrderStr,TuffyOrderRequest.class);
        tuffyOrderRequest.setZrsupplyShipToken(ZhongRuiUSPSUtil.ACCOUNT);
        tuffyOrderRequest.setAppToken(ZhongRuiUSPSUtil.PASSWORD);
        tuffyOrderRequest.setCallingTime(DateUtil.convertDate2String(new Date(),DateUtil.DATE_FORMAT_YYYYMMDDHHMMSS));
        tuffyOrderRequest.getData().getSalesOrder().setCusDocumentNo("WCL20230428001");
        tuffyOrderRequest.getData().getSalesOrder().setCarrierCode("usps_priority_mail");
        tuffyOrderRequest.setVerification(ZhongRuiUSPSUtil.doVerification(tuffyOrderRequest.getZrsupplyShipToken(),tuffyOrderRequest.getAppToken(),tuffyOrderRequest.getCallingTime(),tuffyOrderRequest.getData().getSalesOrder().getCusDocumentNo(),tuffyOrderRequest.getBusinessType()));
        TuffyOrderResponse tuffyOrderResponse = ZhongRuiUSPSUtil.tuffyOrder(tuffyOrderRequest);
    }

    @Test
    public void modifyTuffyOrderTest(){
        ModifyTuffyOrderRequest modifyTuffyOrderRequest = JSONObject.parseObject(tuffyOrderStr,ModifyTuffyOrderRequest.class);
        modifyTuffyOrderRequest.setZrsupplyShipToken(ZhongRuiUSPSUtil.ACCOUNT);
        modifyTuffyOrderRequest.setAppToken(ZhongRuiUSPSUtil.PASSWORD);
        modifyTuffyOrderRequest.setCallingTime(DateUtil.convertDate2String(new Date(),DateUtil.DATE_FORMAT_YYYYMMDDHHMMSS));
        modifyTuffyOrderRequest.getData().getSalesOrder().setCusDocumentNo("WCL20230423001");
        modifyTuffyOrderRequest.getData().getSalesOrder().setCarrierCode("usps_priority_mail");
        modifyTuffyOrderRequest.setVerification(ZhongRuiUSPSUtil.doVerification(modifyTuffyOrderRequest.getZrsupplyShipToken(),modifyTuffyOrderRequest.getAppToken(),modifyTuffyOrderRequest.getCallingTime(),modifyTuffyOrderRequest.getData().getSalesOrder().getCusDocumentNo(),modifyTuffyOrderRequest.getBusinessType()));
        ModifyTuffyOrderResponse modifyTuffyOrderResponse = ZhongRuiUSPSUtil.modifyTuffyOrder(modifyTuffyOrderRequest);
    }

    @Test
    public void confirmOrderTest(){
        ConfirmOrderRequest confirmOrderRequest = new ConfirmOrderRequest();
        confirmOrderRequest.setZrsupplyShipToken(ZhongRuiUSPSUtil.ACCOUNT);
        confirmOrderRequest.setAppToken(ZhongRuiUSPSUtil.PASSWORD);

        List<String> picNumber = new ArrayList<>();
        picNumber.add("93055202241500000000005645");

        PicNumbers picNumbers = new PicNumbers();
        picNumbers.setPicNumber(picNumber);
        confirmOrderRequest.setPicNumbers(picNumbers);

        ConfirmOrderResponse confirmOrderResponse = ZhongRuiUSPSUtil.confirmOrder(confirmOrderRequest);
    }

    @Test
    public void eliminateOrderTest(){
        EliminateOrderRequest eliminateOrderRequest = new EliminateOrderRequest();

        eliminateOrderRequest.setZrsupplyShipToken(ZhongRuiUSPSUtil.ACCOUNT);
        eliminateOrderRequest.setAppToken(ZhongRuiUSPSUtil.PASSWORD);

        List<String> picNumber = new ArrayList<>();
        picNumber.add("93055202241500000000005645");

        PicNumbers picNumbers = new PicNumbers();
        picNumbers.setPicNumber(picNumber);
        eliminateOrderRequest.setPicNumbers(picNumbers);

        EliminateOrderResponse eliminateOrderResponse = ZhongRuiUSPSUtil.eliminateOrder(eliminateOrderRequest);
    }


    private static final String tuffyOrderStr = "{\n" +
            "\t\"businessType\": \"logisticChannel\",\n" +
            "\t\"zrsupplyShipToken\": \"your zrsupplyShipToken\",\n" +
            "\t\"callingTime\": \"2019-03-13 12:00:00\",\n" +
            "\t\"appToken\": \"your appToken\",\n" +
            "\t\"data\": {\n" +
            "\t\t\"salesOrder\": {\n" +
            "\t\t\t\"buyerCountries\": \"US\",\n" +
            "\t\t\t\"poZipCode\": \"90304\",\n" +
            "\t\t\t\"buyerProvince\": \"CA\",\n" +
            "\t\t\t\"buyerCity\": \"INGLEWOOD\",\n" +
            "\t\t\t\"buyerAddress\": \"718 S. ISIS AVE,\",\n" +
            "\t\t\t\"buyerAddress2\": \"718S. ISISAVE,\",\n" +
            "\t\t\t\"buyerName\": \"TiffianyWalker\",\n" +
            "\t\t\t\"buyerCompany\": \"zrSupply\",\n" +
            "\t\t\t\"buyerPhone\": \"7722123105\",\n" +
            "\t\t\t\"buyerCode\": \"46556\",\n" +
            "\t\t\t\"packageLength\": \"12\",\n" +
            "\t\t\t\"packageWidth\": \"12\",\n" +
            "\t\t\t\"packageHeight\": \"2\",\n" +
            "\t\t\t\"packageWeight\": \"0.2\",\n" +
            "\t\t\t\"carrierCode\": \"usps_priority_mail\",\n" +
            "\t\t\t\"cusDocumentNo\": \"evsTest049\",\n" +
            "\t\t\t\"returnAddress1\": \"2125 Gateway Blvd\",\n" +
            "\t\t\t\"returnAddress2\": \"Hebron KY\",\n" +
            "\t\t\t\" needPushManifest \": \"Y\",\n" +
            "\t\t\t\"fromCity\": \"Hebron\",\n" +
            "\t\t\t\"fromCompany\": \"companyName\",\n" +
            "\t\t\t\"fromName\": \"Online Seller\",\n" +
            "\t\t\t\"fromPhone\": \"************\",\n" +
            "\t\t\t\"fromPostalCode\": \"41048\",\n" +
            "\t\t\t\"fromState\": \"KY\",\n" +
            "\t\t\t\"detailList\": [{\n" +
            "\t\t\t\t\t\"goodsName\": \"mobile\",\n" + //英文品名
            "\t\t\t\t\t\"goodsUnitPrice\": \"12\",\n" +
            "\t\t\t\t\t\"goodsSum\": \"1\",\n" +
            "\t\t\t\t\t\"goodsRemark\": \"小于50个字符\"\n" +
            "\t\t\t\t},\n" +
            "\t\t\t\t{\n" +
            "\t\t\t\t\t\"goodsName\": \"mobile 2\",\n" +
            "\t\t\t\t\t\"goodsUnitPrice\": \"12\",\n" +
            "\t\t\t\t\t\"goodsSum\": \"2\",\n" +
            "\t\t\t\t\t\"goodsRemark\": \"\"\n" +
            "\t\t\t\t}\n" +
            "\t\t\t]\n" +
            "\t\t}\n" +
            "\t},\n" +
            "\t\"verification\": \"a3f0f8610da3747233632b731d45642f\"\n" +
            "}";

}