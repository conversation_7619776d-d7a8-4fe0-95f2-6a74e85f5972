package com.sinoair.core.wish;

import com.sinoair.ceop.domain.vo.wish.WishParcelLabelParamVO;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-02-21
 * @time: 14:33
 * @description: To change this template use File | Settings | File Templates.
 */
public class WishParcelLabelUtilTest extends CommonTestCase {

    @Test
    public void printLabel() {
        WishParcelLabelParamVO param = new WishParcelLabelParamVO();
        param.setLabelType("面单标识1");
        param.setSinoairNo("86123456789");
        param.setShipperName("大雄");
        param.setShipperAddress("河北省任丘市辛中驿镇培里村");
        param.setShipperCity("沧州市");
        param.setShipperProvince("河北省");
        param.setShipperCountry("CHINA");
        param.setShipperMobile("17600108082");
        param.setConsigneeName("王XX");
        param.setConsigneeAddress("北京市顺义区天竺工业区A区天柱路20号 外运发展二层211室");
        param.setConsigneeCity("北京市");
        param.setConsigneePostcode("000000");
        param.setConsigneeCountry("CHINA");
        param.setConsigneeMobile("010-80418274");
        param.setWishOrderNo("WOSP005815505324FRA");
        param.setWishOrderDate("2019-02-21");
        try {
            //String url = WishParcelLabelUtil.printLabelUrl(param);
            String base64pdf = WishParcelLabelUtil.getLabelBase64(param);
            System.out.println(base64pdf);
        } catch (Exception e) {
            e.printStackTrace();
        }
    }
}