package com.sinoair.core.utils;

import com.sinoair.ceop.domain.Constant;
import com.sinoair.ceop.domain.model.Expressairwaybill;
import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.Test;

import java.util.Date;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by JunFei on 2020/5/21.
 */
public class HttpUtilTest {

    @Test
    public void response() throws Exception{
        Map<String,String> parammap = new HashMap<String,String>();
        parammap.put("LP001192408988471","88900");
        parammap.put("LP001157617780351","86170");
        parammap.put("LP001193634019671","66023");
        parammap.put("LP001141311099561","30034");
//        parammap.put("LP00116909759447","24043");
//        parammap.put("LP00117303029423","40055");
//        parammap.put("LP00117440902947","20139");
//        parammap.put("LP00118972633638","40016");
//        parammap.put("LP00118524167423","31039");
//        parammap.put("LP00121221121936","10156");
//        parammap.put("LP00122048842927","21100");
//        parammap.put("LP00122740014986","42123");
//        parammap.put("LP00118909550480","28069");
//        parammap.put("LP00122063021000","00010");
//        parammap.put("LP00118918065840","21052");
//        parammap.put("LP00122080540904","25069");
//        parammap.put("LP00118963874707","20123");
//        parammap.put("LP00122563722515","72021");
//        parammap.put("LP00122567167051","86079");
//        parammap.put("LP00121882919637","81100");
//        parammap.put("LP00118816947107","10043");
//        parammap.put("LP00122661192329","07100");
//        parammap.put("LP00118825011665","20037");
//        parammap.put("LP00122665039126","60027");
//        parammap.put("LP00122010190267","30028");
//        parammap.put("LP00122731956866","32020");
//        parammap.put("LP00122040701175","32020");
//        parammap.put("LP00122734386505","56020");
//        parammap.put("LP00118900328538","71010");
//        parammap.put("LP00118903514704","00132");
        StringBuffer sb = new StringBuffer("");
        for(String key:parammap.keySet()) {

            String ShippingMethod = new String[]{"PARCELPREMIUMFML","TRACKPOST"}[new java.util.Random().nextInt(2)];
            String requestParam = "<?xml version=\"\"1.0\"\" encoding=\"\"UTF-8\"\" standalone=\"\"yes\"\"?>\n" +
                    "<FinalMileLabelCreateRequest>\n" +
                    "    <Authentication>\n" +
                    "        <AuthenticationKey>75A724D1002152C04AB3003BAF1051EDBE947050</AuthenticationKey>\n" +
                    "        <LayoutType>L01</LayoutType>\n" +
                    "        <LayoutVersion>1.0</LayoutVersion>\n" +
                    "        <CustomerReference>XueQB-20200108155332-15</CustomerReference>\n" +
                    "    </Authentication>\n" +
                    "    <ShipperAddress>\n" +
                    "        <ShipperAddressType>SHB</ShipperAddressType>\n" +
                    "        <CompanyName>wish</CompanyName>\n" +
                    "        <AddressLine1>Test Address 1</AddressLine1>\n" +
                    "        <HouseNumber></HouseNumber>\n" +
                    "        <HouseNumberExtension></HouseNumberExtension>\n" +
                    "        <CityOrTown>Hangzhou</CityOrTown>\n" +
                    "        <StateOrProvince>Zhejiang</StateOrProvince>\n" +
                    "        <PostalCode>310000</PostalCode>\n" +
                    "        <CountryCode>CHN</CountryCode>\n" +
                    "    </ShipperAddress>\n" +
                    "    <Delivery>\n" +
                    "        <OrderNumber>"+key+"</OrderNumber>\n" +
                    "        <OrderReference></OrderReference>\n" +
                    "        <OrderContent></OrderContent>\n" +
                    "        <ShippingMethod>"+ShippingMethod+"</ShippingMethod>\n" +
                    "        <CYPID></CYPID>\n" +
                    "        <Currency>USD</Currency>\n" +
                    "        <LabelType>3</LabelType>\n" +
                    "        <CustomsClearance>4</CustomsClearance>\n" +
                    "        <CustomsInvoice></CustomsInvoice>\n" +
                    "        <PurchaseDate>2020-04-29</PurchaseDate>\n" +
                    "    </Delivery>\n" +
                    "     <DeliveryAddress>\n" +
                    "        <DeliveryAddressType>DL1</DeliveryAddressType>\n" +
                    "        <ConsigneeName>George Smits</ConsigneeName>\n" +
                    "        <CompanyName></CompanyName>\n" +
                    "        <AddressLine1>Via Giovanni Berta 131</AddressLine1>\n" +
                    "        <HouseNumber></HouseNumber>\n" +
                    "        <HouseNumberExtension></HouseNumberExtension>\n" +
                    "        <CityOrTown>IJmuiden</CityOrTown>\n" +
                    "        <StateOrProvince>Other</StateOrProvince>\n" +
                    "        <PostalCode>"+parammap.get(key)+"</PostalCode>\n" +
                    "        <CountryCode>ITA</CountryCode>\n" +
                    "    </DeliveryAddress>\n" +
                    "    <DeliveryContact>\n" +
                    "        <PhoneNumber>0781556865</PhoneNumber>\n" +
                    "        <SMSNumber></SMSNumber>\n" +
                    "        <EmailAddress><EMAIL></EmailAddress>\n" +
                    "        <PersonalNumber></PersonalNumber>\n" +
                    "    </DeliveryContact>\n" +
                    "    <DeliveryPackage>\n" +
                    "        <PackageNumber>1</PackageNumber>\n" +
                    "        <PackageReference>88091379002</PackageReference>\n" +
                    "        <PackageWeight>1000</PackageWeight>\n" +
                    "        <DimensionHeight></DimensionHeight>\n" +
                    "        <DimensionWidth></DimensionWidth>\n" +
                    "        <DimensionLength></DimensionLength>\n" +
                    "    </DeliveryPackage>\n" +
                    "    <DeliveryContent>\n" +
                    "        <PackageNumber>1</PackageNumber>\n" +
                    "        <SKUCode>[0xe5][0xa4][0x96][0xe5][0xa5][0x97]</SKUCode>\n" +
                    "        <SKUDescription>COAT</SKUDescription>\n" +
                    "        <Quantity>1</Quantity>\n" +
                    "        <Price>22</Price>\n" +
                    "        <CountryCodeOrigin>CHN</CountryCodeOrigin>\n" +
                    "    </DeliveryContent>\n" +
                    "    <DeliveryContent>\n" +
                    "        <PackageNumber>1</PackageNumber>\n" +
                    "        <SKUCode>[0xe9][0x9e][0x8b][0xe5][0xad][0x90]</SKUCode>\n" +
                    "        <SKUDescription>shoes</SKUDescription>\n" +
                    "        <Quantity>1</Quantity>\n" +
                    "        <Price>23</Price>\n" +
                    "        <CountryCodeOrigin>CHN</CountryCodeOrigin>\n" +
                    "    </DeliveryContent>\n" +
                    "    <DeliveryContent>\n" +
                    "        <PackageNumber>1</PackageNumber>\n" +
                    "        <SKUCode>[0xe5][0xa4][0xb9][0xe5][0x85][0x8b][0xe8]</SKUCode>\n" +
                    "        <SKUDescription>Jacket</SKUDescription>\n" +
                    "        <Quantity>1</Quantity>\n" +
                    "        <Price>10</Price>\n" +
                    "        <CountryCodeOrigin>CHN</CountryCodeOrigin>\n" +
                    "    </DeliveryContent>\n" +
                    "    <Validation>\n" +
                    "        <TotalDeliveryPackages>1</TotalDeliveryPackages>\n" +
                    "        <MailAddressConfirmation></MailAddressConfirmation>\n" +
                    "        <MailAddressError><EMAIL></MailAddressError>\n" +
                    "        <TimeZone></TimeZone>\n" +
                    "    </Validation>\n" +
                    "</FinalMileLabelCreateRequest>";
            String b2cResponse = HttpUtil.writePost("https://finalmilelabel-live.b2ceurope.eu", requestParam, "UTF-8", false);
            Map<String, String> map = analyzeB2CResponse(b2cResponse);
            //响应成功的
            if ("0".equalsIgnoreCase(map.get("ResultCode"))) {
                String base64File = map.get("DataBlob");
                String eawbReference1 = map.get("TrackingNumber");
                String fileName = "B2CEuropeService" + eawbReference1 + ".pdf";
                String url = FileUtil.uploadBase64ToLiveFtp(base64File, fileName);

                sb.append("'"+ key+",");
                sb.append("'" + map.get("TrackingNumber")+",");
                sb.append("'" + map.get("PackageBarcode")+",");
                sb.append("'" + url+",");
                sb.append(","+ShippingMethod+",");
                sb.append("'"+map.get("Carrier")+",");
                sb.append("'"+parammap.get(key)+"\r\n");
            }
            Thread.sleep(2000);
        }
        FileUtil.createFileUTF8BOM("/",sb,"a.csv");
    }

    private Map<String,String> analyzeB2CResponse(String b2cResponse)throws Exception{
        Map<String,String> map = new HashMap<String,String>();
        Document doc = DocumentHelper.parseText(b2cResponse);
        Element rootelement = doc.getRootElement();
        //ResultCode
        Element resultElement = rootelement.element("Result");
        Element resultCodeElement = resultElement.element("ResultCode");
        map.put("ResultCode",resultCodeElement.getStringValue());
        //成功的话
        if("0".equalsIgnoreCase(map.get("ResultCode"))){

            //DeliveryPackage
            Element deliveryPackageElement = rootelement.element("DeliveryPackage");
            Element trackingNumberElement = deliveryPackageElement.element("TrackingNumber");
            map.put("TrackingNumber",trackingNumberElement.getStringValue());
            Element packageBarCodeElement = deliveryPackageElement.element("PackageBarcode");
            map.put("PackageBarcode", packageBarCodeElement.getStringValue());

            Element deliveryPackageDataElement  = deliveryPackageElement.element("DeliveryPackageData");
            Element dataBlobElement = deliveryPackageDataElement.element("DataBlob");
            map.put("DataBlob",dataBlobElement.getStringValue());

            Element deliveryElement  = resultCodeElement.element("DeliveryPackageData");
            if(deliveryElement == null){
                deliveryElement = rootelement.element("Delivery");
            }
            Element carrierElement = deliveryElement.element("Carrier");
            map.put("Carrier",carrierElement.getStringValue());

        }else{
            Element errorDataElement = rootelement.element("ErrorData");
            Element errorDescription = errorDataElement.element("ErrorDescription");
            map.put("ErrorDescription",errorDescription.getStringValue());
        }
        return map;
    }

}