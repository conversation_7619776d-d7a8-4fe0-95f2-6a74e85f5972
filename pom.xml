<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/maven-v4_0_0.xsd">
    <modelVersion>4.0.0</modelVersion>
    <groupId>com.sinoair</groupId>
    <artifactId>ceop</artifactId>
    <packaging>war</packaging>
    <version>${app_version}</version>
    <name>ceop</name>
    <url>http://maven.apache.org</url>

    <!-- param init start -->
    <properties>
        <project.build.sourceEncoding>UTF-8</project.build.sourceEncoding>
        <plugin.mybatis.generator>1.3.1</plugin.mybatis.generator>
        <mybatis.generator.generatorConfig.xml>${basedir}/src/test/resources/generatorConfig.xml
        </mybatis.generator.generatorConfig.xml>
        <mybatis.generator.generatorConfig.properties>file:///${basedir}/src/test/resources/generatorConfig.properties
        </mybatis.generator.generatorConfig.properties>


        <!-- jdk -->
        <jdk.version>1.8</jdk.version>
        <!-- encoding -->
        <encoding.val>utf8</encoding.val>
        <!-- spring版本号 3.2.4.RELEASE -->
        <spring.version>4.1.6.RELEASE</spring.version>
        <!-- mybatis版本号 -->
        <mybatis.version>3.2.4</mybatis.version>
        <!-- log4j日志文件管理包版本 -->
        <slf4j.version>1.6.6</slf4j.version>
        <log4j.version>1.2.12</log4j.version>
        <!-- junit -->
        <junit.version>4.12</junit.version>
        <!-- jetty -->
        <jetty.version>8.0.0.M3</jetty.version>
        <!-- spring-mybatis -->
        <mybatis.spring.version>1.3.1</mybatis.spring.version>
        <!-- mysql驱动 -->
        <mysql.version>5.1.9</mysql.version>
        <!-- 阿里巴巴数据源 -->
        <alibaba.version>1.0.3</alibaba.version>
        <!-- jackson -->
        <jackson.version>2.12.7</jackson.version>
        <!-- servlet-api -->
        <servlet-api.version>3.0.1</servlet-api.version>
        <!-- commons-lang3 -->
        <commons-lang3.version>3.3.2</commons-lang3.version>
        <!-- commons-fileupload -->
        <commons-fileupload.version>1.4</commons-fileupload.version>
        <!-- jsp-api -->
        <jsp-api.version>2.2.1-b03</jsp-api.version>
        <!-- jstl -->
        <jstl.version>1.2</jstl.version>
        <!-- oracle 驱动 -->
        <ojdbc.version>6.0</ojdbc.version>
        <!-- memcached -->
        <spymemcached.version>2.8.4</spymemcached.version>
        <!-- 工具网站 -->
        <!-- <pagehelper.version>3.4.1</pagehelper.version>-->
        <pagehelper.version>5.1.1</pagehelper.version>
        <hibernate.validator.version>5.1.1.Final</hibernate.validator.version>
        <!--jerry -->
        <jerry.version>8.0.0.M3</jerry.version>
        <!-- fastjson -->
        <fastjson.version>1.2.83</fastjson.version>
        <!-- commons-collections -->
        <commons-collections.version>3.2.1</commons-collections.version>
        <!-- aspectjweaver -->
        <aspectjweaver.version>1.8.6</aspectjweaver.version>
        <!-- javax.servlet-api -->
        <javax.servlet-api.version>3.0.1</javax.servlet-api.version>
        <!-- cglib -->
        <cglib.version>3.2.0</cglib.version>
        <!-- ehcache-core -->
        <ehcache-core.version>2.6.11</ehcache-core.version>
        <!-- commons-codec -->
        <commons-codec.version>1.11</commons-codec.version>
        <!-- apache.poi -->
        <apache.poi.version>3.10-FINAL</apache.poi.version>
        <!-- org.quartz -->
        <org.quartz.version>2.2.1</org.quartz.version>
        <commons-net.version>3.6</commons-net.version>
        <log4j2.version>2.20.0</log4j2.version>
    </properties>
    <!-- param init end -->
    <!--资源过滤  begin-->
    <profiles>
        <profile>
            <id>product-test</id>
            <properties>
                <finalName>CEOP</finalName>
                <environment.name>product</environment.name>
                <app_version>2.19.26</app_version>
                <profileId>product-test</profileId>
                <environment.desc>product environment on linux server</environment.desc>
                <!--是否加载字典-->
                <dictionary.init_config>false</dictionary.init_config>
                <jdbc.url>********************************************</jdbc.url>
                <jdbc.username>ceos</jdbc.username>
                <jdbc.password>Ceos_210</jdbc.password>
                <dataSource>DruidDataSource</dataSource>
                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>6379</db.redis.port>
                <db.redis.password>RedisDB.Cloud20211110</db.redis.password>
                <db.redis.database>0</db.redis.database>
                <db.redisson.address>redis://*************:6379</db.redisson.address>
                <db.redisson.password>RedisDB.Cloud20211110</db.redisson.password>
                <db.redisson.database>0</db.redisson.database>
                <historyRootPath>D:/bea/ceosHistoryLog</historyRootPath>
                <appFilePath>D:/bea/appConfig</appFilePath>
                <dispatchNumberURL>http://mla.mailamericas.com/api/v1/reserve?access_token=SIN2887DF89SA0Z0299
                </dispatchNumberURL>
                <orderPlatformURL>http://**************/order/FastRpt/PDF_NEW.aspx
                </orderPlatformURL>
                <!--阿姆斯特丹FBA-->
                <ams.tempUrl>http://************:8888/via/net</ams.tempUrl>
                <ams.viatoken.fba>3e39f5929b7831b6e14375d9e7f39cbf</ams.viatoken.fba>
                <ams.viaurl>https://app.viaeurope.com</ams.viaurl>
                <!-- 生产环境-->
                <requestUrlCainiao>http://link.cainiao.com/gateway/link.do</requestUrlCainiao>
                <requestUrlUseast>https://useast-link.cainiao.com/gateway/link.do</requestUrlUseast>
                <!-- 预发环境-->
                <!--                <requestUrlCainiao>https://prelink.cainiao.com/gateway/link.do</requestUrlCainiao>-->
                <signKey>v6lfQ5XH677s5I4835tgecOOBmZ9u7T9</signKey>
                <signKey4TW>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67</signKey4TW>
                <!--阿姆斯特丹FBA END-->
                <inter.littlepackage>http://************:8888/littlePackageQuery</inter.littlepackage>
                <interchange.url>http://*************:81</interchange.url>
                <!-- weblogic-->
                <!--  <weblogic.adminurl>t3://************:7001/</weblogic.adminurl>
                  <weblogic.user>weblogic</weblogic.user>
                  <weblogic.password>sinoairbea!%@</weblogic.password>-->
                <!-- log4j-->
                <log4j.rootLogger>DEBUG</log4j.rootLogger>
                <print.url>http://localhost:8080</print.url>
                <!--美线操作手册在文件服务器的地址-->
                <US.NoteBook>http://ecdowntest.sinoair.com:8081/download</US.NoteBook>
                <!--文件服务器下载地址-->
                <sinoair_file_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_ecdown_url_key>

                <!--文件服务器下载地址-->
                <sinoair_file_wb_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_wb_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_wb_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_wb_ecdown_url_key>

                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>

                <!-- data export job 配置 -->
                <data.export.url>http://************:9000</data.export.url>
                <data.export.ceos.code>CEOS_210</data.export.ceos.code>

                <!--对接ACT Express-->
                <act.express.url>http://dataif.ufms.co.kr/if/order</act.express.url>

                <!--美国专线对接-->
                <sureline2_url>http://************</sureline2_url>
                <sureline2_AppKey>5d21f7b7-c5c4-4e1c-ad0f-635eead09469</sureline2_AppKey>
                <sureline2_ClientSecret>24383ddf-9ac8-4917-804c-2db452cadab1</sureline2_ClientSecret>

                <!-- 万色对接信息 -->
                <wise_api_host_url>http://*************:38888</wise_api_host_url>
                <wise_api_key>qMuhp6Ktj</wise_api_key>

                <!-- colisprive -->
                <colisprive.wsdl.url>https://www.colisprive.com/Externe/WSCP.asmx</colisprive.wsdl.url>
                <colisprive.wsdl.username>SINOTRANSAGWS</colisprive.wsdl.username>
                <colisprive.wsdl.password>hS*dP3Jq5@pV!7gX</colisprive.wsdl.password>
                <colisprive.wsdl.CPCustoID>AG</colisprive.wsdl.CPCustoID>
                <colisprive.wsdl.AccountID>EM110419</colisprive.wsdl.AccountID>

                <!--艾莉薇对接 start-->
                <ALW_REQUEST_URL>http://api.ollyway.com</ALW_REQUEST_URL>
                <ALW_REQUEST_USER>A720</ALW_REQUEST_USER>
                <ALW_REQUEST_TOKEN>0b3f75c1901eada9b50f5121a6585f46</ALW_REQUEST_TOKEN>
                <ALW_REQUEST_USER_SNR>A743</ALW_REQUEST_USER_SNR>
                <ALW_REQUEST_TOKEN_SNR>5ddde2cd7a67e7195d39790dcd4e729f</ALW_REQUEST_TOKEN_SNR>
                <!--艾莉薇对接 end-->

                <!--wishB2B-->
                <wishB2B_Base_url>https://www.wishpost.cn</wishB2B_Base_url>
                <wishB2B_api_key>JHBia2RmMiQxMDAkUE1mNEg2TTBacXkxVnNxNWR3NWh6QSRFMGIyZnFBbFhMdTJORElhZFpjVFNKSmdpOGc=
                </wishB2B_api_key>
                <!--wish店铺模式 test api key-->
                <!--<wishB2B_api_key_FUSION>JHBia2RmMiQxMDXXXXXXXXXXXXXXU3TXZHYmdWQy92YWw5SFk=</wishB2B_api_key_FUSION>-->
                <!--wish店铺模式 production api key-->
                <wishB2B_api_key_FUSION>
                    JHBia2RmMiQxMDAkelhsUEtRVmdqTkg2SDJOTVNla2RJdyRoZnRQdHMvUGUxdWxidjlWSzhwZ2xWWnBuTkU=
                </wishB2B_api_key_FUSION>


                <!--Wise末端派送 start -->
                <WISE_API_KEY>d779c1e4c8def888168929a80bfa2e4f11711</WISE_API_KEY>
                <!--Wise末端派送 END -->

                <!--FBA 互联通 start -->
                <HLT_APPKEY>1a2c057e9abc48638eab84f69a72b173</HLT_APPKEY>
                <HLT_URL>http://**************:8086/xms/</HLT_URL>
                <!--FBA 互联通 END -->

                <!--footlocker_singapore-->
                <client_id>********************************</client_id>
                <client_secret>d45a7855b0d944a4b6690d8e8ad79e52</client_secret>
                <pickup_address_id>98989012</pickup_address_id>
                <fl_singapore_token_url>https://api-sandbox.ninjavan.co/SG/2.0/oauth/access_token
                </fl_singapore_token_url>
                <fl_singapore_create_oreder_url>https://api-sandbox.ninjavan.co/SG/4.1/orders
                </fl_singapore_create_oreder_url>

                <ups_emi_validation_url>
                    https://oc-gspk-og.orangeconnex.com/api/shipment/si/package/v1/validation
                </ups_emi_validation_url>
                <ups_emi_label_url>https://oc-gspk-og.orangeconnex.com/api/shipment/si/package/v1/label
                </ups_emi_label_url>
                <ups_emi_apiKey>c3aa211f-514e-4986-8c17-5c6bf9aa8c80</ups_emi_apiKey>
                <ups_emi_securityKey>********-883a-4d85-b665-90534ef3c94c</ups_emi_securityKey>


                <!--小米的结算账号 -->
                <xiaomi.socode>HKG167952</xiaomi.socode>

                <!--B2C europe 的url -->
                <b2c.url>https://finalmilelabel-live.b2ceurope.eu</b2c.url>
                <!--B2C europe 的password -->
                <b2c.pass>75A724D1002152C04AB3003BAF1051EDBE947050</b2c.pass>
                <!--对接EMS-新一代-->
                <EMS_WH_CODE>31120030</EMS_WH_CODE>
                <EMS_KEY>y8ptdndcq9c1n9l1</EMS_KEY>
                <EMS_EC_COMPANY_ID>1100066675327</EMS_EC_COMPANY_ID>
                <EMS_GET_ORDER_NO_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/barCodesAssgine/barCodeService
                </EMS_GET_ORDER_NO_URL>
                <EMS_SEND_ORDER_BACK_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/orderService/OrderReceiveBack
                </EMS_SEND_ORDER_BACK_URL>
                <EMS_SEND_ORDER_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/orderService/orderReceive
                </EMS_SEND_ORDER_URL>
                <EMS_GET_LABEL_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/surface/download</EMS_GET_LABEL_URL>
                <!--对接IQS-->
                <IQS_CREATE_URL>https://www.iqsgsc.com/v2/shipment/create</IQS_CREATE_URL>
                <IQS_TOKEN>access_token_cysACCrnJUGqeuNB</IQS_TOKEN>
                <IQS_ACCOUNT>11261</IQS_ACCOUNT>
                <IQS_UID><EMAIL></IQS_UID>
                <WISH_COLOMBIA_SHIPPING_PUSH_URL>
                    http://svc1.sipost.co/WcfServiceSPOKE/ServiceSPOKE.svc/PostShippingWithoutFile
                </WISH_COLOMBIA_SHIPPING_PUSH_URL>

                <!--GLS对接-->
                <GLS_IP>**************</GLS_IP>
                <GLS_PORT>3030</GLS_PORT>
                <GLS_T805>*********</GLS_T805>
                <GLS_T8700>BE 610</GLS_T8700>
                <GLS_T8914>056aaacL2g</GLS_T8914>
                <GLS_T8915>**********</GLS_T8915>
                <GLS_T805_CAINIAO>*********</GLS_T805_CAINIAO>
                <GLS_T8914_CAINIAO>056aaacZ8i</GLS_T8914_CAINIAO>

                <!--新美线 PB对接参数-->
                <PB_GET_TOKEN_AUTHORIZATION>Basic bXBtcUhXdDN4ekRDUGhmMXVOcmZyYk5HT1hTZFRocnk6cWxZWm1NZWZHTFcxOHZvOQ==
                </PB_GET_TOKEN_AUTHORIZATION>
                <QT_SOCODE>ZSU168192,********,HGH177412</QT_SOCODE>

                <MBB_ADD_EXPRESS_ORDER_URL>
                    http://oms.mbb-logistics.com/service/Storage_addExpressOrder.action?userId=<EMAIL>&amp;token=AFYIzHVSwEnvCaP2aXAbBlSe2wq12w
                </MBB_ADD_EXPRESS_ORDER_URL>
                <AEPOST_URL>https://osb.epg.gov.ae/ebs/genericapi/booking/rest/CreateBooking</AEPOST_URL>
                <AEPOST_HSCODE_URL>
                    https://osb.epg.gov.ae/ebs/epg.pos.CustomerService.lookups.rest.noauth/GetCustomDescriptionsV1
                </AEPOST_HSCODE_URL>
                <AEPOST_TOKEN>C637127</AEPOST_TOKEN>

                <!--捷网英邮的配置 start-->
                <JWYY_USERNAME>18397</JWYY_USERNAME>
                <JWYY_PASSWORD>Op9c0QQguq8cnUaT</JWYY_PASSWORD>
                <!--捷网英邮的配置 end-->
                <!--以色列邮政对接 生产环境 -->
                <ISRAELPOST_URL>https://apimftprd.israelpost.co.il</ISRAELPOST_URL>

                <SinoairExp_Username><EMAIL></SinoairExp_Username>
                <SinoairExp_Password>vF4CFYw~GPaD</SinoairExp_Password>
                <SinoairExp_key>ea9fda3b42c24ba4a487ab306091d695</SinoairExp_key>

                <SinoairTrk_Username><EMAIL></SinoairTrk_Username>
                <SinoairTrk_Password>~aFM71Urr2kr</SinoairTrk_Password>
                <SinoairTrk_key>211d883f85294222a189f35b8aada7ee</SinoairTrk_key>

                <SinoairEco_Username><EMAIL></SinoairEco_Username>
                <SinoairEco_Password>yzHqMR(K8VDV</SinoairEco_Password>
                <SinoairEco_key>fc750c1949da420cae1be089accf572d</SinoairEco_key>

                <SinoairDdp_Username><EMAIL></SinoairDdp_Username>
                <SinoairDdp_Password>9bBB6uvhwnRo</SinoairDdp_Password>
                <SinoairDdp_key>9e9788afb92b4361aefbf21079c9a189</SinoairDdp_key>
                <!--以色列邮政对接 生产环境 end-->
                <!--                天猫直发app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>0743R02E262U38UYADNNr653M7lO3dn2
                </SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>
                <!--                天猫集运台湾app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67
                </SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>
                <!-- shopee 配置开始 start................ -->
                <shopee.url.3.1>https://external2.shopee.sg/lcs/live/api/first_leg/repack_carton/lh_sinotrans_eu
                </shopee.url.3.1>
                <shopee.url.3.2>https://external2.shopee.sg/lcs/live/api/first_leg/loading_carton/lh_sinotrans_eu
                </shopee.url.3.2>
                <shopee.url.3.3>https://external2.shopee.sg/lcs/live/api/first_leg/tracking_carton/lh_sinotrans_eu
                </shopee.url.3.3>
                <shopee.usename>lh_sinotrans_eu</shopee.usename>
                <shopee.password>zWoJ2TOkjvVQuNiSxcaAZsFupIf817gR</shopee.password>
                <!-- shopee 配置开始 End................ -->

                <!-- UBI 配置 start............ -->
                <UBI_URL_IP>http://cn.etowertech.com</UBI_URL_IP>
                <UBI_URL_TOKEN>pcloBCvuQOgiIwVT1ALvGz</UBI_URL_TOKEN>
                <UBI_URL_SECURET>fRTlwEg1Yoy6S5eLssTe0w</UBI_URL_SECURET>

                <UBI_URL_TOKEN_HD>pcloBCvuQOgiIwVT1ALvGz</UBI_URL_TOKEN_HD>
                <UBI_URL_SECURET_HD>fRTlwEg1Yoy6S5eLssTe0w</UBI_URL_SECURET_HD>

                <UBI_URL_TOKEN_HN>pclZP4TsihBI5eLTtlbRCh</UBI_URL_TOKEN_HN>
                <UBI_URL_SECURET_HN>P8MXZhg77ab_T7NbcRpjVA</UBI_URL_SECURET_HN>
                <!-- UBI 配置 end -->

                <!-- AMS配置 -->
                <ams.password>a96dbb5cb2bb43036a79d3e1b39680ed</ams.password>
                <ams.url>https://app-sandbox.viaeurope.com</ams.url>
                <!-- AMS配置 -->

                <!-- SPX配置开始 -->
                <SPX_URL>https://rps.miqexpress.com/rps-api</SPX_URL>
                <SPX_TOKEN>45ea18-ff8005-15e962-05165d-3348da</SPX_TOKEN>
                <!-- SPX配置结束 -->

                <!-- 易云通的配置开始 -->
                <yyt_url>https://api.y2t.com/cb-order/webapi/getBillUrl</yyt_url>
                <yyt_apiAppKey>APIDeC1hCqe709sQHjXlIto2xekVcS6ghjXxENAZ</yyt_apiAppKey>
                <yyt_apiAppSecret>k5eM7qnpyw5r6pm0vp87zphnh7probmag9oLwj5</yyt_apiAppSecret>
                <yyt_boxListUrl>https://api.y2t.com/cb-order/webapi/importBoxNo</yyt_boxListUrl>
                <!-- 易云通的配置结束 -->

                <!-- southAfrica配置 -->
                <south_africa_uri_gen>https://apiprod.postoffice.co.za:8080/api/trn-manager/gen</south_africa_uri_gen>
                <south_africa_token>e78678b1-55f9-40dc-bbf5-b615533a94ef</south_africa_token>
                <south_africa_Authorization>6eaf13a0-d12e-4c8b-a073-b3c74308367d</south_africa_Authorization>
                <south_africa_url_order>https://apiprod.postoffice.co.za/IPSAPIService/ImportService.svc/rest/Mailitem
                </south_africa_url_order>
                <!-- southAfrica配置 -->

                <!--众瑞UPPS-->
                <zhongRui_url>http://*************:82/</zhongRui_url>
                <zhongRui_account>ZR2023041901</zhongRui_account>
                <zhongRui_password>zUjaEefM^g3X</zhongRui_password>


                <!--华磊客户账户推送url-->
                <hl_push_erp_url>http://www.bjzwy.hailei2018.com:8082/logistics/SyncZwyCustomer.html</hl_push_erp_url>
                <!--华磊客户账户推送url-->
                <hl_push_soline_url>http://www.bjzwy.hailei2018.com:8082/logistics/SyncZwyCustomerProduct.html
                </hl_push_soline_url>

                <!-- shopline start -->
                <shopline.url>https://openapi.oneship.io</shopline.url>
                <shopline.appKey>51da2e2332af2c369a02013550aa1fda34034e81</shopline.appKey>
                <shopline.appSecret>65664a4d23b2625edffb70e377b1dc5c8119d349</shopline.appSecret>
                <!-- shopline end -->

                <!--poland.inpost-->
                <poland.inpost.url>https://api-shipx-pl.easypack24.net/v1/</poland.inpost.url>
                <poland.inpost.token>
                    eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJzQlpXVzFNZzVlQnpDYU1XU3JvTlBjRWFveFpXcW9Ua2FuZVB3X291LWxvIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pXCFVeA7CGWQkvn1x3sxkHmWO1trwqTM8kZwnrn9FqLwm5VxUU55WI0yZxlfCv74gx6HWgve-EB4msaph7RljS0oNzXP5glIuZt_4oUhykXa6r1-hqbGRnZGaXbXIcN65V4nbH2sF-8OJloJ5MquuX4zI_kBxnNOiNCQLZGw2i_se7wN25BZzOj-aNDuQr4dHpmvKMvGSQkSEj6FcTZtBydFYBxWlVtt9LyaqNxR_t4BGWXEhXVtRapo7t5HtqOMKC3hYEPIZpxWFEtK5uyk5qsx3VxR2EGTEAlWeNeqYdnKLOsJmngjFV4q6JjcQeJAIfcRgkiHBTJg9oTsPw3-Yw
                </poland.inpost.token>
                <poland.inpost.organizationId>75329</poland.inpost.organizationId>
                <!--poland.inpost-->
                <!-- 千帆集运-->
                <qianfan.appKey>34614806</qianfan.appKey>
                <qianfan.secret>b267a7c4e31ae554be90c7923410f0ee</qianfan.secret>
                <qianfan.url>http://gw.api.taobao.com/router/rest</qianfan.url>
                <qianfan.url.https>https://eco.taobao.com/router/rest</qianfan.url.https>
                <!-- 千帆集运-->


            </properties>
        </profile>
        <profile>
            <id>local</id>
            <properties>
                <!--war包名称-->
                <finalName>CEOPLocal</finalName>
                <app_version>2.19.26</app_version>
                <profileId>local</profileId>
                <environment.name>local</environment.name>
                <environment.desc>local environment on windows server</environment.desc>
                <!--是否加载字典-->
                <dictionary.init_config>false</dictionary.init_config>
                <!--database-->
                <jdbc.url>********************************************</jdbc.url>
                <jdbc.username>CEOP</jdbc.username>
                <jdbc.password>CEOP</jdbc.password>
                <dataSource>DruidDataSource</dataSource>
                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>6379</db.redis.port>
                <db.redis.password>RedisDB.Cloud20211110</db.redis.password>
                <db.redis.database>0</db.redis.database>
                <db.redisson.address>redis://*************:6379</db.redisson.address>
                <db.redisson.password>RedisDB.Cloud20211110</db.redisson.password>
                <db.redisson.database>0</db.redisson.database>
                <historyRootPath>D:/bea/ceosLocalHistoryLog</historyRootPath>
                <appFilePath>D:/bea/appConfig</appFilePath>
                <dispatchNumberURL>http://mla.mailamericas.com/api/v1/reserve?access_token=SIN2887DF89SA0Z0299
                </dispatchNumberURL>
                <orderPlatformURL>http://**************/order/FastRpt/PDF_NEW.aspx
                </orderPlatformURL>
                <dataSource>DruidDataSource</dataSource>
                <!--阿姆斯特丹FBA-->
                <ams.tempUrl>http://************:8888/via/net</ams.tempUrl>
                <ams.viatoken.fba>2ef3e97f7c1ffdec0de489a5f62e3cbf</ams.viatoken.fba>
                <ams.viaurl>https://app-sandbox.viaeurope.com</ams.viaurl>
                <!--                <requestUrlCainiao>https://linkdaily.tbsandbox.com/gateway/link.do-->
                <!--                </requestUrlCainiao>-->
                <!-- 预发环境-->
                <requestUrlCainiao>https://link.cainiao.com/gateway/custom/open_integration_test_env</requestUrlCainiao>
                <requestUrlUseast>http://pre-useast-link.cainiao.com/gateway/link.do</requestUrlUseast>
                <signKey>v6lfQ5XH677s5I4835tgecOOBmZ9u7T9</signKey>
                <signKey4TW>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67</signKey4TW>
                <!--阿姆斯特丹FBA END-->
                <inter.littlepackage>http://localhost:8080/littlePackageQuery</inter.littlepackage>
                <interchange.url>http://*************:8002</interchange.url>
                <!-- weblogic-->
                <!--<weblogic.adminurl>t3://************:7001/</weblogic.adminurl>
                <weblogic.user>weblogic</weblogic.user>
                <weblogic.password>sinoairbea!%@</weblogic.password>-->
                <!-- log4j-->
                <log4j.rootLogger>DEBUG</log4j.rootLogger>
                <print.url>http://localhost:8080</print.url>
                <!--美线操作手册在文件服务器的地址-->
                <US.NoteBook>http://ecdowntest.sinoair.com:8081/download</US.NoteBook>
                <!--文件服务器下载地址-->
                <sinoair_file_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_ecdown_url_key>

                <!--文件服务器下载地址-->
                <sinoair_file_wb_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_wb_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_wb_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_wb_ecdown_url_key>

                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>

                <!-- data export job 配置 -->
                <data.export.url>http://************:9000</data.export.url>
                <data.export.ceos.code>CEOSTEST</data.export.ceos.code>
                <!--对接ACT Express-->
                <act.express.url>http://datatest.ufms.co.kr/if/order</act.express.url>

                <!--美国专线对接-->
                <sureline2_url>http://*************:18002</sureline2_url>
                <sureline2_AppKey>aa914890-1a67-4ce1-8abc-aa174c5e7c79</sureline2_AppKey>
                <sureline2_ClientSecret>94ddc303-bd12-4206-95fd-7f33adb8cef8</sureline2_ClientSecret>

                <!-- 万色对接信息 -->
                <wise_api_host_url>http://**************:7777</wise_api_host_url>
                <wise_api_key>qMuhp6Ktj</wise_api_key>

                <!--香港中通对接-->
                <hk_zto_getSelfExtractingPoint_url>http://**************:10003/index.php/I/Warehouse/pickup
                </hk_zto_getSelfExtractingPoint_url>
                <hk_zto_addOrder_url>http://**************:10003/index.php/I/Thirdparty/batchSave</hk_zto_addOrder_url>
                <hk_zto_api_id>16d87195707ac9db849979d22b3dd5d6</hk_zto_api_id>
                <hk_zto_api_key>f58cffd75eb58b1d4c061c3e2aa082f8</hk_zto_api_key>
                <hk_zto_referer>http://test.com</hk_zto_referer>

                <!--ups对接-->
                <ups_accessLicenseNumber>****************</ups_accessLicenseNumber>
                <ups_userId>Sinotrans123</ups_userId>
                <ups_password>Ups12345</ups_password>
                <ups_account>6545E2</ups_account>
                <ups_shipConfirm_url>https://wwwcie.ups.com/ups.app/xml/ShipConfirm</ups_shipConfirm_url>
                <ups_shipAccept_url>https://wwwcie.ups.com/ups.app/xml/ShipAccept</ups_shipAccept_url>
                <ups_void_url>https://wwwcie.ups.com/ups.app/xml/Void</ups_void_url>

                <!--wishB2B-->
                <wishB2B_Base_url>https://***********</wishB2B_Base_url>
                <wishB2B_api_key>JHBia2RmMiQxMDAkTklZd2h0QjZ6MWtyQmFEVXV0ZTZWdyRjdXhMR09iaWRGdjNQam8wU0NYQU9IYzd1T0U=
                </wishB2B_api_key>
                <!--wish店铺模式 test api key-->
                <wishB2B_api_key_FUSION>JHBia2RmMiQxMDXXXXXXXXXXXXXXU3TXZHYmdWQy92YWw5SFk=</wishB2B_api_key_FUSION>


                <!-- colisprive -->
                <colisprive.wsdl.url>https://www.test.colisprive.com/Externe/WSCP.asmx</colisprive.wsdl.url>
                <colisprive.wsdl.username>MONLOGIN</colisprive.wsdl.username>
                <colisprive.wsdl.password>MonMotDePasse</colisprive.wsdl.password>
                <colisprive.wsdl.CPCustoID>1G</colisprive.wsdl.CPCustoID>
                <colisprive.wsdl.AccountID>EM190319</colisprive.wsdl.AccountID>


                <!-- ESNAD 配置开始 -->
                <ESNAD_UPLOAD_ORDER_URL>https://dms.esnadexpress.com/api/v1/process</ESNAD_UPLOAD_ORDER_URL>
                <ESNAD_UPLOAD_ORDER_API_KEY>otYYNweFcmDSCmIeEqhAtMw5KKgaFzeX</ESNAD_UPLOAD_ORDER_API_KEY>
                <ESNAD_PDF_INVOICE_URL>https://dms.esnadexpress.com/api/v1/process/get_process_pdf
                </ESNAD_PDF_INVOICE_URL>
                <ESNAD_MER_CHART_CODE>HKCHSIN01</ESNAD_MER_CHART_CODE>
                <!-- ESNAD 配置结束 -->
                <!--GLS对接-->
                <GLS_IP>**************</GLS_IP>
                <GLS_PORT>3030</GLS_PORT>
                <GLS_T8700>BE 610</GLS_T8700>
                <GLS_T805>*********</GLS_T805>
                <GLS_T8914>056aaacL2g</GLS_T8914>
                <GLS_T8915>**********</GLS_T8915>
                <GLS_T805_CAINIAO>*********</GLS_T805_CAINIAO>
                <GLS_T8914_CAINIAO>056aaacZ8i</GLS_T8914_CAINIAO>
                <!--艾莉薇对接 start-->
                <ALW_REQUEST_URL>http://api.ollyway.com</ALW_REQUEST_URL>
                <ALW_REQUEST_USER>A720</ALW_REQUEST_USER>
                <ALW_REQUEST_TOKEN>0b3f75c1901eada9b50f5121a6585f46</ALW_REQUEST_TOKEN>
                <ALW_REQUEST_USER_SNR>A743</ALW_REQUEST_USER_SNR>
                <ALW_REQUEST_TOKEN_SNR>5ddde2cd7a67e7195d39790dcd4e729f</ALW_REQUEST_TOKEN_SNR>
                <!--艾莉薇对接 end-->

                <!--华得士作为末端 德国DHL 派送 start -->
                <REQUEST_HDS_API_URL>http://oms.gfsbiz.com.cn/default/svc/web-service</REQUEST_HDS_API_URL>
                <REQUEST_HDS_APPTOKEN>41ac86b27fe34caa8f9aa56560fafe03</REQUEST_HDS_APPTOKEN>
                <REQUEST_HDS_APPKEY>41ac86b27fe34caa8f9aa56560fafe03f358f2d3b00214a7a73cf7e578198929
                </REQUEST_HDS_APPKEY>
                <!--华得士作为末端 德国DHL 派送 start -->

                <!--Wise末端派送 start -->
                <WISE_API_KEY>d779c1e4c8def888168929a80bfa2e4f11711</WISE_API_KEY>
                <!--Wise末端派送 END -->
                <GETTOKEN>JQeHDiMfQ7XsPcg9bttcuDZLQnNm4JWw9f9UJjz6omsBUWRCBtGy2s2X3KAQ</GETTOKEN>
                <SENDMANIFESTURL>https://api-stg.k-parcel.com/api/shipment/create</SENDMANIFESTURL>
                <!--FBA 互联通 start -->
                <HLT_APPKEY>05fe47c9bf5a11e989e65254000627ef</HLT_APPKEY>
                <HLT_URL>http://**************:8086/xms/</HLT_URL>
                <!--FBA 互联通 END -->

                <!--footlocker_singapore-->
                <client_id>********************************</client_id>
                <client_secret>d45a7855b0d944a4b6690d8e8ad79e52</client_secret>
                <pickup_address_id>98989012</pickup_address_id>
                <fl_singapore_token_url>https://api-sandbox.ninjavan.co/SG/2.0/oauth/access_token
                </fl_singapore_token_url>
                <fl_singapore_create_oreder_url>https://api-sandbox.ninjavan.co/SG/4.1/orders
                </fl_singapore_create_oreder_url>

                <!--小米结算账号 -->
                <xiaomi.socode>HKG1000000739</xiaomi.socode>
                <ups_emi_validation_url>
                    https://testyw-gspk.orangeconnex.com:6443/api/shipment/si/package/v1/validation
                </ups_emi_validation_url>
                <ups_emi_label_url>https://testyw-gspk.orangeconnex.com:6443/api/shipment/si/package/v1/label
                </ups_emi_label_url>
                <ups_emi_apiKey>c3aa211f-514e-4986-8c17-5c6bf9aa8c80</ups_emi_apiKey>
                <ups_emi_securityKey>********-883a-4d85-b665-90534ef3c94c</ups_emi_securityKey>
                <!--B2C europe 的url -->
                <b2c.url>https://finalmilelabel-live.b2ceurope.eu</b2c.url>
                <!--B2C europe 的password -->
                <b2c.pass>75A724D1002152C04AB3003BAF1051EDBE947050</b2c.pass>
                <!--ITOM华德仕 -->
                <ITOMHDTOKEN>Basic QzEwMDAzJmRQWjJLUlM2ZXJRPQ==</ITOMHDTOKEN>
                <ITOMHDCreateOrderURL>https://sengiexpress.com/api/WayBill/CreateOrder</ITOMHDCreateOrderURL>
                <ITOMHDGetOrderURL>https://sengiexpress.com/api/Label/Print</ITOMHDGetOrderURL>
                <!--对接EMS-新一代-->
                <EMS_WH_CODE>23803100</EMS_WH_CODE>
                <EMS_KEY>8nVV209U16ml9q63</EMS_KEY>
                <EMS_EC_COMPANY_ID>90000003465705</EMS_EC_COMPANY_ID>
                <EMS_GET_ORDER_NO_URL>https://**************:443/pcpErp-web/a/pcp/barCodesAssgine/barCodeService
                </EMS_GET_ORDER_NO_URL>
                <EMS_SEND_ORDER_BACK_URL>https://**************:443/pcpErp-web/a/pcp/orderService/OrderReceiveBack
                </EMS_SEND_ORDER_BACK_URL>
                <EMS_SEND_ORDER_URL>https://**************:443/pcpErp-web/a/pcp/orderService/orderReceive
                </EMS_SEND_ORDER_URL>
                <EMS_GET_LABEL_URL>https://**************:443/pcpErp-web/a/pcp/surface/download</EMS_GET_LABEL_URL>
                <!--对接IQS-->
                <IQS_CREATE_URL>http://test.iqsgsc.com/v2/shipment/create</IQS_CREATE_URL>
                <IQS_TOKEN>access_token_2966ec3dde7684842862ecadb55cf02fd33efeb8</IQS_TOKEN>
                <IQS_ACCOUNT>RUH0011test</IQS_ACCOUNT>
                <IQS_UID><EMAIL></IQS_UID>

                <!--新美线 PB对接参数-->
                <PB_GET_TOKEN_AUTHORIZATION>Basic bXBtcUhXdDN4ekRDUGhmMXVOcmZyYk5HT1hTZFRocnk6cWxZWm1NZWZHTFcxOHZvOQ==
                </PB_GET_TOKEN_AUTHORIZATION>

                <QT_SOCODE>ZSU168192,********,HGH177412</QT_SOCODE>

                <!-- MBB -->
                <MBB_ADD_EXPRESS_ORDER_URL>
                    http://oms.mbb-logistics.com/service/Storage_addExpressOrder.action?userId=test&amp;token=AFYIzHVSwEnvCaP2aXAbBlSeB4wkmm
                </MBB_ADD_EXPRESS_ORDER_URL>
                <AEPOST_URL>https://osbtest.epg.gov.ae/ebs/genericapi/booking/rest/CreateBooking</AEPOST_URL>
                <AEPOST_HSCODE_URL>
                    https://osb.epg.gov.ae/ebs/epg.pos.CustomerService.lookups.rest.noauth/GetCustomDescriptionsV1
                </AEPOST_HSCODE_URL>
                <AEPOST_TOKEN>C175120</AEPOST_TOKEN>

                <!--                wish哥伦比亚末端预报推送-->
                <WISH_COLOMBIA_SHIPPING_PUSH_URL>
                    http://svc1.sipost.co/WcfServiceSPOKE/ServiceSPOKE.svc/PostShipping
                </WISH_COLOMBIA_SHIPPING_PUSH_URL>

                <!--捷网英邮的配置 start-->
                <JWYY_USERNAME>2158</JWYY_USERNAME>
                <JWYY_PASSWORD>Jnet2017qwe</JWYY_PASSWORD>
                <!--捷网英邮的配置 end-->
                <!--以色列邮政对接 test start -->
                <ISRAELPOST_URL>https://apimfttst.israelpost.co.il</ISRAELPOST_URL>

                <SinoairExp_Username><EMAIL></SinoairExp_Username>
                <SinoairExp_Password>Sinoair@321</SinoairExp_Password>
                <SinoairExp_key>43703121015f44649892bf11902bbdf6</SinoairExp_key>

                <SinoairTrk_Username><EMAIL></SinoairTrk_Username>
                <SinoairTrk_Password>NvaV_B81*WPW</SinoairTrk_Password>
                <SinoairTrk_key>43703121015f44649892bf11902bbdf6</SinoairTrk_key>

                <SinoairEco_Username><EMAIL></SinoairEco_Username>
                <SinoairEco_Password>_h9BZk3QozcJ</SinoairEco_Password>
                <SinoairEco_key>43703121015f44649892bf11902bbdf6</SinoairEco_key>

                <SinoairDdp_Username><EMAIL></SinoairDdp_Username>
                <SinoairDdp_Password>USYqD3Y*At64</SinoairDdp_Password>
                <SinoairDdp_key>43703121015f44649892bf11902bbdf6</SinoairDdp_key>
                <!--以色列邮政对接  test end-->
                <!--                天猫直发app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>0743R02E262U38UYADNNr653M7lO3dn2
                </SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>
                <!--                天猫集运台湾app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67
                </SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>

                <!-- shopee 配置开始 start................ -->
                <shopee.url.3.1>https://external2.shopee.sg/lcs/uat/api/first_leg/repack_carton/lh_sinotrans_eu
                </shopee.url.3.1>
                <shopee.url.3.2>https://external2.shopee.sg/lcs/uat/api/first_leg/loading_carton/lh_sinotrans_eu
                </shopee.url.3.2>
                <shopee.url.3.3>https://external2.shopee.sg/lcs/uat/api/first_leg/tracking_carton/lh_sinotrans_eu
                </shopee.url.3.3>
                <shopee.usename>lh_sinotrans_eu</shopee.usename>
                <shopee.password>testtesttesttesttesttesttest123456</shopee.password>
                <!-- shopee 配置开始 End................ -->

                <!-- UBI 配置 start............ -->
                <UBI_URL_IP>http://qa.etowertech.com</UBI_URL_IP>
                <UBI_URL_TOKEN>testqMfBG8xvSKJKT6O2Pz9</UBI_URL_TOKEN>
                <UBI_URL_SECURET>H2daH9s2IJmCQYQ77oEiAQ</UBI_URL_SECURET>

                <UBI_URL_TOKEN_HD>pcloBCvuQOgiIwVT1ALvGz</UBI_URL_TOKEN_HD>
                <UBI_URL_SECURET_HD>fRTlwEg1Yoy6S5eLssTe0w</UBI_URL_SECURET_HD>

                <UBI_URL_TOKEN_HN>pclZP4TsihBI5eLTtlbRCh</UBI_URL_TOKEN_HN>
                <UBI_URL_SECURET_HN>P8MXZhg77ab_T7NbcRpjVA</UBI_URL_SECURET_HN>
                <!-- UBI 配置 end -->

                <!-- AMS配置 -->
                <ams.password>a96dbb5cb2bb43036a79d3e1b39680ed</ams.password>
                <ams.url>https://app-sandbox.viaeurope.com</ams.url>
                <!-- AMS配置 -->

                <!-- southAfrica配置 -->
                <south_africa_uri_gen>http://apitst.postoffice.co.za:443/api/trn-manager/gen</south_africa_uri_gen>
                <south_africa_token>719d6d08-6fb5-4ab5-ab74-33d83258d762</south_africa_token>
                <south_africa_Authorization>e7da93d0-2245-4257-b179-70b74d6c67f7</south_africa_Authorization>
                <south_africa_url_order>
                    http://apitst.postoffice.co.za:8084/IPSAPIService/ImportService.svc/rest/Mailitem
                </south_africa_url_order>
                <!-- southAfrica配置 -->


                <!-- SPX配置开始 -->
                <SPX_URL>https://rps.miqexpress.com/rps-api</SPX_URL>
                <SPX_TOKEN>45ea18-ff8005-15e962-05165d-3348da</SPX_TOKEN>
                <!-- SPX配置结束 -->

                <!-- 易云通的配置开始 -->
                <yyt_url>https://api.y2t.com/cb-order-dev/webapi/getBillUrl</yyt_url>
                <yyt_apiAppKey>APIDccKized4ulfwtVaG1rb2jo8ddzoc777yprn9</yyt_apiAppKey>
                <yyt_apiAppSecret>61B6y8eemw79lydx134thN7scesyygjjmckgFg47</yyt_apiAppSecret>
                <yyt_boxListUrl>https://api.y2t.com/cb-order-dev/webapi/importBoxNo</yyt_boxListUrl>
                <!-- 易云通的配置结束 -->


                <!--华磊客户账户推送url-->
                <hl_push_erp_url>http://**************:8082/logistics/SyncZwyCustomer.html</hl_push_erp_url>
                <!--华磊客户账户推送url-->
                <hl_push_soline_url>http://**************:8082/logistics/SyncZwyCustomerProduct.html
                </hl_push_soline_url>

                <!--众瑞UPPS-->
                <zhongRui_url>http://*************:3168/newwl/</zhongRui_url>
                <zhongRui_account>1006106</zhongRui_account>
                <zhongRui_password>123456</zhongRui_password>

                <!-- shopline start -->
                <shopline.url>https://logistic-op-gw-sandbox.myshoplinestg.com</shopline.url>
                <shopline.appKey>3a4dcfab4d3d2eb30a400b565f842a2bd229b253</shopline.appKey>
                <shopline.appSecret>fda46930eaf61f69a6ae5a69212b94c0635f2217</shopline.appSecret>
                <!-- shopline end -->

                <!--poland.inpost-->
                <poland.inpost.url>https://sandbox-api-shipx-pl.easypack24.net/v1/</poland.inpost.url>
                <poland.inpost.token>
                    eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJkVzROZW9TeXk0OHpCOHg4emdZX2t5dFNiWHY3blZ0eFVGVFpzWV9TUFA4In0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QDDP_qsnnFxyNyjDUm6Sj6bihLF_dSZSp54cQ1xPAfvh7JvrS1_c0WaNdTEGYMN0WuIhGVgJxF1W9aXabRLNy7B1a0ShzPtBhqQyVdyn7fep1XUiOGz-IVGw2wbYLz1s8dZd8msk8wYB6X1ezf-7vRbGRx_jsKMXFMR--i7Y3Gw80B1gP4-iP9zqV-zk9m76T5LBI2ALjPA3RTZ8HWW7nYFTdea9cwWFKFRtc3BX-pOHDqGwzUZQ1RCBsSaMC7_fFXJyJK35X7XTdpV6pArL_7xyOAMcIcgi8PTvtX3lCwCOO2rHz4ehcNQ6CbNL4IjT0X9eZBmPRVhyDoRLmkJuyQ
                </poland.inpost.token>
                <poland.inpost.organizationId>3308</poland.inpost.organizationId>
                <!--poland.inpost-->
                <!-- 千帆集运-->
                <qianfan.appKey>34614806</qianfan.appKey>
                <qianfan.secret>b267a7c4e31ae554be90c7923410f0ee</qianfan.secret>
                <qianfan.url>http://gw.api.taobao.com/router/rest</qianfan.url>
                <qianfan.url.https>https://eco.taobao.com/router/rest</qianfan.url.https>
                <!-- 千帆集运-->
            </properties>
            <activation>
                <activeByDefault>true</activeByDefault>
            </activation>
        </profile>
        <profile>
            <id>test</id>
            <properties>
                <finalName>CEOPTest</finalName>
                <app_version>2.19.26</app_version>
                <profileId>test</profileId>
                <environment.name>test</environment.name>
                <environment.desc>test environment on linux server</environment.desc>
                <!--是否加载字典-->
                <dictionary.init_config>true</dictionary.init_config>
                <!--database-->
                <jdbc.url>********************************************</jdbc.url>
                <jdbc.username>CEOP</jdbc.username>
                <jdbc.password>CEOP</jdbc.password>
                <dataSource>DruidDataSource</dataSource>
                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>6379</db.redis.port>
                <db.redis.password>RedisDB.Cloud20211110</db.redis.password>
                <db.redis.database>0</db.redis.database>
                <db.redisson.address>redis://*************:6379</db.redisson.address>
                <db.redisson.password>RedisDB.Cloud20211110</db.redisson.password>
                <db.redisson.database>0</db.redisson.database>
                <!--                <dataSource>DruidDataSource</dataSource>-->
                <historyRootPath>/bea/ceosTestHistoryLog</historyRootPath>
                <appFilePath>/bea/appConfig</appFilePath>
                <dispatchNumberURL>http://mla.theegg.com.ar/api/v1/reserve?access_token=SIN2887DF89SA0Z0299
                </dispatchNumberURL>
                <orderPlatformURL>http://**************/order/FastRpt/PDF_NEW.aspx
                </orderPlatformURL>
                <requestUrlCainiao>https://prelink.cainiao.com/gateway/link.do</requestUrlCainiao>
                <signKey>v6lfQ5XH677s5I4835tgecOOBmZ9u7T9</signKey>
                <signKey4TW>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67</signKey4TW>
                <requestUrlUseast>http://pre-useast-link.cainiao.com/gateway/link.do</requestUrlUseast>

                <!--阿姆斯特丹FBA-->
                <ams.tempUrl>http://************:8888/via/net</ams.tempUrl>
                <ams.viatoken.fba>2ef3e97f7c1ffdec0de489a5f62e3cbf</ams.viatoken.fba>
                <ams.viaurl>https://app-sandbox.viaeurope.com</ams.viaurl>
                <!--阿姆斯特丹FBA END-->
                <inter.littlepackage>http://************:8888/littlePackageQuery</inter.littlepackage>
                <interchange.url>http://*************:8002</interchange.url>
                <!-- weblogic-->
                <!-- <weblogic.adminurl>t3://172.17.0.227:7001/</weblogic.adminurl>
                 <weblogic.user>weblogic</weblogic.user>
                 <weblogic.password>sinoairbea!%@</weblogic.password>-->
                <!-- log4j-->
                <log4j.rootLogger>debug</log4j.rootLogger>
                <print.url>http://************:8001</print.url>
                <!--美线操作手册在文件服务器的地址-->
                <US.NoteBook>http://ecdowntest.sinoair.com:8081/download</US.NoteBook>

                <!--文件服务器下载地址-->
                <sinoair_file_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_ecdown_url_key>

                <!--文件服务器下载地址-->
                <sinoair_file_wb_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_wb_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_wb_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_wb_ecdown_url_key>

                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>

                <!-- data export job 配置 -->
                <data.export.url>http://************:9000</data.export.url>
                <data.export.ceos.code>CEOSTEST</data.export.ceos.code>
                <WISH_COLOMBIA_SHIPPING_PUSH_URL>
                    http://svc1.sipost.co/WcfServiceSPOKE/ServiceSPOKE.svc/PostShipping
                </WISH_COLOMBIA_SHIPPING_PUSH_URL>

                <!--对接ACT Express-->
                <act.express.url>http://datatest.ufms.co.kr/if/order</act.express.url>

                <!--美线对接-->
                <sureline2_url>http://*************:18002</sureline2_url>
                <sureline2_AppKey>aa914890-1a67-4ce1-8abc-aa174c5e7c79</sureline2_AppKey>
                <sureline2_ClientSecret>94ddc303-bd12-4206-95fd-7f33adb8cef8</sureline2_ClientSecret>

                <!-- 万色对接信息 -->
                <wise_api_host_url>http://**************:7777</wise_api_host_url>
                <wise_api_key>qMuhp6Ktj</wise_api_key>

                <!--香港中通对接-->
                <hk_zto_getSelfExtractingPoint_url>http://**************:10003/index.php/I/Warehouse/pickup
                </hk_zto_getSelfExtractingPoint_url>
                <hk_zto_addOrder_url>http://**************:10003/index.php/I/Thirdparty/batchSave</hk_zto_addOrder_url>
                <hk_zto_api_id>16d87195707ac9db849979d22b3dd5d6</hk_zto_api_id>
                <hk_zto_api_key>f58cffd75eb58b1d4c061c3e2aa082f8</hk_zto_api_key>
                <hk_zto_referer>http://test.com</hk_zto_referer>

                <!--ups对接-->
                <ups_accessLicenseNumber>****************</ups_accessLicenseNumber>
                <ups_userId>Sinotrans123</ups_userId>
                <ups_password>Ups12345</ups_password>
                <ups_account>6545E2</ups_account>
                <ups_shipConfirm_url>https://wwwcie.ups.com/ups.app/xml/ShipConfirm</ups_shipConfirm_url>
                <ups_shipAccept_url>https://wwwcie.ups.com/ups.app/xml/ShipAccept</ups_shipAccept_url>
                <ups_void_url>https://wwwcie.ups.com/ups.app/xml/Void</ups_void_url>

                <!--wishB2B-->
                <wishB2B_Base_url>https://***********</wishB2B_Base_url>
                <wishB2B_api_key>JHBia2RmMiQxMDAkTklZd2h0QjZ6MWtyQmFEVXV0ZTZWdyRjdXhMR09iaWRGdjNQam8wU0NYQU9IYzd1T0U=
                </wishB2B_api_key>
                <!--wish店铺模式 test api key-->
                <wishB2B_api_key_FUSION>JHBia2RmMiQxMDXXXXXXXXXXXXXXU3TXZHYmdWQy92YWw5SFk=</wishB2B_api_key_FUSION>

                <!-- colisprive -->
                <colisprive.wsdl.url>https://www.test.colisprive.com/Externe/WSCP.asmx</colisprive.wsdl.url>
                <colisprive.wsdl.username>MONLOGIN</colisprive.wsdl.username>
                <colisprive.wsdl.password>MonMotDePasse</colisprive.wsdl.password>
                <colisprive.wsdl.CPCustoID>1G</colisprive.wsdl.CPCustoID>
                <colisprive.wsdl.AccountID>EM190319</colisprive.wsdl.AccountID>


                <!-- ESNAD 配置开始 -->
                <ESNAD_UPLOAD_ORDER_URL>https://dms.esnadexpress.com/api/v1/process</ESNAD_UPLOAD_ORDER_URL>
                <ESNAD_UPLOAD_ORDER_API_KEY>otYYNweFcmDSCmIeEqhAtMw5KKgaFzeX</ESNAD_UPLOAD_ORDER_API_KEY>
                <ESNAD_PDF_INVOICE_URL>https://dms.esnadexpress.com/api/v1/process/get_process_pdf
                </ESNAD_PDF_INVOICE_URL>
                <ESNAD_MER_CHART_CODE>HKCHSIN01</ESNAD_MER_CHART_CODE>
                <!-- ESNAD 配置结束 -->
                <!--艾莉薇对接 start-->
                <ALW_REQUEST_URL>http://api.ollyway.com</ALW_REQUEST_URL>
                <ALW_REQUEST_USER>A720</ALW_REQUEST_USER>
                <ALW_REQUEST_TOKEN>0b3f75c1901eada9b50f5121a6585f46</ALW_REQUEST_TOKEN>
                <ALW_REQUEST_USER_SNR>A743</ALW_REQUEST_USER_SNR>
                <ALW_REQUEST_TOKEN_SNR>5ddde2cd7a67e7195d39790dcd4e729f</ALW_REQUEST_TOKEN_SNR>
                <!--艾莉薇对接 end-->

                <!--华得士作为末端 德国DHL 派送 start -->
                <REQUEST_HDS_API_URL>http://oms.gfsbiz.com.cn/default/svc/web-service</REQUEST_HDS_API_URL>
                <REQUEST_HDS_APPTOKEN>41ac86b27fe34caa8f9aa56560fafe03</REQUEST_HDS_APPTOKEN>
                <REQUEST_HDS_APPKEY>41ac86b27fe34caa8f9aa56560fafe03f358f2d3b00214a7a73cf7e578198929
                </REQUEST_HDS_APPKEY>
                <!--华得士作为末端 德国DHL 派送 start -->

                <!--GLS对接-->
                <GLS_IP>**************</GLS_IP>
                <GLS_PORT>3030</GLS_PORT>
                <GLS_T805>*********</GLS_T805>
                <GLS_T8700>BE 610</GLS_T8700>
                <GLS_T8914>056aaacL2g</GLS_T8914>
                <GLS_T8915>**********</GLS_T8915>
                <GLS_T805_CAINIAO>*********</GLS_T805_CAINIAO>
                <GLS_T8914_CAINIAO>056aaacZ8i</GLS_T8914_CAINIAO>

                <!--Wise末端派送 start -->
                <WISE_API_KEY>d779c1e4c8def888168929a80bfa2e4f11711</WISE_API_KEY>
                <!--Wise末端派送 END -->
                <GETTOKEN>JQeHDiMfQ7XsPcg9bttcuDZLQnNm4JWw9f9UJjz6omsBUWRCBtGy2s2X3KAQ</GETTOKEN>
                <SENDMANIFESTURL>https://api-stg.k-parcel.com/api/shipment/create</SENDMANIFESTURL>
                <!--FBA 互联通 start -->
                <HLT_APPKEY>05fe47c9bf5a11e989e65254000627ef</HLT_APPKEY>
                <HLT_URL>http://**************:8086/xms/</HLT_URL>
                <!--FBA 互联通 END -->

                <!--footlocker_singapore-->
                <client_id>********************************</client_id>
                <client_secret>d45a7855b0d944a4b6690d8e8ad79e52</client_secret>
                <pickup_address_id>98989012</pickup_address_id>
                <fl_singapore_token_url>https://api-sandbox.ninjavan.co/SG/2.0/oauth/access_token
                </fl_singapore_token_url>
                <fl_singapore_create_oreder_url>https://api-sandbox.ninjavan.co/SG/4.1/orders
                </fl_singapore_create_oreder_url>

                <!--小米的结算账号 -->
                <xiaomi.socode>HKG1000000739</xiaomi.socode>

                <!--B2C europe 的url -->
                <b2c.url>https://finalmilelabel-live.b2ceurope.eu</b2c.url>
                <!--B2C europe 的password -->
                <b2c.pass>75A724D1002152C04AB3003BAF1051EDBE947050</b2c.pass>
                <!--ITOM华德仕 -->
                <ITOMHDTOKEN>Basic QzEwMDAzJmRQWjJLUlM2ZXJRPQ==</ITOMHDTOKEN>
                <ITOMHDCreateOrderURL>https://sengiexpress.com/api/WayBill/CreateOrder</ITOMHDCreateOrderURL>
                <ITOMHDGetOrderURL>https://sengiexpress.com/api/Label/Print</ITOMHDGetOrderURL>
                <!--对接EMS-新一代-->
                <EMS_WH_CODE>23803100</EMS_WH_CODE>
                <EMS_KEY>8nVV209U16ml9q63</EMS_KEY>
                <EMS_EC_COMPANY_ID>90000003465705</EMS_EC_COMPANY_ID>
                <EMS_GET_ORDER_NO_URL>https://**************:443/pcpErp-web/a/pcp/barCodesAssgine/barCodeService
                </EMS_GET_ORDER_NO_URL>
                <EMS_SEND_ORDER_BACK_URL>https://**************:443/pcpErp-web/a/pcp/orderService/OrderReceiveBack
                </EMS_SEND_ORDER_BACK_URL>
                <EMS_SEND_ORDER_URL>https://**************:443/pcpErp-web/a/pcp/orderService/orderReceive
                </EMS_SEND_ORDER_URL>
                <EMS_GET_LABEL_URL>https://**************:443/pcpErp-web/a/pcp/surface/download</EMS_GET_LABEL_URL>
                <!--对接IQS-->
                <IQS_CREATE_URL>http://test.iqsgsc.com/v2/shipment/create</IQS_CREATE_URL>
                <IQS_TOKEN>access_token_2966ec3dde7684842862ecadb55cf02fd33efeb8</IQS_TOKEN>
                <IQS_ACCOUNT>RUH0011test</IQS_ACCOUNT>
                <IQS_UID><EMAIL></IQS_UID>

                <!--新美线 PB对接参数-->
                <PB_GET_TOKEN_AUTHORIZATION>Basic bXBtcUhXdDN4ekRDUGhmMXVOcmZyYk5HT1hTZFRocnk6cWxZWm1NZWZHTFcxOHZvOQ==
                </PB_GET_TOKEN_AUTHORIZATION>

                <QT_SOCODE>ZSU168192,********,HGH177412</QT_SOCODE>

                <!-- MBB -->
                <MBB_ADD_EXPRESS_ORDER_URL>
                    http://oms.mbb-logistics.com/service/Storage_addExpressOrder.action?userId=test&amp;token=AFYIzHVSwEnvCaP2aXAbBlSeB4wkmm
                </MBB_ADD_EXPRESS_ORDER_URL>
                <AEPOST_URL>https://osbtest.epg.gov.ae/ebs/genericapi/booking/rest/CreateBooking</AEPOST_URL>
                <AEPOST_HSCODE_URL>
                    https://osb.epg.gov.ae/ebs/epg.pos.CustomerService.lookups.rest.noauth/GetCustomDescriptionsV1
                </AEPOST_HSCODE_URL>
                <AEPOST_TOKEN>C175120</AEPOST_TOKEN>
                <!--                wish哥伦比亚末端预报推送-->

                <!--捷网英邮的配置 start-->
                <JWYY_USERNAME>2158</JWYY_USERNAME>
                <JWYY_PASSWORD>Jnet2017qwe</JWYY_PASSWORD>
                <!--捷网英邮的配置 end-->

                <!--以色列邮政对接 test start -->
                <ISRAELPOST_URL>https://apimfttst.israelpost.co.il</ISRAELPOST_URL>

                <SinoairExp_Username><EMAIL></SinoairExp_Username>
                <SinoairExp_Password>Sinoair@321</SinoairExp_Password>
                <SinoairExp_key>43703121015f44649892bf11902bbdf6</SinoairExp_key>

                <SinoairTrk_Username><EMAIL></SinoairTrk_Username>
                <SinoairTrk_Password>NvaV_B81*WPW</SinoairTrk_Password>
                <SinoairTrk_key>43703121015f44649892bf11902bbdf6</SinoairTrk_key>

                <SinoairEco_Username><EMAIL></SinoairEco_Username>
                <SinoairEco_Password>_h9BZk3QozcJ</SinoairEco_Password>
                <SinoairEco_key>43703121015f44649892bf11902bbdf6</SinoairEco_key>

                <SinoairDdp_Username><EMAIL></SinoairDdp_Username>
                <SinoairDdp_Password>USYqD3Y*At64</SinoairDdp_Password>
                <SinoairDdp_key>43703121015f44649892bf11902bbdf6</SinoairDdp_key>
                <!--以色列邮政对接  test end-->
                <!--                天猫直发app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>0743R02E262U38UYADNNr653M7lO3dn2
                </SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>
                <!--                天猫集运台湾app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67
                </SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>
                <!-- shopee 配置开始 start................ -->
                <shopee.url.3.1>https://external2.shopee.sg/lcs/uat/api/first_leg/repack_carton/lh_sinotrans_eu
                </shopee.url.3.1>
                <shopee.url.3.2>https://external2.shopee.sg/lcs/uat/api/first_leg/loading_carton/lh_sinotrans_eu
                </shopee.url.3.2>
                <shopee.url.3.3>https://external2.shopee.sg/lcs/uat/api/first_leg/tracking_carton/lh_sinotrans_eu
                </shopee.url.3.3>
                <shopee.usename>lh_sinotrans_eu</shopee.usename>
                <shopee.password>testtesttesttesttesttesttest123456</shopee.password>
                <!-- shopee 配置开始 End................ -->

                <!-- UBI 配置 start............ -->
                <UBI_URL_IP>http://qa.etowertech.com</UBI_URL_IP>
                <UBI_URL_TOKEN>test5AdbzO5OEeOpvgAVXUFE0A</UBI_URL_TOKEN>
                <UBI_URL_SECURET>79db9e5OEeOpvgAVXUFWSD</UBI_URL_SECURET>

                <UBI_URL_TOKEN_HD>pcloBCvuQOgiIwVT1ALvGz</UBI_URL_TOKEN_HD>
                <UBI_URL_SECURET_HD>fRTlwEg1Yoy6S5eLssTe0w</UBI_URL_SECURET_HD>

                <UBI_URL_TOKEN_HN>pclZP4TsihBI5eLTtlbRCh</UBI_URL_TOKEN_HN>
                <UBI_URL_SECURET_HN>P8MXZhg77ab_T7NbcRpjVA</UBI_URL_SECURET_HN>
                <!-- UBI 配置 end -->

                <!-- AMS配置 -->
                <ams.password>a96dbb5cb2bb43036a79d3e1b39680ed</ams.password>
                <ams.url>https://app-sandbox.viaeurope.com</ams.url>
                <!-- AMS配置 -->

                <!-- southAfrica配置 -->
                <south_africa_uri_gen>http://apitst.postoffice.co.za:443/api/trn-manager/gen</south_africa_uri_gen>
                <south_africa_token>719d6d08-6fb5-4ab5-ab74-33d83258d762</south_africa_token>
                <south_africa_Authorization>e7da93d0-2245-4257-b179-70b74d6c67f7</south_africa_Authorization>
                <south_africa_url_order>
                    http://apitst.postoffice.co.za:8084/IPSAPIService/ImportService.svc/rest/Mailitem
                </south_africa_url_order>
                <!-- southAfrica配置 -->

                <!-- SPX配置开始 -->
                <SPX_URL>http://rps.sandbox.miqexpress.com/rps-api</SPX_URL>
                <SPX_TOKEN>53dd4e-0346fd-35f72b-e8b933-6afdaf</SPX_TOKEN>
                <!-- SPX配置结束 -->

                <!-- 易云通的配置开始 -->
                <yyt_url>https://api.y2t.com/cb-order-dev/webapi/getBillUrl</yyt_url>
                <yyt_apiAppKey>APIDccKized4ulfwtVaG1rb2jo8ddzoc777yprn9</yyt_apiAppKey>
                <yyt_apiAppSecret>61B6y8eemw79lydx134thN7scesyygjjmckgFg47</yyt_apiAppSecret>
                <yyt_boxListUrl>https://api.y2t.com/cb-order-dev/webapi/importBoxNo</yyt_boxListUrl>
                <!-- 易云通的配置结束 -->

                <!--众瑞UPPS-->
                <zhongRui_url>http://*************:3168/newwl/</zhongRui_url>
                <zhongRui_account>1006106</zhongRui_account>
                <zhongRui_password>123456</zhongRui_password>

                <!--华磊客户账户推送url-->
                <hl_push_erp_url>http://www.bjzwy.hailei2018.com:8082/logistics/SyncZwyCustomer.html</hl_push_erp_url>
                <!--华磊客户账户推送url-->
                <hl_push_soline_url>http://www.bjzwy.hailei2018.com:8082/logistics/SyncZwyCustomerProduct.html
                </hl_push_soline_url>

                <!-- shopline start -->
                <shopline.url>https://logistic-op-gw-sandbox.myshoplinestg.com</shopline.url>
                <shopline.appKey>3a4dcfab4d3d2eb30a400b565f842a2bd229b253</shopline.appKey>
                <shopline.appSecret>fda46930eaf61f69a6ae5a69212b94c0635f2217</shopline.appSecret>
                <!-- shopline end -->

                <!--poland.inpost-->
                <poland.inpost.url>https://sandbox-api-shipx-pl.easypack24.net/v1/</poland.inpost.url>
                <poland.inpost.token>
                    eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJkVzROZW9TeXk0OHpCOHg4emdZX2t5dFNiWHY3blZ0eFVGVFpzWV9TUFA4In0.********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.QDDP_qsnnFxyNyjDUm6Sj6bihLF_dSZSp54cQ1xPAfvh7JvrS1_c0WaNdTEGYMN0WuIhGVgJxF1W9aXabRLNy7B1a0ShzPtBhqQyVdyn7fep1XUiOGz-IVGw2wbYLz1s8dZd8msk8wYB6X1ezf-7vRbGRx_jsKMXFMR--i7Y3Gw80B1gP4-iP9zqV-zk9m76T5LBI2ALjPA3RTZ8HWW7nYFTdea9cwWFKFRtc3BX-pOHDqGwzUZQ1RCBsSaMC7_fFXJyJK35X7XTdpV6pArL_7xyOAMcIcgi8PTvtX3lCwCOO2rHz4ehcNQ6CbNL4IjT0X9eZBmPRVhyDoRLmkJuyQ
                </poland.inpost.token>
                <poland.inpost.organizationId>3308</poland.inpost.organizationId>
                <!--poland.inpost-->
                <!-- 千帆集运-->
                <qianfan.appKey>34614806</qianfan.appKey>
                <qianfan.secret>b267a7c4e31ae554be90c7923410f0ee</qianfan.secret>
                <qianfan.url>http://gw.api.taobao.com/router/rest</qianfan.url>
                <qianfan.url.https>https://eco.taobao.com/router/rest</qianfan.url.https>
                <!-- 千帆集运-->
            </properties>
        </profile>
        <profile>
            <id>product</id>
            <properties>
                <finalName>CEOP</finalName>
                <environment.name>product</environment.name>
                <app_version>2.19.26</app_version>
                <profileId>product</profileId>
                <environment.desc>product environment on linux server</environment.desc>
                <!--是否加载字典-->
                <dictionary.init_config>true</dictionary.init_config>
                <jdbc.url>*******************************************</jdbc.url>
                <jdbc.username>ceos</jdbc.username>
                <jdbc.password>Ceos_210</jdbc.password>
                <dataSource>DruidDataSource</dataSource>
                <db.redis.ip>*************</db.redis.ip>
                <db.redis.port>36379</db.redis.port>
                <db.redis.password>kj_WY_fzplm@2021</db.redis.password>
                <db.redis.database>3</db.redis.database>
                <db.redisson.address>redis://**************:36379</db.redisson.address>
                <db.redisson.password>kj_WY_fzplm@2021</db.redisson.password>
                <db.redisson.database>3</db.redisson.database>
                <historyRootPath>/bea/ceosHistoryLog</historyRootPath>
                <appFilePath>/bea/appConfig</appFilePath>
                <dispatchNumberURL>http://mla.mailamericas.com/api/v1/reserve?access_token=SIN2887DF89SA0Z0299
                </dispatchNumberURL>
                <orderPlatformURL>http://**************/order/FastRpt/PDF_NEW.aspx
                </orderPlatformURL>
                <!--阿姆斯特丹FBA-->
                <ams.tempUrl>http://************:8888/via/net</ams.tempUrl>
                <ams.viatoken.fba>3e39f5929b7831b6e14375d9e7f39cbf</ams.viatoken.fba>
                <ams.viaurl>https://app.viaeurope.com</ams.viaurl>
                <requestUrlCainiao>http://link.cainiao.com/gateway/link.do
                </requestUrlCainiao>
                <requestUrlUseast>https://useast-link.cainiao.com/gateway/link.do</requestUrlUseast>
                <signKey>v6lfQ5XH677s5I4835tgecOOBmZ9u7T9</signKey>
                <signKey4TW>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67</signKey4TW>
                <!--阿姆斯特丹FBA END-->
                <inter.littlepackage>http://************:8888/littlePackageQuery</inter.littlepackage>
                <interchange.url>http://*************:81</interchange.url>
                <WISH_COLOMBIA_SHIPPING_PUSH_URL>
                    http://svc1.sipost.co/WcfServiceSPOKE/ServiceSPOKE.svc/PostShippingWithoutFile
                </WISH_COLOMBIA_SHIPPING_PUSH_URL>
                <!-- weblogic-->
                <!--  <weblogic.adminurl>t3://************:7001/</weblogic.adminurl>
                  <weblogic.user>weblogic</weblogic.user>
                  <weblogic.password>sinoairbea!%@</weblogic.password>-->
                <!-- log4j-->
                <log4j.rootLogger>info</log4j.rootLogger>
                <print.url>http://ceos.sinoair.com</print.url>
                <!--美线操作手册在文件服务器的地址-->
                <US.NoteBook>http://ecdowntest.sinoair.com:8081/download</US.NoteBook>
                <!--文件服务器下载地址-->
                <sinoair_file_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_ecdown_url_key>

                <!--文件服务器下载地址-->
                <sinoair_file_wb_ec_download_url_kdy>http://ecdown.sinoair.com/download?uuid=
                </sinoair_file_wb_ec_download_url_kdy>
                <!--文件服务器上传地址-->
                <sinoair_file_wb_ecdown_url_key>http://ecdown.sinoair.com/upload</sinoair_file_wb_ecdown_url_key>

                <sinoair.sinoairProxyHost>************</sinoair.sinoairProxyHost>
                <sinoair.sinoairProxyPort>8080</sinoair.sinoairProxyPort>
                <sinoair.openroxy>OFF</sinoair.openroxy>

                <!-- data export job 配置 -->
                <data.export.url>http://*************:9000</data.export.url>
                <data.export.ceos.code>CEOS_210</data.export.ceos.code>

                <!--对接ACT Express-->
                <act.express.url>http://dataif.ufms.co.kr/if/order</act.express.url>

                <!--美国专线对接-->
                <sureline2_url>http://************</sureline2_url>
                <sureline2_AppKey>5d21f7b7-c5c4-4e1c-ad0f-635eead09469</sureline2_AppKey>
                <sureline2_ClientSecret>24383ddf-9ac8-4917-804c-2db452cadab1</sureline2_ClientSecret>

                <!-- 万色对接信息 -->
                <wise_api_host_url>http://openapi.superbpost.com:38888</wise_api_host_url>
                <wise_api_key>qMuhp6Ktj</wise_api_key>

                <!-- colisprive -->
                <colisprive.wsdl.url>https://www.colisprive.com/Externe/WSCP.asmx</colisprive.wsdl.url>
                <colisprive.wsdl.username>SINOTRANSAGWS</colisprive.wsdl.username>
                <colisprive.wsdl.password>hS*dP3Jq5@pV!7gX</colisprive.wsdl.password>
                <colisprive.wsdl.CPCustoID>AG</colisprive.wsdl.CPCustoID>
                <colisprive.wsdl.AccountID>EM110419</colisprive.wsdl.AccountID>

                <!--香港中通对接-->
                <hk_zto_getSelfExtractingPoint_url>http://shipbao.net:8888/index.php/I/Auth/website
                </hk_zto_getSelfExtractingPoint_url>
                <hk_zto_addOrder_url>http://shipbao.net:8888/index.php/I/Thirdparty/batchSave</hk_zto_addOrder_url>
                <hk_zto_api_id>c29acd40a71578fb5b754570a0117742</hk_zto_api_id>
                <hk_zto_api_key>9cbd371a3ef44ae8c8170399412b3600</hk_zto_api_key>
                <hk_zto_referer>http://ztoztozto.com</hk_zto_referer>

                <!--ups对接-->
                <ups_accessLicenseNumber>****************</ups_accessLicenseNumber>
                <ups_userId>Sinotrans123</ups_userId>
                <ups_password>Ups12345</ups_password>
                <ups_account>6545E2</ups_account>
                <ups_shipConfirm_url>https://onlinetools.ups.com/ups.app/xml/ShipConfirm</ups_shipConfirm_url>
                <ups_shipAccept_url>https://onlinetools.ups.com/ups.app/xml/ShipAccept</ups_shipAccept_url>
                <ups_void_url>https://onlinetools.ups.com/ups.app/xml/Void</ups_void_url>

                <ups_emi_validation_url>
                    https://oc-gspk-og.orangeconnex.com/api/shipment/si/package/v1/validation
                </ups_emi_validation_url>
                <ups_emi_label_url>https://oc-gspk-og.orangeconnex.com/api/shipment/si/package/v1/label
                </ups_emi_label_url>
                <ups_emi_apiKey>c3aa211f-514e-4986-8c17-5c6bf9aa8c80</ups_emi_apiKey>
                <ups_emi_securityKey>********-883a-4d85-b665-90534ef3c94c</ups_emi_securityKey>

                <!--wishB2B-->
                <wishB2B_Base_url>https://www.wishpost.cn</wishB2B_Base_url>
                <wishB2B_api_key>JHBia2RmMiQxMDAkUE1mNEg2TTBacXkxVnNxNWR3NWh6QSRFMGIyZnFBbFhMdTJORElhZFpjVFNKSmdpOGc=
                </wishB2B_api_key>
                <!--wish店铺模式 production api key-->
                <wishB2B_api_key_FUSION>
                    JHBia2RmMiQxMDAkelhsUEtRVmdqTkg2SDJOTVNla2RJdyRoZnRQdHMvUGUxdWxidjlWSzhwZ2xWWnBuTkU=
                </wishB2B_api_key_FUSION>


                <!-- ESNAD 配置开始 -->
                <ESNAD_UPLOAD_ORDER_URL>https://dms.esnadexpress.com/api/v1/process</ESNAD_UPLOAD_ORDER_URL>
                <ESNAD_UPLOAD_ORDER_API_KEY>otYYNweFcmDSCmIeEqhAtMw5KKgaFzeX</ESNAD_UPLOAD_ORDER_API_KEY>
                <ESNAD_PDF_INVOICE_URL>https://dms.esnadexpress.com/api/v1/process/get_process_pdf
                </ESNAD_PDF_INVOICE_URL>
                <ESNAD_MER_CHART_CODE>HKCHSIN01</ESNAD_MER_CHART_CODE>
                <!-- ESNAD 配置结束 -->
                <!--艾莉薇对接 start-->
                <ALW_REQUEST_URL>http://api.ollyway.com</ALW_REQUEST_URL>
                <ALW_REQUEST_USER>A720</ALW_REQUEST_USER>
                <ALW_REQUEST_TOKEN>0b3f75c1901eada9b50f5121a6585f46</ALW_REQUEST_TOKEN>
                <ALW_REQUEST_USER_SNR>A743</ALW_REQUEST_USER_SNR>
                <ALW_REQUEST_TOKEN_SNR>5ddde2cd7a67e7195d39790dcd4e729f</ALW_REQUEST_TOKEN_SNR>
                <!--艾莉薇对接 end-->

                <!--华得士作为末端 德国DHL 派送 start -->
                <REQUEST_HDS_API_URL>http://oms.gfsbiz.com.cn/default/svc/web-service</REQUEST_HDS_API_URL>
                <REQUEST_HDS_APPTOKEN>41ac86b27fe34caa8f9aa56560fafe03</REQUEST_HDS_APPTOKEN>
                <REQUEST_HDS_APPKEY>41ac86b27fe34caa8f9aa56560fafe03f358f2d3b00214a7a73cf7e578198929
                </REQUEST_HDS_APPKEY>
                <!--华得士作为末端 德国DHL 派送 start -->

                <!--GLS对接-->
                <GLS_IP>**************</GLS_IP>
                <GLS_PORT>3030</GLS_PORT>
                <GLS_T805>*********</GLS_T805>
                <GLS_T8700>BE 610</GLS_T8700>
                <GLS_T8914>056aaacL2g</GLS_T8914>
                <GLS_T8915>**********</GLS_T8915>
                <GLS_T805_CAINIAO>*********</GLS_T805_CAINIAO>
                <GLS_T8914_CAINIAO>056aaacZ8i</GLS_T8914_CAINIAO>

                <!--Wise末端派送 start -->
                <WISE_API_KEY>319a3f1f21982e9539c1ea5fdd49e7ce11766</WISE_API_KEY>
                <!--Wise末端派送 END -->
                <GETTOKEN>fbef3ffe-6f2d-4ddb-a247-5bce019961aa</GETTOKEN>
                <SENDMANIFESTURL>https://pos-kp.kerry-ecommerce.com.cn/pos-web/shipment/create</SENDMANIFESTURL>
                <!--FBA 互联通 start -->
                <HLT_APPKEY>1a2c057e9abc48638eab84f69a72b173</HLT_APPKEY>
                <HLT_URL>http://**************:8086/xms/</HLT_URL>
                <!--FBA 互联通 END -->

                <!--footlocker_singapore-->
                <client_id>9cbb95c36cff471d8737b8847611e223</client_id>
                <client_secret>c2c5f717a8634bac8d27c73ded620e9c</client_secret>
                <pickup_address_id>98989012</pickup_address_id>
                <fl_singapore_token_url>https://api.ninjavan.co/SG/2.0/oauth/access_token</fl_singapore_token_url>
                <fl_singapore_create_oreder_url>https://api.ninjavan.co/SG/4.1/orders</fl_singapore_create_oreder_url>

                <!--小米的结算账号 -->
                <xiaomi.socode>HKG167952</xiaomi.socode>

                <!--B2C europe 的url -->
                <b2c.url>https://finalmilelabel-live.b2ceurope.eu</b2c.url>
                <!--B2C europe 的password -->
                <b2c.pass>75A724D1002152C04AB3003BAF1051EDBE947050</b2c.pass>
                <!--ITOM华德仕 -->
                <ITOMHDTOKEN>Basic QzEwMDA4Jkc0bTZKUXRMREE4PQ==</ITOMHDTOKEN>
                <ITOMHDCreateOrderURL>https://sengiexpress.com/api/WayBill/CreateOrder</ITOMHDCreateOrderURL>
                <ITOMHDGetOrderURL>https://sengiexpress.com/api/Label/Print</ITOMHDGetOrderURL>
                <!--对接EMS-新一代-->
                <EMS_WH_CODE>31120030</EMS_WH_CODE>
                <EMS_KEY>y8ptdndcq9c1n9l1</EMS_KEY>
                <EMS_EC_COMPANY_ID>1100066675327</EMS_EC_COMPANY_ID>
                <EMS_GET_ORDER_NO_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/barCodesAssgine/barCodeService
                </EMS_GET_ORDER_NO_URL>
                <EMS_SEND_ORDER_BACK_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/orderService/OrderReceiveBack
                </EMS_SEND_ORDER_BACK_URL>
                <EMS_SEND_ORDER_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/orderService/orderReceive
                </EMS_SEND_ORDER_URL>
                <EMS_GET_LABEL_URL>https://my.ems.com.cn/pcpErp-web/a/pcp/surface/download</EMS_GET_LABEL_URL>
                <!--对接IQS-->
                <IQS_CREATE_URL>https://www.iqsgsc.com/v2/shipment/create</IQS_CREATE_URL>
                <IQS_TOKEN>access_token_cysACCrnJUGqeuNB</IQS_TOKEN>
                <IQS_ACCOUNT>11261</IQS_ACCOUNT>
                <IQS_UID><EMAIL></IQS_UID>

                <!--新美线 PB对接参数-->
                <PB_GET_TOKEN_AUTHORIZATION>Basic bXBtcUhXdDN4ekRDUGhmMXVOcmZyYk5HT1hTZFRocnk6cWxZWm1NZWZHTFcxOHZvOQ==
                </PB_GET_TOKEN_AUTHORIZATION>

                <QT_SOCODE>ZSU168192,********,HGH177412</QT_SOCODE>

                <!-- MBB -->
                <MBB_ADD_EXPRESS_ORDER_URL>
                    http://oms.mbb-logistics.com/service/Storage_addExpressOrder.action?userId=<EMAIL>&amp;token=AFYIzHVSwEnvCaP2aXAbBlSe2wq12w
                </MBB_ADD_EXPRESS_ORDER_URL>
                <AEPOST_URL>https://osb.epg.gov.ae/ebs/genericapi/booking/rest/CreateBooking</AEPOST_URL>
                <AEPOST_HSCODE_URL>
                    https://osb.epg.gov.ae/ebs/epg.pos.CustomerService.lookups.rest.noauth/GetCustomDescriptionsV1
                </AEPOST_HSCODE_URL>
                <AEPOST_TOKEN>C637127</AEPOST_TOKEN>

                <!--捷网英邮的配置 start-->
                <JWYY_USERNAME>18397</JWYY_USERNAME>
                <JWYY_PASSWORD>Op9c0QQguq8cnUaT</JWYY_PASSWORD>
                <!--捷网英邮的配置 end-->

                <!--以色列邮政对接 生产环境 -->
                <ISRAELPOST_URL>https://apimftprd.israelpost.co.il</ISRAELPOST_URL>

                <SinoairExp_Username><EMAIL></SinoairExp_Username>
                <SinoairExp_Password>vF4CFYw~GPaD</SinoairExp_Password>
                <SinoairExp_key>ea9fda3b42c24ba4a487ab306091d695</SinoairExp_key>

                <SinoairTrk_Username><EMAIL></SinoairTrk_Username>
                <SinoairTrk_Password>~aFM71Urr2kr</SinoairTrk_Password>
                <SinoairTrk_key>211d883f85294222a189f35b8aada7ee</SinoairTrk_key>

                <SinoairEco_Username><EMAIL></SinoairEco_Username>
                <SinoairEco_Password>yzHqMR(K8VDV</SinoairEco_Password>
                <SinoairEco_key>fc750c1949da420cae1be089accf572d</SinoairEco_key>

                <SinoairDdp_Username><EMAIL></SinoairDdp_Username>
                <SinoairDdp_Password>9bBB6uvhwnRo</SinoairDdp_Password>
                <SinoairDdp_key>9e9788afb92b4361aefbf21079c9a189</SinoairDdp_key>
                <!--以色列邮政对接 生产环境 end-->
                <!--                天猫直发app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>0743R02E262U38UYADNNr653M7lO3dn2
                </SinoEx_Cainiao_CONSOWAREHOUSE_APPSECRET>
                <!--                天猫集运台湾app_secret-->
                <SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>YKrVt30J7bxDSP15Ds7Ms0H1429W2A67
                </SinoEx_Cainiao_CONSOWAREHOUSE_TW_APPSECRET>
                <!-- shopee 配置开始 start................ -->
                <shopee.url.3.1>https://external2.shopee.sg/lcs/live/api/first_leg/repack_carton/lh_sinotrans_eu
                </shopee.url.3.1>
                <shopee.url.3.2>https://external2.shopee.sg/lcs/live/api/first_leg/loading_carton/lh_sinotrans_eu
                </shopee.url.3.2>
                <shopee.url.3.3>https://external2.shopee.sg/lcs/live/api/first_leg/tracking_carton/lh_sinotrans_eu
                </shopee.url.3.3>
                <shopee.usename>lh_sinotrans_eu</shopee.usename>
                <shopee.password>zWoJ2TOkjvVQuNiSxcaAZsFupIf817gR</shopee.password>
                <!-- shopee 配置开始 End................ -->

                <!-- UBI 配置 start............ -->
                <UBI_URL_IP>http://cn.etowertech.com</UBI_URL_IP>
                <UBI_URL_TOKEN>pcloBCvuQOgiIwVT1ALvGz</UBI_URL_TOKEN>
                <UBI_URL_SECURET>fRTlwEg1Yoy6S5eLssTe0w</UBI_URL_SECURET>

                <UBI_URL_TOKEN_HD>pcloBCvuQOgiIwVT1ALvGz</UBI_URL_TOKEN_HD>
                <UBI_URL_SECURET_HD>fRTlwEg1Yoy6S5eLssTe0w</UBI_URL_SECURET_HD>

                <UBI_URL_TOKEN_HN>pclZP4TsihBI5eLTtlbRCh</UBI_URL_TOKEN_HN>
                <UBI_URL_SECURET_HN>P8MXZhg77ab_T7NbcRpjVA</UBI_URL_SECURET_HN>
                <!-- UBI 配置 end -->

                <!-- AMS配置 -->
                <ams.password>a96dbb5cb2bb43036a79d3e1b39680ed</ams.password>
                <ams.url>https://app-sandbox.viaeurope.com</ams.url>
                <!-- AMS配置 -->

                <!-- southAfrica配置 -->
                <south_africa_uri_gen>https://apiprod.postoffice.co.za:8080/api/trn-manager/gen</south_africa_uri_gen>
                <south_africa_token>e78678b1-55f9-40dc-bbf5-b615533a94ef</south_africa_token>
                <south_africa_Authorization>6eaf13a0-d12e-4c8b-a073-b3c74308367d</south_africa_Authorization>
                <south_africa_url_order>https://apiprod.postoffice.co.za/IPSAPIService/ImportService.svc/rest/Mailitem
                </south_africa_url_order>
                <!-- southAfrica配置 -->

                <!-- SPX配置开始 -->
                <SPX_URL>https://rps.miqexpress.com/rps-api</SPX_URL>
                <SPX_TOKEN>45ea18-ff8005-15e962-05165d-3348da</SPX_TOKEN>
                <!-- SPX配置结束 -->

                <!-- 易云通的配置开始 -->
                <yyt_url>https://api.y2t.com/cb-order/webapi/getBillUrl</yyt_url>
                <yyt_apiAppKey>APIDeC1hCqe709sQHjXlIto2xekVcS6ghjXxENAZ</yyt_apiAppKey>
                <yyt_apiAppSecret>k5eM7qnpyw5r6pm0vp87zphnh7probmag9oLwj5</yyt_apiAppSecret>
                <yyt_boxListUrl>https://api.y2t.com/cb-order/webapi/importBoxNo</yyt_boxListUrl>
                <!-- 易云通的配置结束 -->

                <!--众瑞UPPS-->
                <zhongRui_url>http://*************:82/</zhongRui_url>
                <zhongRui_account>ZR2023041901</zhongRui_account>
                <zhongRui_password>zUjaEefM^g3X</zhongRui_password>


                <!--华磊客户账户推送url-->
                <hl_push_erp_url>http://www.bjzwy.hailei2018.com:8082/logistics/SyncZwyCustomer.html</hl_push_erp_url>
                <!--华磊客户账户推送url-->
                <hl_push_soline_url>http://www.bjzwy.hailei2018.com:8082/logistics/SyncZwyCustomerProduct.html
                </hl_push_soline_url>

                <!-- shopline start -->
                <shopline.url>https://openapi.oneship.io</shopline.url>
                <shopline.appKey>51da2e2332af2c369a02013550aa1fda34034e81</shopline.appKey>
                <shopline.appSecret>65664a4d23b2625edffb70e377b1dc5c8119d349</shopline.appSecret>
                <!-- shopline end -->

                <!--poland.inpost-->
                <poland.inpost.url>https://api-shipx-pl.easypack24.net/v1/</poland.inpost.url>
                <poland.inpost.token>
                    eyJhbGciOiJSUzI1NiIsInR5cCIgOiAiSldUIiwia2lkIiA6ICJzQlpXVzFNZzVlQnpDYU1XU3JvTlBjRWFveFpXcW9Ua2FuZVB3X291LWxvIn0.**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************.pXCFVeA7CGWQkvn1x3sxkHmWO1trwqTM8kZwnrn9FqLwm5VxUU55WI0yZxlfCv74gx6HWgve-EB4msaph7RljS0oNzXP5glIuZt_4oUhykXa6r1-hqbGRnZGaXbXIcN65V4nbH2sF-8OJloJ5MquuX4zI_kBxnNOiNCQLZGw2i_se7wN25BZzOj-aNDuQr4dHpmvKMvGSQkSEj6FcTZtBydFYBxWlVtt9LyaqNxR_t4BGWXEhXVtRapo7t5HtqOMKC3hYEPIZpxWFEtK5uyk5qsx3VxR2EGTEAlWeNeqYdnKLOsJmngjFV4q6JjcQeJAIfcRgkiHBTJg9oTsPw3-Yw
                </poland.inpost.token>
                <poland.inpost.organizationId>75329</poland.inpost.organizationId>
                <!--poland.inpost-->
                <!-- 千帆集运-->
                <qianfan.appKey>34614806</qianfan.appKey>
                <qianfan.secret>b267a7c4e31ae554be90c7923410f0ee</qianfan.secret>
                <qianfan.url>http://gw.api.taobao.com/router/rest</qianfan.url>
                <qianfan.url.https>https://eco.taobao.com/router/rest</qianfan.url.https>
                <!-- 千帆集运-->
            </properties>
        </profile>
    </profiles>
    <!--资源过滤  end-->

    <dependencies>
        <!--quartz-->
        <dependency>
            <groupId>org.quartz-scheduler</groupId>
            <artifactId>quartz</artifactId>
            <version>${org.quartz.version}</version>
        </dependency>

        <dependency>
            <groupId>com.zto.intl</groupId>
            <artifactId>common</artifactId>
            <version>0.0.1</version>
        </dependency>

        <!-- junit -->
        <dependency>
            <groupId>junit</groupId>
            <artifactId>junit</artifactId>
            <version>${junit.version}</version>
            <scope>test</scope>
        </dependency>

        <!-- spring start -->
        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-core</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-web</artifactId>
            <version>${spring.version}</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/commons-beanutils/commons-beanutils -->
        <dependency>
            <groupId>commons-beanutils</groupId>
            <artifactId>commons-beanutils</artifactId>
            <version>1.9.2</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-oxm</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-tx</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-jdbc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-webmvc</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-aop</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-context-support</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <dependency>
            <groupId>org.springframework</groupId>
            <artifactId>spring-test</artifactId>
            <version>${spring.version}</version>
        </dependency>

        <!-- mybatis/spring包 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis-spring</artifactId>
            <version>${mybatis.spring.version}</version>
        </dependency>
        <!-- spring end -->

        <!-- mybatis核心包 -->
        <dependency>
            <groupId>org.mybatis</groupId>
            <artifactId>mybatis</artifactId>
            <version>${mybatis.version}</version>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
            <version>5.2.0</version>
            <exclusions>
                <exclusion>
                    <groupId>junit</groupId>
                    <artifactId>junit</artifactId>
                </exclusion>
            </exclusions>

        </dependency>

        <!--qrcode-->
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>core</artifactId>
            <version>3.4.1</version>
        </dependency>
        <dependency>
            <groupId>com.google.zxing</groupId>
            <artifactId>javase</artifactId>
            <version>3.4.1</version>
        </dependency>

        <!-- oracle驱动包 -->
        <dependency>
            <groupId>oracle</groupId>
            <artifactId>ojdbc</artifactId>
            <version>${ojdbc.version}</version>
        </dependency>


        <!-- 阿里巴巴数据源包 -->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>druid</artifactId>
            <version>${alibaba.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-httpclient</groupId>
            <artifactId>commons-httpclient</artifactId>
            <version>3.1</version>
        </dependency>

        <!-- json数据 -->
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-core</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-databind</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <dependency>
            <groupId>com.fasterxml.jackson.core</groupId>
            <artifactId>jackson-annotations</artifactId>
            <version>${jackson.version}</version>
        </dependency>
        <!--<dependency>
            <groupId>net.sf.json-lib</groupId>
            <artifactId>json-lib</artifactId>
            <version>2.4</version>
        </dependency>-->
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
            <version>${fastjson.version}</version>
        </dependency>

        <!--日志系统依赖start-->

        <!-- slf4j核心包-->
        <dependency>
            <groupId>org.slf4j</groupId>
            <artifactId>slf4j-api</artifactId>
            <version>1.7.25</version>
        </dependency>

        <!--slf4j对应log4j2的中间件,即桥接，告诉slf4j使用log4j2-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-slf4j-impl</artifactId>
            <version>2.20.0</version>
        </dependency>

        <!--log4j2核心包-->
        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-core</artifactId>
            <version>2.20.0</version>
        </dependency>

        <dependency>
            <groupId>org.apache.logging.log4j</groupId>
            <artifactId>log4j-api</artifactId>
            <version>2.20.0</version>
        </dependency>
        <!--日志系统依赖end-->

        <!-- apache.commons -->
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-lang3</artifactId>
            <version>${commons-lang3.version}</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-collections4</artifactId>
            <version>4.4</version>
        </dependency>

        <!-- commons-collections -->
        <dependency>
            <groupId>commons-collections</groupId>
            <artifactId>commons-collections</artifactId>
            <version>${commons-collections.version}</version>
        </dependency>


        <!--commons-fileupload -->
        <dependency>
            <groupId>commons-fileupload</groupId>
            <artifactId>commons-fileupload</artifactId>
            <version>${commons-fileupload.version}</version>
        </dependency>

        <!-- jsp-api -->
        <dependency>
            <groupId>javax.servlet.jsp</groupId>
            <artifactId>jsp-api</artifactId>
            <version>${jsp-api.version}</version>
        </dependency>

        <dependency>
            <groupId>javax.servlet</groupId>
            <artifactId>javax.servlet-api</artifactId>
            <version>${javax.servlet-api.version}</version>
        </dependency>

        <!-- jstl -->
        <dependency>
            <groupId>jstl</groupId>
            <artifactId>jstl</artifactId>
            <version>${jstl.version}</version>
        </dependency>


        <!-- 工具网站 -->
        <dependency>
            <groupId>com.github.pagehelper</groupId>
            <artifactId>pagehelper</artifactId>
            <version>${pagehelper.version}</version>
        </dependency>

        <!-- hibernate-validator -->
        <dependency>
            <groupId>org.hibernate</groupId>
            <artifactId>hibernate-validator</artifactId>
            <version>${hibernate.validator.version}</version>
        </dependency>

        <dependency>
            <groupId>org.aspectj</groupId>
            <artifactId>aspectjweaver</artifactId>
            <version>${aspectjweaver.version}</version>
        </dependency>
        <dependency>
            <groupId>cglib</groupId>
            <artifactId>cglib</artifactId>
            <version>${cglib.version}</version>
        </dependency>
        <dependency>
            <groupId>net.sf.ehcache</groupId>
            <artifactId>ehcache-core</artifactId>
            <version>${ehcache-core.version}</version>
        </dependency>

        <dependency>
            <groupId>commons-codec</groupId>
            <artifactId>commons-codec</artifactId>
            <version>${commons-codec.version}</version>
        </dependency>

        <!-- poi -->
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml-schemas</artifactId>
            <version>${apache.poi.version}</version>
        </dependency>

        <dependency>
            <groupId>com.sun.mail</groupId>
            <artifactId>jakarta.mail</artifactId>
            <version>1.6.7</version>
        </dependency>

        <!--jxl-->
        <!-- https://mvnrepository.com/artifact/net.sourceforge.jexcelapi/jxl -->
        <dependency>
            <groupId>net.sourceforge.jexcelapi</groupId>
            <artifactId>jxl</artifactId>
            <version>2.6.12</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>


        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
            <version>4.5.14</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/org.apache.httpcomponents/httpclient -->
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpclient</artifactId>
            <version>4.5.14</version>
        </dependency>
        <!--菜鸟文件上传-->
        <dependency>
            <groupId>com.cainiao.logisticscloud.dss</groupId>
            <artifactId>dss-sdk</artifactId>
            <version>1.1.2</version>
        </dependency>
        <dependency>
            <groupId>com.cainiao.logisticscloud.dss</groupId>
            <artifactId>dss-sdk-core</artifactId>
            <version>1.1.2</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.aliyun.oss/aliyun-sdk-oss -->
        <dependency>
            <groupId>com.aliyun.oss</groupId>
            <artifactId>aliyun-sdk-oss</artifactId>
            <version>2.5.0</version>
        </dependency>
        <dependency>
            <groupId>jdom</groupId>
            <artifactId>jdom</artifactId>
            <version>1.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.thoughtworks.xstream/xstream -->
        <dependency>
            <groupId>com.thoughtworks.xstream</groupId>
            <artifactId>xstream</artifactId>
            <version>1.4.20</version>
        </dependency>

        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
            <version>3.10</version>
        </dependency>

        <!--引入百度语音合成技术 播放语音提示-->
        <dependency>
            <groupId>com.baidu.aip</groupId>
            <artifactId>java-sdk</artifactId>
            <version>4.6.1</version>
            <exclusions>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
            </exclusions>
        </dependency>

        <!-- https://mvnrepository.com/artifact/net.sf.barcode4j/barcode4j -->
        <dependency>
            <groupId>net.sf.barcode4j</groupId>
            <artifactId>barcode4j</artifactId>
            <version>2.1</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.github.stuxuhai/jpinyin -->
        <dependency>
            <groupId>com.github.stuxuhai</groupId>
            <artifactId>jpinyin</artifactId>
            <version>1.1.8</version>
        </dependency>
        <dependency>
            <groupId>commons-net</groupId>
            <artifactId>commons-net</artifactId>
            <version>${commons-net.version}</version>
        </dependency>

        <dependency>
            <groupId>com.jcraft</groupId>
            <artifactId>jsch</artifactId>
            <version>0.1.53</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/com.itextpdf/itextpdf -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itextpdf</artifactId>
            <version>5.5.13</version>
        </dependency>
        <!-- https://mvnrepository.com/artifact/com.google.zxing/core -->
        <dependency>
            <groupId>com.itextpdf</groupId>
            <artifactId>itext-asian</artifactId>
            <version>5.2.0</version>
        </dependency>

        <dependency>
            <groupId>com.lowagie</groupId>
            <artifactId>itext</artifactId>
            <version>2.1.7</version>
        </dependency>

        <!-- https://mvnrepository.com/artifact/org.redisson/redisson -->
        <dependency>
            <groupId>org.redisson</groupId>
            <artifactId>redisson</artifactId>
            <version>2.15.2</version>
        </dependency>
        <!--        千帆依赖的okhttp-->
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
            <version>4.9.0</version>
        </dependency>
        <!--pdfbox的依赖库-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>fontbox</artifactId>
            <version>2.0.12</version>
        </dependency>
        <!--提供pdf转图片的相关类库-->
        <dependency>
            <groupId>org.apache.pdfbox</groupId>
            <artifactId>pdfbox</artifactId>
            <version>2.0.12</version>
        </dependency>
        <!--解决pdf转图片时图章消失的问题-->
        <dependency>
            <groupId>com.github.jai-imageio</groupId>
            <artifactId>jai-imageio-jpeg2000</artifactId>
            <version>1.3.0</version>
        </dependency>
        <dependency>
            <groupId>net.coobird</groupId>
            <artifactId>thumbnailator</artifactId>
            <version>0.4.8</version>
        </dependency>

        <!--csv文件解析-->
        <dependency>
            <groupId>net.sourceforge.javacsv</groupId>
            <artifactId>javacsv</artifactId>
            <version>2.0</version>
        </dependency>
        <dependency>
            <groupId>net.sf.barcode4j</groupId>
            <artifactId>barcode4j</artifactId>
            <version>2.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.rocketmq</groupId>
            <artifactId>rocketmq-client</artifactId>
            <version>4.4.0</version>
        </dependency>

        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
            <version>16.0.1</version>
        </dependency>


        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-api</artifactId>
            <version>0.10.5</version>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-impl</artifactId>
            <version>0.10.5</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>io.jsonwebtoken</groupId>
            <artifactId>jjwt-jackson</artifactId>
            <version>0.10.5</version>
            <scope>runtime</scope>
        </dependency>
        <dependency>
            <groupId>com.auth0</groupId>
            <artifactId>java-jwt</artifactId>
            <version>3.2.0</version>
        </dependency>

        <dependency>
            <groupId>io.netty</groupId>
            <artifactId>netty-all</artifactId>
            <version>4.1.13.Final</version>
        </dependency>
        <!--流式读取Excel数据-->
        <!--<dependency>-->
        <!--<groupId>com.monitorjbl</groupId>-->
        <!--<artifactId>xlsx-streamer</artifactId>-->
        <!--<version>0.2.3</version>-->
        <!--</dependency>-->


        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>1.1.2-beta5</version>
        </dependency>

        <dependency>
            <groupId>com.qcloud</groupId>
            <artifactId>cos_api</artifactId>
            <version>5.2.4</version>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-log4j12</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
            </exclusions>
        </dependency>

        <dependency>
            <groupId>com.google.code.gson</groupId>
            <artifactId>gson</artifactId>
            <version>2.8.5</version>
        </dependency>

        <dependency>
            <groupId>com.sinoair.plugin</groupId>
            <artifactId>sinoair-api-post</artifactId>
            <version>1.1.5.5</version>
        </dependency>

        <!--        <dependency>
                    <groupId>org.springframework.data</groupId>
                    <artifactId>spring-data-redis</artifactId>
                    <version>2.1.3.RELEASE</version>
                </dependency>-->
        <dependency>
            <groupId>org.springframework.data</groupId>
            <artifactId>spring-data-redis</artifactId>
            <version>1.6.6.RELEASE</version>
        </dependency>

        <dependency>
            <groupId>redis.clients</groupId>
            <artifactId>jedis</artifactId>
            <version>2.9.1</version>
        </dependency>

        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-pool2</artifactId>
            <version>2.11.1</version>
        </dependency>

        <dependency>
            <groupId>org.mountcloud</groupId>
            <artifactId>graphql-client</artifactId>
            <version>1.2</version>
        </dependency>
        <dependency>
            <groupId>com.sinoair.plugin</groupId>
            <artifactId>taobao-sdk-NEW</artifactId>
            <version>20240117</version>
        </dependency>
    </dependencies>

    <build>
        <resources>
            <resource>
                <directory>src/main/resources</directory>
                <filtering>true</filtering>
            </resource>
        </resources>
        <finalName>${finalName}</finalName>
        <plugins>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-surefire-plugin</artifactId>
                <version>2.5</version>
                <configuration>
                    <forkMode>once</forkMode>
                    <argLine>-Dfile.encoding=UTF-8</argLine>

                    <!--todo -->
                    <skipTests>true</skipTests>
                    <!--自己指定测试报告目录-->
                    <reportsDirectory>${project.build.directory}/test-reports</reportsDirectory>
                </configuration>
            </plugin>
            <!-- Mybatis generator代码生成插件 配置 -->
            <plugin>
                <groupId>org.mybatis.generator</groupId>
                <artifactId>mybatis-generator-maven-plugin</artifactId>
                <version>${plugin.mybatis.generator}</version>
                <configuration>
                    <configurationFile>${mybatis.generator.generatorConfig.xml}</configurationFile>
                    <overwrite>true</overwrite>
                    <verbose>true</verbose>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-compiler-plugin</artifactId>
                <configuration>
                    <source>${jdk.version}</source>
                    <target>${jdk.version}</target>
                    <encoding>${encoding.val}</encoding>
                </configuration>
            </plugin>
            <plugin>
                <groupId>org.apache.maven.plugins</groupId>
                <artifactId>maven-resources-plugin</artifactId>
                <configuration>
                    <encoding>UTF-8</encoding>
                    <nonFilteredFileExtensions>
                        <nonFilteredFileExtension>pdf</nonFilteredFileExtension>
                        <nonFilteredFileExtension>p12</nonFilteredFileExtension>
                    </nonFilteredFileExtensions>
                </configuration>
            </plugin>

            <plugin>
                <groupId>org.eclipse.jetty</groupId>
                <artifactId>jetty-maven-plugin</artifactId>
                <version>9.4.51.v20230217</version>
                <configuration>
                    <systemProperties>
                        <systemProperty>
                            <name>org.eclipse.jetty.server.Request.maxFormContentSize</name>
                            <value>-1</value>
                        </systemProperty>
                        <systemProperty>
                            <name>org.eclipse.jetty.server.Request.maxFormKeys</name>
                            <value>-1</value>
                        </systemProperty>
                    </systemProperties>
                    <stopKey/>
                    <stopPort/>
                </configuration>
            </plugin>
        </plugins>


    </build>
</project>


