package com.sinoair.core.utils;

import org.junit.Test;

import java.io.File;

import static org.junit.Assert.*;

public class Base64UtilsTest {

    @Test
    public void imgToBase64Str() {
        String imgPath="D:/test/2.jpg";
        System.out.println(Base64Utils.imgToBase64Str(imgPath));
    }

    @Test
    public void base64StrimgToByte() {
//        String imgPath="D:/test/2.jpg";
//        String s=Base64Utils.imgToBase64Str(imgPath);
//        System.out.println(s);
        String ss="";
        File file =  FileUtil.base64GenerateFile(ss);
        System.out.println(file.getAbsolutePath());
    }
}