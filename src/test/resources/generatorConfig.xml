<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE generatorConfiguration
        PUBLIC "-//mybatis.org//DTD MyBatis Generator Configuration 1.0//EN"
        "http://mybatis.org/dtd/mybatis-generator-config_1_0.dtd">

<!-- 配置Run As Maven build : Goals 参数 : mybatis-generator:generate -Dmybatis.generator.overwrite=true -->
<!-- 配置 tableName,使用 Run As Maven build 生成 dao model 层 -->
<generatorConfiguration>
    <!-- 配置文件路径 -->
    <properties url="${mybatis.generator.generatorConfig.properties}"/>

    <!--数据库驱动包路径 -->
    <classPathEntry location="${drive.class.path}"/>

    <context id="DB2Tables" targetRuntime="MyBatis3">
        <!--关闭注释 -->
        <commentGenerator>
            <property name="suppressAllComments" value="true"/>
        </commentGenerator>

        <!--数据库连接信息 -->
        <jdbcConnection driverClass="${jdbc.driver}" connectionURL="${jdbc.url}" userId="${jdbc.username}"
                        password="${jdbc.password}">
        </jdbcConnection>

        <!--生成的model 包路径 -->
        <javaModelGenerator targetPackage="${model.package}" targetProject="${target.project}">
            <property name="enableSubPackages" value="ture"/>
            <property name="trimStrings" value="true"/>
        </javaModelGenerator>

        <!--生成xml mapper文件 路径 -->
        <sqlMapGenerator targetPackage="${xml.mapper.package}" targetProject="${target.project}">
            <property name="enableSubPackages" value="ture"/>
        </sqlMapGenerator>

        <!-- 生成的Dao接口 的包路径 -->
        <javaClientGenerator type="XMLMAPPER" targetPackage="${dao.package}" targetProject="${target.project}">
            <property name="enableSubPackages" value="ture"/>
        </javaClientGenerator>

        <!--对应数据库表名 -->
        <!--<table tableName="SERIALNO" domainObjectName="SerialNO" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="USERS" domainObjectName="User" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="USERS" domainObjectName="User" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="USERs_ROLE" domainObjectName="UserRole" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="ROLE" domainObjectName="Role" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="ROLE_MENU" domainObjectName="RoleMenu" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="MENU" domainObjectName="Menu" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="COMPANY" domainObjectName="Company" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expressairwaybill" domainObjectName="Expressairwaybill" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expressbusinessactivity" domainObjectName="Expressbusinessactivity" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="eawb_track" domainObjectName="EawbtTrack" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="eawb_track_eba_temp" domainObjectName="EawbTrackEbaTemp" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="eawb_track_property" domainObjectName="EawbtTrackProperty" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expressassignment" domainObjectName="Expressassignment" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expressitem" domainObjectName="Expressitem" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="packages" domainObjectName="Packages" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="eawb_packages" domainObjectName="EawbPackages" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="Expresstransmodedefine" domainObjectName="ExpressTransModeDefine" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="express_correos_manifest" domainObjectName="ExpressCorreosManifest" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="EAWB_NO_FOR_EDI" domainObjectName="EawbNoForEdi" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="express_edi_log" domainObjectName="ExpressEdiLog" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="EXPRESS_PROPERTY" domainObjectName="ExpressProperty" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="EXPRESSACTIVITYDEFINE" domainObjectName="ExpressActivityDefine" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="EXPRESSACTIVITYSTATUSTYPE" domainObjectName="ExpressActivityStatusType" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="EXPRESSACTIVITYTRANSLATE" domainObjectName="ExpressActivityTransLate" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="EXPRESSINTERNATIONALTRANS" domainObjectName="ExpressInternationalTrans" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="nationcontinent" domainObjectName="NationContinent" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="settlementobject" domainObjectName="SettlementObject" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expselecterpkgdetail" domainObjectName="ExpSelecterPkgDetail" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="expselecterpkgdetaillog" domainObjectName="ExpSelecterPkgDetailLog" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
        <table tableName="packages" domainObjectName="Packages" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expressassignment" domainObjectName="Expressassignment" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="airport" domainObjectName="AirPort" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="expressitem" domainObjectName="Expressitem" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="eawb_packages" domainObjectName="EawbPackages" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="currencytype" domainObjectName="CurrencyType" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="EXPRESSEXCEPTIONSTATUS" domainObjectName="ExpressExceptionStatus" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="EXPRESS_MANIFEST" domainObjectName="ExpressManifest" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!-- <table tableName="expressairwaybill" domainObjectName="Expressairwaybill" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="EXPRESS_TRUCK_PACKAGE" domainObjectName="ExpressTruckPackage" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->
        <!-- <table tableName="EXPRESS_CHILE_POSTCODE_FULL" domainObjectName="ExpressChilePostcodeFull" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
         <table tableName="EXPRESS_CHILE_ALWAYS_BUY" domainObjectName="ExpressChileAlwaysBuy" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>
         <table tableName="EXPRESS_CHILE_POSTCODE" domainObjectName="ExpressChilePostcode" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->

        <!--  <table tableName="EXPRESSASSIGNMENTACTUAL" domainObjectName="ExpressAssignmentActual"
                 enableCountByExample="false"
                 enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                 selectByExampleQueryId="false"></table>
          <table tableName="EXPRESSASSIGNMENT_EAA" domainObjectName="ExpressAssignmentEaa" enableCountByExample="false"
                 enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                 selectByExampleQueryId="false"></table>-->
        <!--  <table tableName="TRANSIT_POINT_ECCEZIONI" domainObjectName="TransitPointEccezioni"
                 enableCountByExample="false"
                 enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                 selectByExampleQueryId="false"></table>
          <table tableName="TRANSIT_POINT" domainObjectName="TransitPoint"
                 enableCountByExample="false"
                 enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                 selectByExampleQueryId="false"></table>-->
        <!--    <table tableName="DISPATCH_ORDER" domainObjectName="DispatchOrder"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table tableName="QUOTATION_ORDER_DETAIL" domainObjectName="QuotationOrderDetail"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table tableName="QUOTATION_ORDER" domainObjectName="QuotationOrder"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table tableName="APPOINT_TRUCK" domainObjectName="AppointTruck"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table tableName="DECLARATION" domainObjectName="Declaration"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table tableName="DELIVERY_ORDER" domainObjectName="DeliveryOrder"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table schema="CEOP" tableName="EXPRESSITEM" domainObjectName="Expressitem"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table  tableName="DISPATCH_ORDER_DETAIL" domainObjectName="DispatchOrderDetail"
                      enableCountByExample="false"
                      enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                      selectByExampleQueryId="false"></table>
               <table  tableName="ASSIGNMENT_PACKAGES" domainObjectName="AssignmentPackages"
                       enableCountByExample="false"
                       enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                       selectByExampleQueryId="false"></table>
               <table  tableName="DECLARATION_FILE" domainObjectName="DeclarationFile"
                       enableCountByExample="false"
                       enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
                       selectByExampleQueryId="false"></table>-->
        <!--<table  tableName="EAWB_CUSTOM_ITEM" domainObjectName="EawbCustomItem"
                enableCountByExample="false"
                enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"></table>
        <table  tableName="EAWB_CUSTOME_ITEM_TAX" domainObjectName="EawbCustomeItemTax"
                enableCountByExample="false"
                enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"></table>-->
        <!--<table tableName="CARGO_ULDLIST" domainObjectName="CargoUldList" enableCountByExample="false" enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false" selectByExampleQueryId="false"></table>-->
        <!--<table tableName="TOPSCAN_RECORD" domainObjectName="TopscanRecord" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>
        <table tableName="topscan_period" domainObjectName="TopscanPeriod" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>
        <table tableName="TOPSCAN_PERIOD_DETAIL" domainObjectName="TopscanPeriodDetail" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->
        <!--new -->
        <!--<table tableName="CHAPTER_TASK" domainObjectName="ChapterTask" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->
        <!--new -->
        <!--new -->
        <!--<table tableName="CHAPTER_TASK_EAA" domainObjectName="ChapterTaskEaa" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->
        <!--new -->
        <!--<table tableName="CHAPTER_TASK" domainObjectName="ChapterTask" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->
        <!--new -->
        <!--        <table tableName="wc_off_shelf_task_eawb" domainObjectName="WcOffShelfTaskEawb" enableCountByExample="false"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false"></table>-->
        <!--        <table tableName="wc_off_shelf_task_ds" domainObjectName="WcOffShelfTaskDs" enableCountByExample="false"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false"></table>-->

        <!--        <table tableName="PCS_DOMESTIC_SHIPMENT" domainObjectName="PcsDomesticShipment" enableCountByExample="false"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false"></table>-->
        <!--        <table tableName="PCS_DS_WC_DS" domainObjectName="PcsDsWcDs" enableCountByExample="false"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false"></table>-->
        <!--        <table tableName="PCS_SKU" domainObjectName="PcsSku" enableCountByExample="false"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false"></table>-->
        <!--        <table tableName="PCS_SKU_WC_DS" domainObjectName="PcsSkuWcDs" enableCountByExample="false"-->
        <!--               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
        <!--               selectByExampleQueryId="false"></table>-->
        <!--<table tableName="WC_DS_EXCEPTION" domainObjectName="WcDsException" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>
        <table tableName="PCS_DS_TRACK" domainObjectName="PcsDsTrack" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>
        <table tableName="PCS_DS_PIC" domainObjectName="PcsDsPic" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>
        <table tableName="PCS_DOMESTIC_SHIPMENT" domainObjectName="PcsDomesticShipment" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->

        <!--<table tableName="FILE_BIND" domainObjectName="FileBind" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false"></table>-->
        <!--<table tableName="APP_ACCOUNT" domainObjectName="AppAccount" enableCountByExample="false"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
               <!--selectByExampleQueryId="false" schema="CEOP"></table>-->
        <!--<table tableName="wc_domestic_shipment" domainObjectName="WcDomesticShipment" enableCountByExample="false"-->
               <!--enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"-->
               <!--selectByExampleQueryId="false" schema="CEOP"></table>-->
        <table tableName="EXPRESS_CN_GLO_CD_STD_POSTCODE" domainObjectName="ExpressCnGloCdStdPostcode" enableCountByExample="false"
               enableUpdateByExample="false" enableDeleteByExample="false" enableSelectByExample="false"
               selectByExampleQueryId="false" schema="CEOP"></table>
    </context>
</generatorConfiguration>
