package com.sinoair.core.webservice.colisprive;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.webservice.colisprive.setparcel.request.CsgAdd;
import com.sinoair.core.webservice.colisprive.setparcel.request.DlvrAddress;
import com.sinoair.core.webservice.colisprive.setparcel.request.GPSCoord;
import com.sinoair.core.webservice.colisprive.setparcel.request.SetParcelRequest;
import com.sinoair.core.webservice.colisprive.setparcel.response.SetParcelResult;
import com.sinoair.core.webservice.colisprive.setshipment.response.SetShipmentResponse;
import org.junit.Ignore;
import org.junit.Test;

import java.math.BigDecimal;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: 大雄
 * @date: 2019-04-02
 * @time: 10:54
 * @description: To change this template use File | Settings | File Templates.
 */
public class ColisPriveApiUtilTest extends CommonTestCase {

    //    @Ignore
    @Test
    public void setParcel() {
        setParcelCP();
        setParcelCS();
    }

    private void setParcelCP() {
        SetParcelRequest setParcelRequest = new SetParcelRequest();
        setParcelRequest.setOrderID("SINO0004232");
        setParcelRequest.setCltNum("EM190319");
        setParcelRequest.setPclShipDate("20190402");
        setParcelRequest.setPclWeight(156);
        setParcelRequest.setPclWithPOD(false);
        setParcelRequest.setLabelFormat("PDF_ZEBRA");
        CsgAdd csgAdd = new CsgAdd();
        csgAdd.setDlvrName("Jack");
        csgAdd.setDlvrPhon("0682856013");
        DlvrAddress dlvrAddress = new DlvrAddress();
        dlvrAddress.setAdd1("ĬĮİΊΪЇΊ DE LA ĂĄǺǼΆ");
        dlvrAddress.setZc("88400");
        dlvrAddress.setCity("GERARDMER");
        dlvrAddress.setCountry("FR");
        GPSCoord gpsCoord = new GPSCoord();
        gpsCoord.setLat(new BigDecimal(0));
        gpsCoord.setLongs(new BigDecimal(0));
        dlvrAddress.setGpsCoord(gpsCoord);
        csgAdd.setDlvrAddress(dlvrAddress);
        setParcelRequest.setCsgAdd(csgAdd);
        try {
            SetParcelResult setParcelResult = ColisPriveApiUtil.setParcel(setParcelRequest);
            System.out.println(JSONObject.toJSONString(setParcelResult));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    private void setParcelCS() {
        SetParcelRequest setParcelRequest = new SetParcelRequest();
        setParcelRequest.setOrderID("SINO0000004");
        setParcelRequest.setCltNum("EM190319");
        setParcelRequest.setPclShipDate("20190402");
        setParcelRequest.setPclWeight(156);
        setParcelRequest.setPclWithPOD(false);
        setParcelRequest.setLabelFormat("PDF_ZEBRA");
        CsgAdd csgAdd = new CsgAdd();
        csgAdd.setDlvrName("Jack");
        csgAdd.setDlvrPhon("0682856013");
        setParcelRequest.setCsgAdd(csgAdd);
        DlvrAddress dlvrAddress = new DlvrAddress();
        dlvrAddress.setAdd1("7 CHEMIN DE LA FONTAINE");
        dlvrAddress.setZc("92300");
        dlvrAddress.setCity("GERARDMER");
        dlvrAddress.setCountry("FR");
        csgAdd.setDlvrAddress(dlvrAddress);
        GPSCoord gpsCoord = new GPSCoord(new BigDecimal(0), new BigDecimal(0));
        dlvrAddress.setGpsCoord(gpsCoord);
        try {
            SetParcelResult setParcelResult = ColisPriveApiUtil.setParcel(setParcelRequest);
            System.out.println(JSONObject.toJSONString(setParcelResult));
        } catch (Exception e) {
            e.printStackTrace();
        }
    }

    @Ignore
    @Test
    public void setShipment() {
        try {
            SetShipmentResponse setShipmentResponse = ColisPriveApiUtil.setShipment();
            System.out.println(JSONObject.toJSONString(setShipmentResponse));
            System.out.println("------------");
        } catch (Exception e) {
            e.printStackTrace();
            System.out.println("---------------");
        }
    }
}