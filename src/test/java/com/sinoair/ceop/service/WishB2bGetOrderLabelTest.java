package com.sinoair.ceop.service;

import com.sinoair.ceop.domain.vo.wish.WishB2bGetOrderLabelReturnVo;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;

import javax.annotation.Resource;

public class WishB2bGetOrderLabelTest extends CommonTestCase {


    @Resource(name = "WishB2bService")
    private WishB2bService wishB2bService;

    @Test
    public void testWishReport() throws Exception {
        String json = "{\n" +
                "\t\"tracking_number\": \"a1\",\n" +
                "\t\"box_ids\": [\"box1\", \"box2\"]\n" +
                "\n" +
                "}";
        WishB2bGetOrderLabelReturnVo result = wishB2bService.orderLabel(json);

    }


}
