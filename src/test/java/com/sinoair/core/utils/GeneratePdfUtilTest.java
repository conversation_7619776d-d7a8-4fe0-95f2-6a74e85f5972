package com.sinoair.core.utils;

import org.junit.Assert;
import org.junit.Test;

import java.util.Calendar;

/**
 * Created by WangXX4 on 2017-03-29.
 */
public class GeneratePdfUtilTest {
    @Test
    public void acquirePdfUrl() throws Exception {

        String xml = "<?xml version=\"1.0\" standalone=\"yes\"?>\n" +
                "<DocumentElement>\n" +
                "\t<NewOrderPrintItem>\n" +
                "\t\t<System_id>1</System_id>\n" +
                "\t\t<Customer_id>124515</Customer_id>\n" +
                "\t\t<Gekou_code />\n" +
                "\t\t<Order_id>lida20171026test004</Order_id>\n" +
                "\t\t<Product_shortname>Men's T-Shirts</Product_shortname>\n" +
                "\t\t<product_postcustomername>.</product_postcustomername>\n" +
                "\t\t<product_postname>.</product_postname>\n" +
                "\t\t<Product_signperson>.</Product_signperson>\n" +
                "\t\t<return_name>1</return_name>\n" +
                "\t\t<return_address>2</return_address>\n" +
                "\t\t<return_telehpone>3</return_telehpone>\n" +
                "\t\t<return_postcode>4</return_postcode>\n" +
                "\t\t<pickup_zone>5</pickup_zone>\n" +
                "\t\t<Order_customerinvoicecode>lida20171026test004</Order_customerinvoicecode>\n" +
                "\t\t<Order_serveinvoicecode>680002800101/0001-00</Order_serveinvoicecode>\n" +
                "\t\t<order_transfercode/>\n" +
                "\t\t<Order_weightkg>10</Order_weightkg>\n" +
                "\t\t<Order_weightg>1000.000</Order_weightg>\n" +
                "\t\t<Ct_code>HR</Ct_code>\n" +
                "\t\t<Ct_ename>UNITED STATES</Ct_ename>\n" +
                "\t\t<Ct_name>美国</Ct_name>\n" +
                "\t\t<Customer_code></Customer_code>\n" +
                "\t\t<Customer_shortname>测试客户</Customer_shortname>\n" +
                "\t\t<Consignee_name>aaaaa</Consignee_name>\n" +
                "\t\t<consignee_companyname>.</consignee_companyname>\n" +
                "\t\t<Consignee_address>cccccc</Consignee_address>\n" +
                "\t\t<Consignee_city>Sirik</Consignee_city>\n" +
                "\t\t<Consignee_state></Consignee_state>\n" +
                "\t\t<Consignee_telephone>00989173669799</Consignee_telephone>\n" +
                "\t\t<consignee_mobile>.</consignee_mobile>\n" +
                "\t\t<consignee_email>.</consignee_email>\n" +
                "\t\t<Sku_code>SDSD*1;</Sku_code>\n" +
                "\t\t<item_id>.</item_id>\n" +
                "\t\t<Invoice_name>DSD*1;</Invoice_name>\n" +
                "\t\t<Invoice_ename>Men's T-Shirts</Invoice_ename>\n" +
                "\t\t<hs_code></hs_code>\n" +
                "\t\t<Invoice_unitamount>12.00</Invoice_unitamount>\n" +
                "\t\t<Invoice_totalamount>12.00</Invoice_totalamount>\n" +
                "\t\t<Invoice_pcs>1</Invoice_pcs>\n" +
                "\t\t<order_transactionurl/>\n" +
                "\t\t<buyer_id>.</buyer_id>\n" +
                "\t\t<order_customnote>.</order_customnote>\n" +
                "\t\t<Product_id>2521</Product_id>\n" +
                "\t\t<Consignee_postcode>7946166153</Consignee_postcode>\n" +
                "\t\t<Hs_code></Hs_code>\n" +
                "\t\t<Order_createdate>2016-12-08 09:24:05</Order_createdate>\n" +
                "\t\t<Order_returnsign>N</Order_returnsign>\n" +
                "\t\t<Shipper_name>shipper_name</Shipper_name>\n" +
                "\t\t<Shipper_companyname>shipper_company</Shipper_companyname>\n" +
                "\t\t<Shipper_address>shipper_addr</Shipper_address>\n" +
                "\t\t<shipper_state>shipper_name</shipper_state>\n" +
                "\t\t<shipper_city>shipper_city</shipper_city>\n" +
                "\t\t<Shipper_country>CN</Shipper_country>\n" +
                "\t\t<Shipper_postcode>51800</Shipper_postcode>\n" +
                "\t\t<Shipper_telephone>151126787612</Shipper_telephone>\n" +
                "\t\t<shipper_countryname>中国</shipper_countryname>\n" +
                "\t\t<shipper_countryename>CHINA</shipper_countryename>\n" +
                "\t\t<pkg_print_code>33333333333333333333</pkg_print_code>\n" +
                "\t\t<rpx_info/>\n" +
                "\t</NewOrderPrintItem>\n" +
                "</DocumentElement>";
        System.out.println(xml);
        long begin = Calendar.getInstance().getTimeInMillis();
        System.out.println(begin);
        String result = GeneratePdfUtil.acquirePdfUrl(xml);
        long end = Calendar.getInstance().getTimeInMillis();
        System.out.println(end);
        long time = end - begin;
        double timx = Double.valueOf(time) / 1000;
        System.out.println(timx);
        System.out.println("result = " + result);
        Assert.assertNotNull(result);
    }
}