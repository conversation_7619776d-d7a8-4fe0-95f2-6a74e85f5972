# \u6570\u636E\u5E93\u9A71\u52A8jar \u8DEF\u5F84
#drive.class.path=C:\\Users\\<USER>\\.m2\\repository\\com\\oracle\\ojdbc14\\10.2.0.1.0\\ojdbc14-10.2.0.1.0.jar
drive.class.path=D:\\softwareData\\maven\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
#drive.class.path=D:\\.m2\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
#drive.class.path=/Users/<USER>/tools/maven/repository/oracle/ojdbc/6.0/ojdbc-6.0.jar
#drive.class.path=C:\\Users\\<USER>\\.m2\\repository\\oracle\\ojdbc\\6.0\\ojdbc-6.0.jar
# \u6570\u636E\u5E93\u8FDE\u63A5\u53C2\u6570
jdbc.driver=oracle.jdbc.driver.OracleDriver
jdbc.url=********************************************
jdbc.username=CEOP
jdbc.password=CEOP
# \u5305\u8DEF\u5F84\u914D\u7F6E
model.package=com.sinoair.ceop.domain.model
dao.package=com.sinoair.ceop.dao
xml.mapper.package=mapper

#\u653E\u5728test\u4E2D\u751F\u6210 \u518D\u8F6C\u79FB\u5230main \u4EE5\u9632\u8986\u76D6
target.project=src/test/java
