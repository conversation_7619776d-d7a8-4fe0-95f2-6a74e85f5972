package com.sinoair.core.utils.mail;

import org.junit.Test;

/**
 * @author: XueQB
 * @date: 2020/6/24 9:48
 */
public class MailUtilsTest {

    @Test
    public void testSendSimpleText() {
        String[] to = new String[]{"<EMAIL>", "<EMAIL>"};
        String[] cc = new String[]{"<EMAIL>"};
        MailUtils.sendSimpleText(to, cc, "CEOS验证码测试邮件", " 尊敬的用户：您的认证码：8Ge5，工作人员不会索取，请勿泄露【CEOS】，发送时间：2020-06-24 09:41:35");
        //try {
        //    MailUtils.postMailUtilNoHtml("<EMAIL> <EMAIL>", "<EMAIL>", "",
        //            "CEOS验证码测试邮件",
        //            " 尊敬的用户：您的认证码：8Ge5，工作人员不会索取，请勿泄露【CEOS】，发送时间：2020-06-24 09:41:35",
        //            null);
        //} catch (MessagingException e) {
        //    e.printStackTrace();
        //} catch (IOException e) {
        //    e.printStackTrace();
        //}
    }
}