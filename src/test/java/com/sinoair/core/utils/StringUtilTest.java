package com.sinoair.core.utils;

import org.junit.Test;

import static org.junit.Assert.assertEquals;

/**
 * Created by ZhangMJ on 2016/7/1.
 */
public class StringUtilTest {

    @Test
    public void null2Empty() throws Exception {
        assertEquals("", StringUtil.null2Empty("", false));
        assertEquals("", StringUtil.null2Empty(" ", true));
        assertEquals("", StringUtil.null2Empty(null, false));
        assertEquals("jo uojoij", StringUtil.null2Empty(" jo uojoij ", true));
    }

    @Test
    public void filterCharacter() throws Exception {
        assertEquals("", StringUtil.filterCharacter(null, null));
        assertEquals("", StringUtil.filterCharacter("", null));
        assertEquals(" oioijoi 789 yih ", StringUtil.filterCharacter(" oioi,joi 78\n9 \ryi\"h ", null));
        assertEquals(" oioi joi 78 9  yi h ", StringUtil.filterCharacter(" oioi,joi 78\n9 \ryi\"h ", " "));
    }

    @Test
    public void additionalCharacter() throws Exception {
        assertEquals("", StringUtil.additionalCharacter(null, null));
        assertEquals("", StringUtil.additionalCharacter("", null));
        assertEquals(" aaaa", StringUtil.additionalCharacter(" aaaa", null));
        assertEquals("aaaa,", StringUtil.additionalCharacter("aaaa", ","));
        assertEquals(" aaaa\n", StringUtil.additionalCharacter(" aaaa", "\n"));
        assertEquals(" aaaa ", StringUtil.additionalCharacter(" aaaa", " "));
    }

    @Test
    public void csvDealString() throws Exception {
        assertEquals("", StringUtil.csvDealString(""));
        assertEquals("", StringUtil.csvDealString(null));
        assertEquals("  aa a a ", StringUtil.csvDealString(" \"aa\ra\na,"));


    }

    @Test
    public void csvDealStringAddComma() throws Exception {
        assertEquals(",", StringUtil.csvDealStringAddComma(null));
        assertEquals(",", StringUtil.csvDealStringAddComma(""));
        assertEquals("aaaa ,", StringUtil.csvDealStringAddComma("aaaa,"));
    }

    @Test
    public void csvDealStringAddNewline() throws Exception {
        assertEquals("\n", StringUtil.csvDealStringAddNewline(null));
        assertEquals("\n", StringUtil.csvDealStringAddNewline(""));
        assertEquals("aaaa \n", StringUtil.csvDealStringAddNewline("aaaa,"));
        System.out.println("748789789".indexOf("33"));
    }

    @Test
    public void testToBlank() {
        String code = "420357019274892700509702997323";
        System.out.println(StringUtil.toBlankCode(code));
        System.out.println(code.substring(8));
    }

    @Test
    public void testJsonToCsv() {
        String json = "[{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020517\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020518\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020519\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020135\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020598\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020599\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020512\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020513\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020514\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020515\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020516\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020605\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020520\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020641\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020521\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020642\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020522\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020643\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020600\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020523\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020644\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020524\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020601\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020645\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020602\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020525\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020646\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020603\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020647\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020604\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020648\"},{\"cNType\":\"CN38\",\"cNote\":\"CNHGHA020140\"}]";
        String csv = StringUtil.jsonToCsv(json);
        System.out.println(csv);

    }
}