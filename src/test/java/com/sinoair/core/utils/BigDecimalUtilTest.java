package com.sinoair.core.utils;

import org.junit.Test;

import java.math.BigDecimal;

import static org.junit.Assert.*;

/**
 * Created by ZhangMJ on 2017/4/27.
 */
public class BigDecimalUtilTest {

    @Test
    public void divideRoundedDecimal2() throws Exception {
        //System.out.println(BigDecimalUtil.divideRoundedDecimal2min(new BigDecimal(1),new BigDecimal(1000),BigDecimalUtil.bigDecimalScale2("0.015")));
        //System.out.println(BigDecimalUtil.bigDecimalScale2("0.01566666").doubleValue());
        /*String str="0.98789999";
        BigDecimal bigDecimal =new BigDecimal(str).setScale(2, BigDecimal.ROUND_FLOOR);

        System.out.println(bigDecimal);
        System.out.println(bigDecimal.doubleValue());
        System.out.println(bigDecimal.toString());

        System.out.println("---------------------------------------------------------------------------------------------------");

        BigDecimal bigDecimal2 =new BigDecimal(str);
        bigDecimal2=bigDecimal2.setScale(2,BigDecimal.ROUND_FLOOR);
        System.out.println(bigDecimal2);
        System.out.println(bigDecimal2.doubleValue());
        System.out.println(bigDecimal2.toString());

        System.out.println("---------------------------------------------------------------------------------------------------");*/

        System.out.println(BigDecimalUtil.divideRoundedDecimal2minFloor(new BigDecimal("10"),new BigDecimal("3"),new BigDecimal("0.01")));
        System.out.println(BigDecimalUtil.divideRoundedDecimal2minFloor(new BigDecimal("10"),new BigDecimal("6"),new BigDecimal("0.01")));
        System.out.println(BigDecimalUtil.divideRoundedDecimal2minUp(new BigDecimal("10"),new BigDecimal("3"),new BigDecimal("0.01")));
        System.out.println(BigDecimalUtil.divideRoundedDecimal2minUp(new BigDecimal("10"),new BigDecimal("6"),new BigDecimal("0.01")));


      /*  BigDecimal mData = new BigDecimal("9.655").setScale(2, BigDecimal.ROUND_HALF_UP);
        System.out.println("mData=" + mData);*/

    }
}