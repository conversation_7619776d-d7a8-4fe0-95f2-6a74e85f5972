package com.sinoair.core.utils;

import com.alibaba.fastjson.JSONObject;
import com.sinoair.ceop.domain.model.EdiRecordWithBLOBs;
import com.sinoair.ceop.service.uralYt.SignUtil;
import org.junit.Ignore;
import org.junit.Test;

import java.io.IOException;
import java.security.KeyManagementException;
import java.security.NoSuchAlgorithmException;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @author: XueQB
 * @date: 2021-07-09 14:38
 */
public class HttpUtilsTest {


    /**
     * 测试以色列邮政对接获取TOken
     *
     * @throws NoSuchAlgorithmException
     * @throws IOException
     * @throws KeyManagementException
     */
    @Ignore
    @Test
    public void sendPostNoSSLWithJson() throws NoSuchAlgorithmException, IOException, KeyManagementException {
        String USERNAME_SINOAIR_C = "<EMAIL>";
        String PASSWORD_SINOAIR_C = "9bBB6uvhwnRo";
        String SUBSCRIPTION_KEY_SINOAIR_C = "9e9788afb92b4361aefbf21079c9a189";
        String GETTOKEN_URL = "https://apimftprd.israelpost.co.il/Auth/GetToken";
        Map<String, String> header = new HashMap<>();
        header.put("Content-Type", "application/json");
        String json = "{\n" +
                "  \"Username\": \"" + USERNAME_SINOAIR_C + "\",\n" +
                "  \"Password\": \"" + PASSWORD_SINOAIR_C + "\"\n" +
                "}";
        header.put("Ocp-Apim-Subscription-Key", SUBSCRIPTION_KEY_SINOAIR_C);
        String response = HttpUtils.sendPostNoSSLWithJson(GETTOKEN_URL, header, json, "UTF-8", 1000 * 60, 1000 * 60 * 2);
        System.out.println(response);
        response = HttpClientUtil.postStr(GETTOKEN_URL, header, json);
        System.out.println(response);
    }



    @Test
    public void testSendJson2() throws NoSuchAlgorithmException, IOException, KeyManagementException {

        String url =  "https://stable-api.kazexpress.com/cdPackage/createPackage";
        String requestBody = "{\"sourcePackageNo\":\"SN104004279\",\"waybillList\":[\"TZB00066615TPKZ\",\"TZB00063490TPKZ\",\"TZB00065647TPKZ\",\"TZB00066822TPKZ\",\"TZB00070207TPKZ\",\"TZB00068797TPKZ\",\"TZB00065619TPKZ\",\"TZB00070621TPKZ\"]}";
        String sign = SignUtil.sign(requestBody,"HQUALi8bvNGZ","ZCEZcqxfVvzs5mzdC5Z2SZLzpDR");
        Map<String,String> header = new HashMap<String,String>();
        header.put("key","HQUALi8bvNGZ");
        header.put("sign",sign);
        long start = System.currentTimeMillis();
        String response = HttpClientUtil.postStr(url,header,requestBody);
        long end = System.currentTimeMillis();
        long timecost = end-start;
        System.out.println("response:::"+response);
        System.out.println("timecost:::::"+timecost);
    }
}