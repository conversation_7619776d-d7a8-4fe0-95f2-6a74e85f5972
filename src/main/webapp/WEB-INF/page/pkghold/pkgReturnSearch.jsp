<%@ page contentType="text/html;charset=UTF-8" language="java" %>
<%@ include file="/WEB-INF/commons/taglibs.jsp" %>
<%@ taglib tagdir="/WEB-INF/tags" prefix="tags" %>
<!DOCTYPE html>
<html>
<head>
    <title>退件查询</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="shortcut icon" href="/img/favicon.ico">
    <link href="/css/bootstrap.min.css?v=3.3.5" rel="stylesheet">
    <link href="/css/font-awesome.min.css?v=4.4.0" rel="stylesheet">
    <link href="/css/plugins/bootstrap-table/bootstrap-table.min.css" rel="stylesheet">
    <link href="/css/animate.min.css" rel="stylesheet">
    <link href="/css/style.min.css?v=4.0.0" rel="stylesheet">
    <link rel="stylesheet" href="/css/viewer.min.css">
    <link href="/css/plugins/sweetalert/sweetalert.css" rel="stylesheet">
    <link href="${ctx}/css/plugins/bootstrap-datetimepicker/bootstrap-datetimepicker.min.css?v=${v}" rel="stylesheet"
          media="screen">
    <link href="${ctx}/css/plugins/bootstrap-datetimepicker/2.44/bootstrap-datetimepicker.min.css?v=${v}"
          rel="stylesheet" media="screen">
    <link href="${ctx}/css/plugins/bootstrap-table/bootstrap-table.css?v=${v}" rel="stylesheet">
    <link href="/css/plugins/iCheck/custom.css" rel="stylesheet">

</head>
<body class="gray-bg">
<div class="wrapper wrapper-content">
    <div class="row">
        <div class="col-sm-12">
            <div class="ibox float-e-margins">
                <!-- 搜索 -->
                <div class="ibox-content">
                    <form id="searchForm">
                        <div class="form-inline" style="margin-top: 10px;">
                            <div class="form-group " style="">
                                <label class="control-label">提单号: </label>
                                <input class="form-control" name="mawbCode" type="text"
                                       id="mawbCode"/>
                            </div>
                            <div class="form-group " style="">
                                <label class="control-label">大箱号: </label>
                                <input class="form-control" name="pkgCode" type="text"
                                       id="pkgCode"/>
                            </div>
                            <div class="form-group " style="">
                                <label class="control-label">包裹号/运单号: </label>
                                <input class="form-control" name="parcelCode" type="text" id="parcelCode"/>
                            </div>
                            <div class="form-group" style="">
                                <label class="control-label">包裹类型：</label>
                                <select class="form-control" name="packageType" id="packageType">
                                    <option value="">请选择</option>
                                    <option value="P">整包</option>
                                    <option value="I">散件</option>
                                    <option value="PENDING">待处理</option>
                                </select>
                            </div>
                            <div class="form-group " style="">
                                <label class="control-label">创建时间</label>
                                <div class='input-group date form_date' id='startDateDiv'
                                     data-date-format="yyyy-mm-dd" data-link-field="startDate"
                                     data-link-format="yyyy-mm-dd">
                                    <input class="form-control" size="7" name="startDate" type="text" id="startDate"
                                           readonly>
                                    <span class="input-group-addon clear-btn"><i class="glyphicon glyphicon-remove"></i></span>
                                    <span class="input-group-addon"><span
                                            class="glyphicon glyphicon-calendar"></span></span>
                                </div>
                                -
                                <div class='input-group date form_date' id='endDateDiv'
                                     data-date-format="yyyy-mm-dd" data-link-field="endDate"
                                     data-link-format="yyyy-mm-dd">
                                    <input class="form-control" size="7" name="endDate" type="text" id="endDate"
                                           readonly>
                                    <span class="input-group-addon clear-btn"><i class="glyphicon glyphicon-remove"></i></span>
                                    <span class="input-group-addon"><span
                                            class="glyphicon glyphicon-calendar"></span></span>
                                </div>
                            </div>


                        </div>
                    </form>
                    <div class="form-group tooltip-demo " style="margin-top: 1vh;">
                        <button class="btn btn-primary" id="myBtn" type="button"
                                style="margin-top: 5px;margin-left: 10px" onclick="search()">查询
                        </button>
                        <button class="btn btn-success" id="exportBtn" type="button"
                                style="margin-top: 5px;margin-left: 10px" onclick="exportData()">导出
                        </button>
                        <button class="btn btn-success" id="inputBtn" type="button"
                                style="margin-top: 5px;margin-left: 10px" onclick="inputData()">信息补录
                        </button>
                    </div>
                    <table id="myTable"
                           data-toggle="table"
                           data-side-pagination="server"
                           data-method="post"
                           data-url="${ctx}/pkgreturn/data"
                           data-pagination="true"
                           data-toolbar="#toolbar"
                           data-page-size="10"
                           data-page-list="[10, 25, 50, 100]"
                           data-query-params="bootstrapTableParam"
                           data-click-to-select="true"
                    >
                        <thead>
                        <th data-formatter="indexFormatter">序号</th>
                        <th data-field="mawbCode">提单号</th>
                        <th data-field="pkgPrintcode">大箱号</th>
                        <th data-field="eawbReference1">包裹号</th>
                        <th data-field="eawbReference2">运单号</th>
                        <th data-field="skuCode">散件SKC号</th>
                        <th data-field="detentionQty">散件商品数量</th>
                        <th data-field="detentionType" data-formatter='typeFormater'>包裹类型</th>
                        <th data-field="userName">创建人</th>
                        <th data-field="createTime" data-formatter="timeFormatter">创建时间</th>
                        <th data-field="URL" data-formatter="picFormater">操作</th>
                        </thead>
                    </table>

                </div>
            </div>
        </div>
    </div>
    <!-- 补录信息 Modal -->
    <div class="modal fade" id="inputModal" tabindex="-1" role="dialog" aria-labelledby="inputModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="inputForm">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="inputModalLabel">信息补录</h4>
                    </div>
                    <div class="modal-body">

                        <div class="form-group">
                            <label> <span style="color: red;">*</span>包裹类型：</label>
                            <select class="form-control" id="inputPackageType">
                                <option value="PARCEL">包裹/运单</option>
                                <option value="ITEM">散件</option>
                            </select>
                        </div>
                        <div class="form-group" id="parcelCodeGroup" style="display: none;">
                            <label><span style="color: red;">*</span> 包裹号：</label>
                            <input type="text" class="form-control" id="inputParcelCode" placeholder="请输入包裹号">
                        </div>
                        <div class="form-group" id="itemCodeGroup" style="display: none;">
                            <label><span style="color: red;">*</span> 散件SKC号：</label>
                            <input type="text" class="form-control" id="inputItemCode" placeholder="请输入散件SKC号">
                        </div>
                        <div class="form-group" id="quantityGroup" style="display: none;">
                            <label><span style="color: red;">*</span> 数量：</label>
                            <input type="number" class="form-control" id="inputQuantity" placeholder="请输入数量">
                        </div>
                        <div class="form-group">
                            <label><span id="inputMawbRequired" style="color: red; display: none;">*</span>提单号：</label>
                            <input type="text" class="form-control" id="inputMawbCode" placeholder="请输入提单号">
                        </div>
                        <div class="form-group">
                            <label>大箱号：</label>
                            <input type="text" class="form-control" id="inputPkgCode" placeholder="请输入大箱号">
                        </div>
                        <!-- 新增运单号字段 -->
                        <div class="form-group">
                            <label>运单号：</label>
                            <input type="text" class="form-control" id="inputPackageNo" placeholder="请输入运单号">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- 编辑补录信息 Modal -->
    <div class="modal fade" id="editInputModal" tabindex="-1" role="dialog" aria-labelledby="editInputModalLabel">
        <div class="modal-dialog" role="document">
            <div class="modal-content">
                <form id="editInputForm">
                    <div class="modal-header">
                        <button type="button" class="close" data-dismiss="modal" aria-label="Close"><span
                                aria-hidden="true">&times;</span></button>
                        <h4 class="modal-title" id="editInputModalLabel">编辑补录信息</h4>
                    </div>
                    <div class="modal-body">
                        <!-- 图片预览区域 -->
                        <div class="form-group">
                            <label>图片预览：</label>
                            <div id="imagePreviewContainer"></div>
                        </div>

                        <!-- 隐藏的 recordId 字段 -->
                        <input type="hidden" id="editRecordId">


                        <div class="form-group">
                            <label><span style="color: red;">*</span>包裹类型：</label>
                            <select class="form-control" id="editPackageType">
                                <option value="PARCEL">包裹/运单</option>
                                <option value="ITEM">散件</option>
                            </select>
                        </div>
                        <div class="form-group" id="editParcelCodeGroup" style="display: none;">
                            <label><span style="color: red;">*</span> 包裹号：</label>
                            <input type="text" class="form-control" id="editParcelCode" placeholder="请输入包裹号">
                        </div>
                        <div class="form-group" id="editItemCodeGroup" style="display: none;">
                            <label><span style="color: red;">*</span> 散件SKC号：</label>
                            <input type="text" class="form-control" id="editItemCode" placeholder="请输入散件SKC号">
                        </div>
                        <div class="form-group" id="editQuantityGroup" style="display: none;">
                            <label><span style="color: red;">*</span> 数量：</label>
                            <input type="number" class="form-control" id="editQuantity" placeholder="请输入数量">
                        </div>
                        <div class="form-group">
                            <label><span id="editMawbRequired" style="color: red; display: none;">*</span>提单号：</label>
                            <input type="text" class="form-control" id="editMawbCode" placeholder="请输入提单号">
                        </div>
                        <div class="form-group">
                            <label>大箱号：</label>
                            <input type="text" class="form-control" id="editPkgCode" placeholder="请输入大箱号">
                        </div>
                        <!-- 新增运单号字段 -->
                        <div class="form-group">
                            <label>运单号：</label>
                            <input type="text" class="form-control" id="editPackageNo" placeholder="请输入运单号">
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-default" data-dismiss="modal">取消</button>
                        <button type="submit" class="btn btn-primary">提交</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

</div>
<script src="/js/jquery.min.js?app.version=${v}"></script>
<script src="/js/bootstrap.min.js?v=3.3.5"></script>
<script src="/js/plugins/peity/jquery.peity.min.js"></script>
<script src="/js/content.min.js?v=1.0.0"></script>
<script src="/js/demo/peity-demo.min.js"></script>
<script src="/js/common/commonAjax.js"></script>
<script src="/js/viewer-jquery.min.js"></script>
<script src="/js/plugins/sweetalert/sweetalert.min.js"></script>
<script src="${ctx}/js/plugins/bootstrap-datetimepicker/2.44/bootstrap-datetimepicker.min.js?v=${v}"></script>
<script src="${ctx}/js/plugins/bootstrap-datetimepicker/2.44/locales/bootstrap-datetimepicker.zh-CN.js?v=${v}"></script>
<script src="${ctx}/js/sprint.js"></script>
<script src="${ctx}/js/plugins/bootstrap-table/bootstrap-table.js?v=${v}"></script>
<script src="${ctx}/js/plugins/bootstrap-table/locale/bootstrap-table-zh-CN.min.js?v=${v}"></script>
<script src="/js/plugins/iCheck/icheck.min.js"></script>
<script src="/js/plugins/layer/laydate/laydate.js"></script>
<script src="/js/plugins/layer/layer.min.js"></script>
<script src="/js/plugins/layer/extend/layer.ext.js"></script>
<script>

    function bootstrapTableParam(params) {
        params.search = '123'
        return params;
    }

    function completeRecord(recordId, imgUrl) {
        // 清空表单内容
        $('#editMawbCode').val('');
        $('#editPkgCode').val('');
        $('#editPackageType').val('PARCEL');
        $('#editParcelCode').val('');
        $('#editItemCode').val('');
        $('#editQuantity').val('');
        $('#editPackageNo').val('');

        // 设置 recordId
        $('#editRecordId').val(recordId);

        // 清空之前的图片预览
        $('#imagePreviewContainer').empty();

        // 销毁之前的viewer实例（如果存在）
        if ($('#imagePreviewContainer').data('viewer')) {
            $('#imagePreviewContainer').viewer('destroy');
        }

        // 图片预览并启用 viewer.js
        if (imgUrl && imgUrl.trim() !== '') {
            const urls = imgUrl.split(';');
            const container = $('#imagePreviewContainer');

            urls.forEach(function(url) {
                if (url.trim() !== '') {
                    const imgElement = $('<img>').attr('src', url.trim())
                        .addClass('thumbnail')
                        .css({
                            width: '80px',
                            height: '80px',
                            cursor: 'pointer'
                        });
                    container.append(imgElement);
                }
            });

            console.log('Initializing viewer.js...');

            // 延迟初始化，确保图片已经添加到DOM
            setTimeout(() => {
                try {
                    // 销毁可能存在的viewer实例
                    if ($('#imagePreviewContainer').data('viewer')) {
                        $('#imagePreviewContainer').viewer('destroy');
                    }

                    // 初始化 viewer.js - 先用基本配置
                    $('#imagePreviewContainer').viewer({
                        navbar: true,
                        toolbar: true,
                        title: false,
                        fullscreen: true,
                        keyboard: true,
                        zoomable: true,
                        scalable: true,
                        rotatable: true,
                        movable: true,
                        transition: true,
                        backdrop: true,
                        loading: true,
                        loop: true,
                        inline: false,
                        button: true,
                        tooltip: true,
                        toggleOnDblclick: true,
                        initialViewIndex: 0,
                        zoomRatio: 0.1,
                        minZoomRatio: 0.01,
                        maxZoomRatio: 100
                    });

                    // 使用jQuery事件绑定来处理全屏显示
                    $('#imagePreviewContainer').off('show.viewer').on('show.viewer', function (e) {
                        console.log('Viewer show event triggered');
                        setTimeout(() => {
                            const viewer = $(this).data('viewer');
                            if (viewer && viewer.container) {
                                const $container = $(viewer.container);

                                // 将容器移动到body下，脱离modal限制
                                if ($container.parent()[0] !== document.body) {
                                    $container.appendTo('body');
                                }

                                // 确保容器全屏显示并重置所有可能影响定位的样式
                                $container.css({
                                    'position': 'fixed',
                                    'top': '0',
                                    'left': '0',
                                    'height': '100vh',
                                    'z-index': '9999',
                                    'background': 'rgba(0, 0, 0, 0.9)',
                                    'margin': '0',
                                    'padding': '0',
                                    'transform': 'none',
                                    'overflow': 'hidden'
                                });

                                // 确保viewer canvas也正确定位，往左偏移一点
                                const $canvas = $container.find('.viewer-canvas');
                                if ($canvas.length) {
                                    $canvas.css({
                                        'position': 'absolute',
                                        'top': '50%',
                                        'left': '45%',
                                        'transform': 'translate(-50%, -50%)'
                                    });
                                }
                            }
                        }, 50);
                    });

                    // 使用jQuery事件绑定来处理居中显示
                    $('#imagePreviewContainer').off('viewed.viewer').on('viewed.viewer', function (e) {
                        console.log('Viewer viewed event triggered');
                        setTimeout(() => {
                            const viewer = $(this).data('viewer');
                            if (viewer) {
                                try {
                                    // 计算适合屏幕的缩放比例，为工具栏留出更多空间
                                    const image = viewer.image;
                                    if (image && image.naturalWidth && image.naturalHeight) {
                                        const containerWidth = window.innerWidth;
                                        const containerHeight = window.innerHeight;
                                        const imageWidth = image.naturalWidth;
                                        const imageHeight = image.naturalHeight;

                                        // 计算缩放比例，留出更多空间给工具栏
                                        const scaleX = (containerWidth * 0.7) / imageWidth;
                                        const scaleY = (containerHeight * 0.7) / imageHeight;
                                        const scale = Math.min(scaleX, scaleY, 1);

                                        viewer.zoomTo(scale);
                                        viewer.moveTo(-50, 0); // 往左偏移50px
                                    }
                                } catch (error) {
                                    console.error('Error in viewed event:', error);
                                    // 如果出错，至少尝试居中
                                    try {
                                        viewer.zoomTo(0.8);
                                        viewer.moveTo(-50, 0); // 往左偏移50px
                                    } catch (e2) {
                                        console.error('Error in fallback:', e2);
                                    }
                                }
                            }
                        }, 200);
                    });

                    console.log('Viewer.js initialized successfully');
                } catch (error) {
                    console.error('Error initializing viewer.js:', error);
                }
            }, 100);
        }

        // 初始化包裹类型
        $('#editPackageType').trigger('change');

        // 显示模态框
        $('#editInputModal').modal('show');
    }


    // 编辑表单提交处理
    $('#editInputForm').on('submit', function (e) {
        e.preventDefault(); // 阻止默认提交行为

        const mawbCode = $('#editMawbCode').val().trim();
        const pkgPrintCode = $('#editPkgCode').val().trim();
        const packageType = $('#editPackageType').val().trim();
        const parcelCode = $('#editParcelCode').val().trim();
        const itemCode = $('#editItemCode').val().trim();
        const quantity = $('#editQuantity').val().trim();
        const recordId = $('#editRecordId').val().trim(); // 获取 recordId
        const packageNo = $('#editPackageNo').val().trim(); // 新增字段

        let errorMsg = '';

        if (packageType === 'PARCEL') {
            // 包裹/运单类型：包裹号必填，提单号非必填
            if (!parcelCode) {
                errorMsg = '请填写包裹号';
            }
        } else if (packageType === 'ITEM') {
            // 散件类型：提单号必填，包裹号非必填，散件SKC号和数量必填
            if (!mawbCode) {
                errorMsg = '请填写提单号';
            } else if (!itemCode || !quantity) {
                errorMsg = '请填写完整的散件SKC号和数量';
            }
        }

        if (errorMsg) {
            swal("提示", errorMsg, "warning");
            return;
        }

        // 构造请求参数
        const postData = {
            mawbCode: mawbCode,
            pkgPrintCode: pkgPrintCode,
            packageType: packageType,
            eawbReference1: parcelCode,
            skuCode: itemCode,
            detentionQty: quantity,
            recordId: recordId,
            packageNo: packageNo // 提交运单号字段
        };

        // 发起 AJAX 请求
        $.ajax({
            url: '${ctx}/pkghold/saveRecord',
            type: 'POST',
            dataType: 'json',
            data: postData,
            success: function (response) {
                if (response && response.msgCode === "SUCCESS") {
                    swal({
                        title: "成功",
                        text: "信息已保存",
                        type: "success"
                    }, function () {
                        $('#editInputModal').modal('hide');
                        $("#myTable").bootstrapTable('refresh');
                    });
                } else {
                    swal("失败", response.message || "保存失败，请重试", "error");
                }
            },
            error: function (xhr, status, error) {
                swal("错误", "网络请求失败：" + error, "error");
            }
        });
    });
    // 监听包裹类型下拉框变化
    $('#editPackageType').on('change', function () {
        const val = $(this).val();
        $('#editParcelCode').val('');
        $('#editItemCode').val('');
        $('#editQuantity').val('');

        $('#editParcelCodeGroup').hide();
        $('#editItemCodeGroup').hide();
        $('#editQuantityGroup').hide();

        if (val === 'PARCEL') {
            $('#editParcelCodeGroup').show();
            $('#editMawbRequired').hide(); // 包裹/运单类型：提单号非必填
        } else if (val === 'ITEM') {
            $('#editParcelCodeGroup').show();
            $('#editItemCodeGroup').show();
            $('#editQuantityGroup').show();
            $('#editMawbRequired').show(); // 散件类型：提单号必填
        }
    });
    $('#editInputModal').on('hidden.bs.modal', function () {
        $('#editInputForm')[0].reset(); // 清空表单
        $('#editParcelCodeGroup').hide();
        $('#editItemCodeGroup').hide();
        $('#editQuantityGroup').hide();

        // 销毁 viewer
        if ($('#imagePreviewContainer').data('viewer')) {
            $('#imagePreviewContainer').viewer('destroy');
        }
    });


    // 打开补录弹窗
    function inputData() {
        // 清空表单内容
        $('#inputMawbCode').val('');
        $('#inputPkgCode').val('');
        $('#inputParcelCode').val('');
        $('#inputItemCode').val('');
        $('#inputPackageNo').val('');
        $('#parcelCodeGroup').hide();
        $('#itemCodeGroup').hide();

        // 显示模态框
        $('#inputModal').modal('show');
        // 设置默认值为 "PARCEL"
        $('#inputPackageType').val('PARCEL');

        // 触发 change 以更新字段显示状态
        $('#inputPackageType').trigger('change');
    }

    // 监听包裹类型下拉框变化
    $('#inputPackageType').on('change', function () {
        const val = $(this).val();
        $('#inputParcelCode').val('');
        $('#inputItemCode').val('');
        $('#inputQuantity').val('');

        $('#parcelCodeGroup').hide();
        $('#itemCodeGroup').hide();
        $('#quantityGroup').hide();


        if (val === 'PARCEL') {
            $('#parcelCodeGroup').show();
            $('#inputMawbRequired').hide(); // 包裹/运单类型：提单号非必填
        } else if (val === 'ITEM') {
            $('#parcelCodeGroup').show();
            $('#itemCodeGroup').show();
            $('#quantityGroup').show();
            $('#inputMawbRequired').show(); // 散件类型：提单号必填
        }
    });
    // $(document).ready(function () {
    //     // 设置默认值为 "PKG"
    //     $('#inputPackageType').val('PKG');
    //
    //     // 触发 change 以更新字段显示状态
    //     $('#inputPackageType').trigger('change');
    // });
    $('#inputModal').on('hidden.bs.modal', function () {
        $('#inputForm')[0].reset(); // 清空表单内容
        $('#parcelCodeGroup').hide();
        $('#itemCodeGroup').hide();
    });
    // 表单提交处理
    $('#inputForm').on('submit', function (e) {
        e.preventDefault(); // 阻止默认提交行为

        const mawbCode = $('#inputMawbCode').val().trim();
        const pkgPrintCode = $('#inputPkgCode').val().trim();
        const packageType = $('#inputPackageType').val().trim();
        const parcelCode = $('#inputParcelCode').val().trim();
        const itemCode = $('#inputItemCode').val().trim();
        const quantity = $('#inputQuantity').val().trim();
        const packageNo = $('#inputPackageNo').val().trim(); // 新增字段

        let errorMsg = '';

        if (packageType === 'PARCEL') {
            // 包裹/运单类型：包裹号必填，提单号非必填
            if (!parcelCode) {
                errorMsg = '请填写包裹号';
            }
        } else if (packageType === 'ITEM') {
            // 散件类型：提单号必填，包裹号非必填，散件SKC号和数量必填
            if (!mawbCode) {
                errorMsg = '请填写提单号';
            } else if (!itemCode || !quantity) {
                errorMsg = '请填写完整的散件SKC号和数量';
            }
        }

        if (errorMsg) {
            swal("提示", errorMsg, "warning");
            return;
        }

        // 构造请求参数
        const postData = {
            mawbCode: mawbCode,
            pkgPrintCode: pkgPrintCode,
            packageType: packageType,
            eawbReference1: parcelCode,
            skuCode: itemCode,
            detentionQty: quantity,
            packageNo: packageNo // 提交运单号字段
        };

        // 发起 AJAX 请求
        $.ajax({
            url: '${ctx}/pkghold/saveRecord',
            type: 'POST',
            dataType: 'json',
            data: postData,
            success: function (response) {
                console.log(response);
                if (response && response.msgCode === "SUCCESS") {
                    swal({
                        title: "成功",
                        text: "信息已保存",
                        type: "success"
                    }, function () {
                        $('#inputModal').modal('hide');
                        $("#myTable").bootstrapTable('refresh');
                    });
                } else {
                    swal("失败", response.message || "保存失败，请重试", "error");
                }
            },
            error: function (xhr, status, error) {
                swal("错误", "网络请求失败：" + error, "error");
            }
        });
    });

    function typeFormater(value, row, index) {
        let val;
        switch (value) {
            case "P":
                val = "整包";
                break;
            case "I":
                val = "散件";
                break;
            default:
                val = "待处理";
                break;
        }
        return val;
        /*return new Date(value).Format("yyyy-MM-dd HH24:mm:ss");*/
    }

    function timeFormatter(value, row, index) {
        if (value == null || value == "") {
            return "-";
        }
        return dateFormat("YYYY-mm-dd HH:MM:SS", new Date(value))
        /*return new Date(value).Format("yyyy-MM-dd HH24:mm:ss");*/
    }

    function picFormater(value, row, index) {
        // console.log(row);
        if (row.imgUrl != null && row.imgUrl != "" && row.detentionType == "PENDING") {
            let imgList = row.imgUrl.split(";");
            return "<img src='" + imgList[0] + "' style='width:50px;height: 50px;'/> <button class='btn btn-primary' type='button' onclick='completeRecord(" + row.id + ",\"" + row.imgUrl + "\")'>回填</button>";
        } else {
            return "--";
        }

    }


    function indexFormatter(value, row, index) {
        var $table = $("#myTable");
        var page = $table.bootstrapTable("getOptions");
        return page.pageSize * (page.pageNumber - 1) + index + 1;
    }

    function dateFormat(fmt, date) {
        let ret;
        const opt = {
            "Y+": date.getFullYear().toString(),        // 年
            "m+": (date.getMonth() + 1).toString(),     // 月
            "d+": date.getDate().toString(),            // 日
            "H+": date.getHours().toString(),           // 时
            "M+": date.getMinutes().toString(),         // 分
            "S+": date.getSeconds().toString()          // 秒
            // 有其他格式化字符需求可以继续添加，必须转化成字符串
        };
        for (let k in opt) {
            ret = new RegExp("(" + k + ")").exec(fmt);
            if (ret) {
                fmt = fmt.replace(ret[1], (ret[1].length == 1) ? (opt[k]) : (opt[k].padStart(ret[1].length, "0")))
            }
            ;
        }
        ;
        return fmt;
    }

    function search() {
        /*console.log($("#myTable").bootstrapTable("getOptions"))*/
        var data = $("#searchForm").serialize();
        $("#myTable").bootstrapTable('destroy');
        $("#myTable").bootstrapTable({url: "/pkgreturn/data" + "?" + data});
    }

    function exportData() {
        var formData = $("#searchForm").serializeArray();
        var data = {};
        $.each(formData, function (index, field) {
            data[field.name] = field.value;
        });
        if (
            (!data.pkgCode || data.pkgCode.trim() === '') &&
            (!data.mawbCode || data.mawbCode.trim() === '') &&
            (!data.parcelCode || data.parcelCode.trim() === '') &&
            (!data.startDate || data.startDate.trim() === '' && !data.endDate || data.endDate.trim() === '')
        ) {
            // 提示错误
            swal({
                title: "请输入导出条件",
                text: "请填写提单号、大箱号、包裹号/运单号 或 时间范围",
                type: "error",
                confirmButtonText: "确定",
                closeOnConfirm: true
            });
            return;
        }
        window.location.href = "${ctx}/pkghold/exportData?" + $.param(data);
    }


    $(function () {
        /**
         * 时间格式设定
         */
        $('.form_date').datetimepicker({
            language: 'zh-CN',
            weekStart: 1,
            todayBtn: 1,
            autoclose: 1,
            todayHighlight: 1,
            startView: 2,
            minView: 2,
            clearBtn: true
        })

        // $(".i-checks").iCheck({checkboxClass: "icheckbox_square-green", radioClass: "iradio_square-green",});

        // 添加清空按钮点击事件
        $('.clear-btn').on('click', function () {
            $(this).prev('input').val('');
            $(this).parent().data('DateTimePicker').date(null);
        });
    })

</script>
</body>
<style>
#imagePreviewContainer {
    text-align: center;
    min-height: 100px;
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    align-items: center;
    gap: 10px;
    padding: 10px;
}

/* Viewer.js 相关样式 - 确保全屏显示和正常工作 */
.viewer-container {
    position: fixed !important;
    top: 0 !important;
    left: 0 !important;

    height: 100vh !important;
    z-index: 9999 !important;
    background: rgba(0, 0, 0, 0.9) !important;
    margin: 0 !important;
    padding: 0 !important;
    transform: none !important;
    overflow: hidden !important;
}

/* 确保viewer canvas正确定位，往左偏移 */
.viewer-canvas {
    position: absolute !important;
    top: 50% !important;
    left: 45% !important;
    transform: translate(-50%, -50%) !important;
}

/* 确保modal不会限制viewer */
.modal {
    overflow: visible !important;
}

.modal-dialog {
    overflow: visible !important;
}

.modal-content {
    overflow: visible !important;
}

/* 确保图片缩略图可点击 */
#imagePreviewContainer img {
    cursor: pointer !important;
}

/* 确保viewer工具栏显示在最上层并正确定位 */
.viewer-toolbar {
    position: absolute !important;
    top: 30px !important;
    right: 30px !important;
    z-index: 10000 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    border-radius: 4px !important;
    padding: 5px !important;
}

.viewer-navbar {
    position: absolute !important;
    bottom: 30px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    z-index: 10000 !important;
    background: rgba(0, 0, 0, 0.7) !important;
    border-radius: 4px !important;
    padding: 5px !important;
}

/* 确保viewer按钮可见 */
.viewer-button {
    background: rgba(255, 255, 255, 0.1) !important;
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: white !important;
    margin: 2px !important;
}

.viewer-button:hover {
    background: rgba(255, 255, 255, 0.2) !important;
}

.thumbnail {
    display: inline-block;
    border: 1px solid #ddd;
    padding: 4px;
    line-height: 1.42857143;
    border-radius: 4px;
    margin: 0; /* 移除margin，使用gap代替 */
}

.clear-btn {
    cursor: pointer;
}
</style>
