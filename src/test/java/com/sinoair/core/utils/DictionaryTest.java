package com.sinoair.core.utils;

import com.sinoair.ceop.domain.model.ExpressItalyOMPostcode;
import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.Test;

import java.sql.SQLOutput;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;

/**
 * Created by IntelliJ IDEA.
 *
 * @author: Wang<PERSON>
 * @date: 2019/5/8
 * @time: 17:45
 * @description:
 */
public class DictionaryTest extends CommonTestCase {

    @Test
    public void getItalyOMPartitionMap(){
        List<ExpressItalyOMPostcode> expressItalyOMPostcodes=Dictionary.getExpressItalySurfaceMailPostcodes();
        Map<String,String> italyOMlPartitionMap=Dictionary.getItalyOMPartitionMap();
        System.out.println(expressItalyOMPostcodes.size());
        for(Map.Entry<String,String> entry :italyOMlPartitionMap.entrySet()){ System.out.println(entry.getKey()+"  "+entry.getValue());
        }
    }



}