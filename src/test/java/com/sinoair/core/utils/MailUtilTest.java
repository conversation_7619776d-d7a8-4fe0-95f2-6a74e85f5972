package com.sinoair.core.utils;

import com.sinoair.ceop.testUtil.CommonTestCase;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import static org.junit.Assert.*;

/**
 * Created by ZhangMJ on 2016/7/9.
 */
public class MailUtilTest extends CommonTestCase {

    @Before
    public void setUp() throws Exception {

    }

    @After
    public void tearDown() throws Exception {

    }

    @Test
    public void postMailHtml() throws Exception {

    }

    @Test
    public void postMailUtilDefault() throws Exception {

    }

    @Test
    public void postMailUtilNoHtml() throws Exception {
        MailUtil.postMailUtilNoHtml("<EMAIL>", null, null, "ceshiceshi", "aaaa\n4444", null);

    }

    @Test
    public void postMailUtil() throws Exception {

    }
}