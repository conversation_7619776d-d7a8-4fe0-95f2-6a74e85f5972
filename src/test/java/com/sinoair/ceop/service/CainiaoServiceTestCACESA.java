package com.sinoair.ceop.service;

import com.sinoair.ceop.dao.EawbpreMapper;
import com.sinoair.ceop.testUtil.CommonTestCase;
import com.sinoair.core.utils.DateUtil;
import com.sinoair.core.utils.StringUtil;
import com.sinoair.core.utils.XMLUtil;
import org.dom4j.Document;
import org.dom4j.DocumentException;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;

import javax.annotation.Resource;
import java.util.Iterator;
import java.util.Random;

/**
 * Created by WangXX4 on 2016/6/14.
 */
public class CainiaoServiceTestCACESA extends CommonTestCase {
    @Resource
    CainiaoService cainiaoService;
    @Resource
    EawbpreMapper eawbpreMapper;


    @Before
    public void setUp() throws Exception {
    }

    @After
    public void tearDown() throws Exception {

    }


    /**
     * 哥伦比亚批量做数据
     *
     * @throws Exception
     */
    @Test
    public void batchCACESA() throws Exception {
//		--------------------------需要改的参数--begin-----------------------------------------------------------
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "ES";//目的国家
        String serviceType = "TRUNK_13169806";//线路  西班牙CACESA
        int[] ordersInPackageArray = {7, 8, 3};//袋子预报内装数

//		-------------------需要改的参数--end---------------------------------------------------------------

        String eawb_reference1Pre = "CACESA" + date;//参考号1 mailno  RB780569565CO
        String base_pkg_no = "CNCACESA" + date + dectCountry + "2";//大包号
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "CACESA";//邮编
        String cainiaoLPNO = "LP" + date + dectCountry;//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(4, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String cainiaoLPNO1 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction(combinateBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry, dectCountry,
                        cainiaoLPNO1, pkg_no, ordersInPackageArray[j]));
                System.out.println("eawb_reference1=====================================================" + eawb_reference1);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }




    private String combinateBatchXml(String servicetype, String mailNo, String postcode, String country,
                                     String city, String orderCode, String pkg_No, int ordersInPackage) {
        String producttionName = getOneProductionName();
        String productionCnName = producttionName.split("-")[0];
        String productionEnName = producttionName.split("-")[1];
        String xml =
                "<logisticsEventsRequest><logisticsEvent><eventHeader><eventType>LOGISTICS_BATCH_SEND</eventType><eventTime>2013-02-25 00:00:00</eventTime>" +
                        "<eventSource>taobao</eventSource>" +
                        "<eventTarget>" + servicetype + "</eventTarget>" +
                        "</eventHeader><eventBody><OrderInfos><product>" +
                        "<productNameCN>" + productionCnName + "</productNameCN>" +
                        "<productNameEN>" + productionEnName + "</productNameEN>" +
                        "<productQantity>" + randomMinMax(1, 5) + "</productQantity>" +
                        "<productCateCN/>" +
                        "<productCateEN>Cuddly cat Toy</productCateEN>" +
                        "<productId /><producingArea />" +
                        "<productWeight>500</productWeight>" +
                        "<productPrice>10</productPrice>" +
                        "</product>" +
                        "</OrderInfos><ecCompanyId>taobao</ecCompanyId><whCode>Tran_Store_904817</whCode><logisticsOrderId>MAM0001</logisticsOrderId><tradeId>3018196085</tradeId>" +
                        "<mailNo>" + mailNo + "</mailNo>" +
                        "<Rcountry>" + country + "</Rcountry>" +
                        "<Rprovince>Barcelona</Rprovince><Rcity>" + city + "</Rcity><Remail><EMAIL></Remail><Raddress>Avda. Jose Pedro Alessandri 605 Depto. 801, Nunoa</Raddress>" +
                        "<Rpostcode>" + postcode + "</Rpostcode><Rname>Sandra Zambrno</Rname><Rphone>*********</Rphone><Sname>tee2</Sname><SwangwangId>aliqatest01</SwangwangId>" +
                        "<Sprovince>ZheJiang</Sprovince><Scity>HangZhou</Scity><Saddress>shangtang</Saddress><Sphone>28433934013</Sphone><Spostcode>941814</Spostcode>" +
                        "<channel>HK</channel><Itotleweight>" + randomMinMax(50, 500) + "</Itotleweight><Itotlevalue>" + randomMinMax(50, 2000) + "</Itotlevalue><totleweight>2168</totleweight>" +
                        "<country>CN</country>" +
                        "<mailKind>1</mailKind><mailClass>L</mailClass><batchNo>LP00035917668244</batchNo><mailType>SUMAITONG</mailType><faceType>2</faceType><undeliveryOption>2</undeliveryOption><hasBattery>false</hasBattery><pickUpAddress/>" +
                        "<packageCode>" + pkg_No + "</packageCode>" +
                        "<orderCode>" + orderCode + "</orderCode>" +
                        "<packageWeight>" + randomMinMax(4000, 75000) + "</packageWeight>" +
                        "<ordersInPackage>" + ordersInPackage + "</ordersInPackage></eventBody></logisticsEvent></logisticsEventsRequest>";

        return xml;
    }


    private String getOneProductionName() {
        String[] str = new String[]{"棉外套-coat", "短裙-philabeg", "帽子-hat", "鞋子-shoes", "衬衫-shirt", "手机-phone", "袜子-stockings", "缎带-Ribbons", "墨水盒-Ink Cartridges", "童装套装-Children's Sets", "耳机-Headphones"};
        return getRandomString(str);
    }

    private String getRandomString(String[] arrs) {
        return arrs[getRandom0_n(arrs.length - 1)];
    }

    /**
     * 生成0-num的数据
     *
     * @param num
     * @return
     */
    private int getRandom0_n(int num) {
        Random rd = new Random();
        int n = rd.nextInt(num + 1);
        return n;
    }

    private int randomMinMax(int min, int max) {
        Random random = new Random();
        int s = random.nextInt(max) % (max - min + 1) + min;
        return s;
    }

    @Test
    public void testXml() throws DocumentException {
        String xml = "<request>\n" +
                "    <logisticsOrderCode>LP20160920141206</logisticsOrderCode>\n" +
                "    <sender>\n" +
                "        <imID>BifrostTester</imID>\n" +
                "        <name>BifrostTester</name>\n" +
                "        <address>\n" +
                "            <country>CN</country>\n" +
                "            <detailAddress>some detailed address</detailAddress>\n" +
                "        </address>\n" +
                "    </sender>\n" +
                "    <receiver>\n" +
                "        <imID>receiverTester</imID>\n" +
                "        <name>receiverTester</name>\n" +
                "        <zipCode>123123</zipCode>\n" +
                "        <address>\n" +
                "            <country>PE</country>\n" +
                "            <detailAddress>detailed receiver address</detailAddress>\n" +
                "        </address>\n" +
                "    </receiver>\n" +
                "    <parcel>\n" +
                "        <weight>1000</weight>\n" +
                "        <weightUnit>g</weightUnit>\n" +
                "        <suggestedWeight>1000</suggestedWeight>\n" +
                "        <price>40</price>\n" +
                "        <priceUnit>CENT</priceUnit>\n" +
                "        <goodsList>\n" +
                "            <goods>\n" +
                "                <name>name</name>\n" +
                "                <cnName>商品</cnName>\n" +
                "                <categoryFeature>00</categoryFeature>\n" +
                "                <price>100</price>\n" +
                "                <priceUnit>CENT</priceUnit>\n" +
                "                <priceCurrency>USD</priceCurrency>\n" +
                "                <quantity>10</quantity>\n" +
                "                <url>some url</url>\n" +
                "            </goods>\n" +
                "        </goodsList>\n" +
                "    </parcel>\n" +
                "    <trackingNumber>1120160920141207</trackingNumber>\n" +
                "    <bizType>1</bizType>\n" +
                "</request>";
        Document doc = DocumentHelper.parseText(xml);
        Element rootElement = doc.getRootElement();
        String logisticsOrderCode = XMLUtil.getElementContent(rootElement, "logisticsOrderCode");
        String trackingNumber = XMLUtil.getElementContent(rootElement, "trackingNumber");
        String bizType = XMLUtil.getElementContent(rootElement, "bizType");
        System.out.println(logisticsOrderCode);
        System.out.println(trackingNumber);
        System.out.println(bizType);

        Iterator senderIterator = rootElement.elementIterator("sender");
        if (senderIterator.hasNext()) {
            Element senderElement = (Element) senderIterator.next();
            String imID = XMLUtil.getElementContent(senderElement, "imID");
            String name = XMLUtil.getElementContent(senderElement, "name");
            System.out.println(imID+"--"+name);
            Iterator addressIterator = senderElement.elementIterator("address");
            if (addressIterator.hasNext()) {
                Element addressElement = (Element) addressIterator.next();
                String country = XMLUtil.getElementContent(addressElement, "country");
                String detailAddress = XMLUtil.getElementContent(addressElement, "detailAddress");
                System.out.println(country+"--"+detailAddress);
            }

        }
    }

    @Test
    public void testReportTransaction4Gsdp() {
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>LP20170419ZMJCACESA001</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>aliqatest07</imID>\n" +
                "\t\t<name>hhhj</name>\n" +
                "\t\t<phone>+34</phone>\n" +
                "\t\t<mobile>55566</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>01002</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>ES</country>\n" +
                "\t\t\t<province>Alava</province>\n" +
                "\t\t\t<city>Vitoria-Gasteiz</city>\n" +
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>bhjef hhewt</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>500</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>500</suggestedWeight>\n" +
                "\t\t<price>100</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<productID>32800579945</productID>\n" +
                "\t\t\t\t<name>Arm Warmers</name>\n" +
                "\t\t\t\t<cnName>手臂套</cnName>\n" +
                "\t\t\t\t<categoryName>Arm Warmers</categoryName>\n" +
                "\t\t\t\t<categoryCNName>手臂套</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>100</price>\n" +
                "\t\t\t\t<itemPrice>0</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>100</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url>http://www.aliexpress.com/item//32800579945.html</url>\n" +
                "\t\t\t\t<productCategory>Apparel &amp; Accessories|Apparel Accessories|Arm Warmers</productCategory>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>100</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<undeliverableOption>1</undeliverableOption>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>0028LP20170419ZMJCACESA001</trackingNumber>\n" +
                "\t<preCPResCode>Tran_Store_11993143</preCPResCode>\n" +
                "\t<currentCPResCode>TRUNK_13169806</currentCPResCode>\n" +
                "\t<nextCPResCode>GATE_13169919</nextCPResCode>\n" +
                "\t<interCPResCode>GATE_13169919</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_STANDARD</bizType>\n" +
                "</request>" +
                "";
        String result = cainiaoService.reportTransaction4Gsdp(xml, "TRUNK_13169806");
        System.out.printf(result);

    }
    @Test
    public void testBatchTransaction4Gsdp() {
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>LP20170419ZMJCACESA001</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>aliqatest07</imID>\n" +
                "\t\t<name>hhhj</name>\n" +
                "\t\t<phone>+34</phone>\n" +
                "\t\t<mobile>55566</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>01002</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>ES</country>\n" +
                "\t\t\t<province>Alava</province>\n" +
                "\t\t\t<city>Vitoria-Gasteiz</city>\n" +
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>bhjef hhewt</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>1</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>500</suggestedWeight>\n" +
                "\t\t<price>100</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<bigBagID>YW0028LP20170419ZMJCACESA001</bigBagID>\n" +
                "\t\t<parcelQuantity>2</parcelQuantity>\n" +
                "\t\t<bigBagWeight>1999</bigBagWeight>\n" +
                "\t\t<bigBagWeightUnit>g</bigBagWeightUnit>\n" +
                "\t\t<asnID>AEtest</asnID>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<productID>32800579945</productID>\n" +
                "\t\t\t\t<name>Arm Warmers</name>\n" +
                "\t\t\t\t<cnName>手臂套</cnName>\n" +
                "\t\t\t\t<categoryName>Arm Warmers</categoryName>\n" +
                "\t\t\t\t<categoryCNName>手臂套</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>100</price>\n" +
                "\t\t\t\t<itemPrice>0</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>100</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url>http://www.aliexpress.com/item//32800579945.html</url>\n" +
                "\t\t\t\t<productCategory>Apparel &amp; Accessories|Apparel Accessories|Arm Warmers</productCategory>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>100</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<undeliverableOption>1</undeliverableOption>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>0028LP20170419ZMJCACESA001</trackingNumber>\n" +
                "\t<preCPResCode>Tran_Store_11993143</preCPResCode>\n" +
                "\t<currentCPResCode>TRUNK_13169806</currentCPResCode>\n" +
                "\t<nextCPResCode>GATE_13169919</nextCPResCode>\n" +
                "\t<interCPResCode>GATE_13169919</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_STANDARD</bizType>\n" +
                "</request>" +
                "";
        String result = cainiaoService.batchTransaction4Gsdp(xml, "TRUNK_13169806");
        System.out.printf(result);

    }

    @Test
    public void testBatchGSDP4GsdpEle() throws Exception{
        String date = DateUtil.getDateStr(DateUtil.yyyyMMddHHmmss);//日期唯一号
        String dectCountry = "ES";//目的国家
        String serviceType = "TRUNK_13195978";//线路  CACESA带电
        int[] ordersInPackageArray = {4, 3,7};//袋子预报内装数

        String eawb_reference1Pre = "0028" + date.substring(4);//参考号1 mailno
        String base_pkg_no = "YW" + date + dectCountry;//大包号
        System.out.println("pkg_no = " + base_pkg_no);
        String postcode = "01002";//邮编
        String cainiaoLPNO =  "LP" + date + dectCountry+"CCSELE";//参考号2  菜鸟lp号
        for (int j = 0; j < ordersInPackageArray.length; j++) {
            String pkg_no = base_pkg_no + StringUtil.getIntFormString(3, j);
            for (int i = 0; i < ordersInPackageArray[j]; i++) {
                String eawb_reference1 = eawb_reference1Pre + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i) + dectCountry;
                String eawb_reference2 = cainiaoLPNO + StringUtil.getIntFormString(3, j) + StringUtil.getIntFormString(3, i);
                cainiaoService.batchTransaction4Gsdp(getGSDPBatchXml(serviceType,
                        eawb_reference1,
                        postcode,
                        dectCountry,"Vitoria-Gasteiz" ,
                        eawb_reference2, pkg_no, ordersInPackageArray[j]),serviceType);
            }
            System.out.println("pkg_no--------------------------------------------------------------------- = " + pkg_no);
        }

    }


    public  String getGSDPReportXml(String eawb_reference2,String eawb_reference1,String serviceType){
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>"+eawb_reference2+"</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>aliqatest07</imID>\n" +
                "\t\t<name>hhhj</name>\n" +
                "\t\t<phone>+34</phone>\n" +
                "\t\t<mobile>55566</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>01002</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>ES</country>\n" +
                "\t\t\t<province>Alava</province>\n" +
                "\t\t\t<city>Vitoria-Gasteiz</city>\n" +
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>bhjef hhewt</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>500</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>500</suggestedWeight>\n" +
                "\t\t<price>100</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<productID>32800579945</productID>\n" +
                "\t\t\t\t<name>Arm Warmers</name>\n" +
                "\t\t\t\t<cnName>手臂套</cnName>\n" +
                "\t\t\t\t<categoryName>Arm Warmers</categoryName>\n" +
                "\t\t\t\t<categoryCNName>手臂套</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>100</price>\n" +
                "\t\t\t\t<itemPrice>0</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>100</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url>http://www.aliexpress.com/item//32800579945.html</url>\n" +
                "\t\t\t\t<productCategory>Apparel &amp; Accessories|Apparel Accessories|Arm Warmers</productCategory>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>100</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<undeliverableOption>1</undeliverableOption>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>"+eawb_reference1+"</trackingNumber>\n" +
                "\t<preCPResCode>Tran_Store_XXX</preCPResCode>\n" +
                "\t<currentCPResCode>"+serviceType+"</currentCPResCode>\n" +
                "\t<nextCPResCode>GATE_XXX</nextCPResCode>\n" +
                "\t<interCPResCode>GATE_XXX</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_STANDARD</bizType>\n" +
                "</request>" +
                "";
        return  xml;
    }



    public  String getGSDPBatchXml(String servicetype, String eawb_reference1, String postcode, String country,
                                   String city, String eawb_reference2, String pkg_No, int ordersInPackage){
        int price=randomMinMax(50, 2000);
        String producttionName = getOneProductionName();
        String productionCnName = producttionName.split("-")[0];
        String productionEnName = producttionName.split("-")[1];
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>"+eawb_reference2+"</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>aliqatest07</imID>\n" +
                "\t\t<name>hhhj</name>\n" +
                "\t\t<phone>+34</phone>\n" +
                "\t\t<mobile>55566</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>"+postcode+"</zipCode>\n" +//01002
                "\t\t<address>\n" +
                "\t\t\t<country>"+country+"</country>\n" +
                "\t\t\t<province>Alava</province>\n" +
                "\t\t\t<city>"+city+"</city>\n" +//Vitoria-Gasteiz
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>bhjef hhewt</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>"+randomMinMax(50, 500)+"</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>500</suggestedWeight>\n" +
                "\t\t<price>100</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<bigBagID>"+pkg_No+"</bigBagID>\n" +
                "\t\t<parcelQuantity>"+ordersInPackage+"</parcelQuantity>\n" +
                "\t\t<bigBagWeight>" + randomMinMax(4000, 75000) + "</bigBagWeight>\n" +
                "\t\t<bigBagWeightUnit>g</bigBagWeightUnit>\n" +
                "\t\t<asnID>AEtest</asnID>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<productID>32800579945</productID>\n" +
                "\t\t\t\t<name>"+productionEnName+"</name>\n" +
                "\t\t\t\t<cnName>"+productionCnName+"</cnName>\n" +
                "\t\t\t\t<categoryName>"+productionEnName+"</categoryName>\n" +
                "\t\t\t\t<categoryCNName>"+productionCnName+"</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>"+price+"</price>\n" +
                "\t\t\t\t<itemPrice>"+price+"</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>"+price+"</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url>http://www.aliexpress.com/item//32800579945.html</url>\n" +
                "\t\t\t\t<productCategory>Apparel &amp; Accessories|Apparel Accessories|Arm Warmers</productCategory>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>"+price+"</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>aliqatest01</imID>\n" +
                "\t\t<name>test</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<mobile>***********</mobile>\n" +
                "\t\t<undeliverableOption>1</undeliverableOption>\n" +
                "\t\t<zipCode>710049</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>bei jing</province>\n" +
                "\t\t\t<city>bei jing shi</city>\n" +
                "\t\t\t<district>chao yang qu</district>\n" +
                "\t\t\t<detailAddress>ba li zhuang jie dao~~~tset teat teata</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>"+eawb_reference1+"</trackingNumber>\n" +
                "\t<preCPResCode>Tran_Store_XXX</preCPResCode>\n" +
                "\t<currentCPResCode>"+servicetype+"</currentCPResCode>\n" +
                "\t<nextCPResCode>GATE_XXX</nextCPResCode>\n" +
                "\t<interCPResCode>GATE_XXX</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_XXX</bizType>\n" +
                "</request>" +
                "";
        return  xml;
    }


    @Test
    public void testOMReportTransaction4Gsdp() {
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>LP00068413827024</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>cn1510329823</imID>\n" +
                "\t\t<name>LiLi Li</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<zipCode>510170</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>guang dong sheng</province>\n" +
                "\t\t\t<city>guang zhou shi</city>\n" +
                "\t\t\t<district>bai yun qu</district>\n" +
                "\t\t\t<detailAddress>jin sha jie dao~~~Room 207 Tiansheng,Xi Jiu St #17</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>fr1131847596utxg</imID>\n" +
                "\t\t<name>belkaim</name>\n" +
                "\t\t<phone>33-</phone>\n" +
                "\t\t<mobile>0652070623</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>13008</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>FR</country>\n" +
                "\t\t\t<province>paca</province>\n" +
                "\t\t\t<city>Marseille</city>\n" +
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>33 bd baptistin cayol bat j</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>100</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>100</suggestedWeight>\n" +
                "\t\t<price>900</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<name>Watch</name>\n" +
                "\t\t\t\t<cnName>手表</cnName>\n" +
                "\t\t\t\t<categoryName>Watch</categoryName>\n" +
                "\t\t\t\t<categoryCNName>手表</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>900</price>\n" +
                "\t\t\t\t<itemPrice>0</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>900</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url/>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>900</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>cn1510329823</imID>\n" +
                "\t\t<name>李莉莉</name>\n" +
                "\t\t<phone>02088902254</phone>\n" +
                "\t\t<undeliverableOption>2</undeliverableOption>\n" +
                "\t\t<zipCode>510170</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>中国</country>\n" +
                "\t\t\t<province>广东省</province>\n" +
                "\t\t\t<city>广州市</city>\n" +
                "\t\t\t<district>白云区</district>\n" +
                "\t\t\t<detailAddress>金沙街道~~~西就街17号天晟大厦207房</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>RS910372607GB</trackingNumber>\n" +
                "\t<preCPResCode>FPXGZ</preCPResCode>\n" +
                "\t<currentCPResCode>DISTRIBUTOR_11358681</currentCPResCode>\n" +
                "\t<nextCPResCode>DISTRIBUTOR_11358681</nextCPResCode>\n" +
                "\t<interCPResCode>DISTRIBUTOR_11358681</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_STANDARD</bizType>\n" +
                "</request>" +
                "";
        String result = cainiaoService.reportTransaction4Gsdp(xml, "DISTRIBUTOR_11358681");
        System.out.printf(result);

    }
    @Test
    public void testOMBatchTransaction() {
        String xml = "<logisticsEventsRequest>\n" +
                "\t<logisticsEvent>\n" +
                "\t\t<eventHeader>\n" +
                "\t\t\t<eventType>LOGISTICS_BATCH_SEND</eventType>\n" +
                "\t\t\t<eventTime>2017-04-10 17:31:50</eventTime>\n" +
                "\t\t\t<eventSource>taobao</eventSource>\n" +
                "\t\t\t<eventTarget>DISTRIBUTOR_12706323</eventTarget>\n" +
                "\t\t</eventHeader>\n" +
                "\t\t<eventBody>\n" +
                "\t\t\t<OrderInfos>\n" +
                "\t\t\t\t<product>\n" +
                "\t\t\t\t\t<productNameCN>手表</productNameCN>\n" +
                "\t\t\t\t\t<productNameEN>Watch</productNameEN>\n" +
                "\t\t\t\t\t<productQantity>1</productQantity>\n" +
                "\t\t\t\t\t<productCateCN>手表</productCateCN>\n" +
                "\t\t\t\t\t<productCateEN>Watch</productCateEN>\n" +
                "\t\t\t\t\t<producingArea/>\n" +
                "\t\t\t\t\t<productWeight>100</productWeight>\n" +
                "\t\t\t\t\t<productPrice>900</productPrice>\n" +
                "\t\t\t\t\t<extendFields>customCode=</extendFields>\n" +
                "\t\t\t\t</product>\n" +
                "\t\t\t</OrderInfos>\n" +
                "\t\t\t<ecCompanyId>taobao</ecCompanyId>\n" +
                "\t\t\t<serviceItemId>5000000011398</serviceItemId>\n" +
                "\t\t\t<bizCode>AE_4PL_STANDARD</bizCode>\n" +
                "\t\t\t<whCode>FPXGZ</whCode>\n" +
                "\t\t\t<logisticsOrderId>68413827024</logisticsOrderId>\n" +
                "\t\t\t<tradeId>3546437209</tradeId>\n" +
                "\t\t\t<mailNo>RS910372607GB</mailNo>\n" +
                "\t\t\t<Rcountry>FR</Rcountry>\n" +
                "\t\t\t<Rprovince>paca</Rprovince>\n" +
                "\t\t\t<Rcity>Marseille</Rcity>\n" +
                "\t\t\t<Remail><EMAIL></Remail>\n" +
                "\t\t\t<Raddress>33 bd baptistin cayol bat j</Raddress>\n" +
                "\t\t\t<Rpostcode>13008</Rpostcode>\n" +
                "\t\t\t<Rname>belkaim</Rname>\n" +
                "\t\t\t<Rphone>0652070623</Rphone>\n" +
                "\t\t\t<Sname>LiLi Li</Sname>\n" +
                "\t\t\t<SwangwangId>cn1510329823</SwangwangId>\n" +
                "\t\t\t<Sprovince>guang dong sheng</Sprovince>\n" +
                "\t\t\t<Scity>guang zhou shi</Scity>\n" +
                "\t\t\t<Saddress>jin sha jie dao~~~Room 207 Tiansheng,Xi Jiu St #17</Saddress>\n" +
                "\t\t\t<Sphone>***********</Sphone>\n" +
                "\t\t\t<Spostcode>510170</Spostcode>\n" +
                "\t\t\t<SstoreName>Watch2us Store</SstoreName>\n" +
                "\t\t\t<channel>HK</channel>\n" +
                "\t\t\t<Itotleweight>131</Itotleweight>\n" +
                "\t\t\t<Itotlevalue>900</Itotlevalue>\n" +
                "\t\t\t<totleweight>131</totleweight>\n" +
                "\t\t\t<country>CN</country>\n" +
                "\t\t\t<mailKind>1</mailKind>\n" +
                "\t\t\t<mailClass>L</mailClass>\n" +
                "\t\t\t<batchNo>XE060001704100101992</batchNo>\n" +
                "\t\t\t<mailType>SUMAITONG</mailType>\n" +
                "\t\t\t<faceType>2</faceType>\n" +
                "\t\t\t<undeliveryOption>2</undeliveryOption>\n" +
                "\t\t\t<hasBattery>false</hasBattery>\n" +
                "\t\t\t<refunderContact>\n" +
                "\t\t\t\t<wangwangId>cn1510329823</wangwangId>\n" +
                "\t\t\t\t<name>李莉莉</name>\n" +
                "\t\t\t\t<phone>02088902254</phone>\n" +
                "\t\t\t\t<email/>\n" +
                "\t\t\t\t<country>中国</country>\n" +
                "\t\t\t\t<province>广东省</province>\n" +
                "\t\t\t\t<city>广州市</city>\n" +
                "\t\t\t\t<street>金沙街道~~~西就街17号天晟大厦207房</street>\n" +
                "\t\t\t\t<zipCode>510170</zipCode>\n" +
                "\t\t\t\t<district>白云区</district>\n" +
                "\t\t\t</refunderContact>\n" +
                "\t\t\t<pickUpAddress>中国广东省广州市白云区黄石街道鹤龙一路（皮博成后面）18号凯升大夏B栋1楼</pickUpAddress>\n" +
                "\t\t\t<packageCode>XE060001704100101992</packageCode>\n" +
                "\t\t\t<orderCode>LP00068413827024</orderCode>\n" +
                "\t\t\t<packageWeight>5259</packageWeight>\n" +
                "\t\t\t<ordersInPackage>30</ordersInPackage>\n" +
                "\t\t</eventBody>\n" +
                "\t</logisticsEvent>\n" +
                "</logisticsEventsRequest>";
        String result = cainiaoService.batchTransaction(xml);
        System.out.printf(result);

    }

    @Test
    public void testCHILEReportTransaction4Gsdp() {
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>LP00072104160114</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>cn1001475998</imID>\n" +
                "\t\t<name>Max Black</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<zipCode>518114</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>guang dong sheng</province>\n" +
                "\t\t\t<city>shen zhen shi</city>\n" +
                "\t\t\t<district>long gang qu</district>\n" +
                "\t\t\t<detailAddress>nan wan jie dao~~~oom B1706, Baolinghuayuan North</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>cl1125961049ptrt</imID>\n" +
                "\t\t<name>Gonzalo Nahmias</name>\n" +
                "\t\t<phone>56-2-22254835</phone>\n" +
                "\t\t<mobile>56994696177</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>Ninguno</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>CL</country>\n" +
                "\t\t\t<province>Metropolitana</province>\n" +
                "\t\t\t<city>Santiago</city>\n" +
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>Felipe II 4343 - Comuna Las Condes Departamento 701</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>10</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>10</suggestedWeight>\n" +
                "\t\t<price>200</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<name>Cable</name>\n" +
                "\t\t\t\t<cnName>数据线</cnName>\n" +
                "\t\t\t\t<categoryName>Cable</categoryName>\n" +
                "\t\t\t\t<categoryCNName>数据线</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>200</price>\n" +
                "\t\t\t\t<itemPrice>0</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>200</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url/>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>200</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>cn1001475998</imID>\n" +
                "\t\t<name>金坚桫</name>\n" +
                "\t\t<phone>18620389782</phone>\n" +
                "\t\t<undeliverableOption>2</undeliverableOption>\n" +
                "\t\t<zipCode>51800</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>中国</country>\n" +
                "\t\t\t<province>广东省</province>\n" +
                "\t\t\t<city>深圳市</city>\n" +
                "\t\t\t<district>龙岗区</district>\n" +
                "\t\t\t<detailAddress>南湾街道~~~宝岭花园北区B1706</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>12800000007000201297901001</trackingNumber>\n" +
                "\t<preCPResCode>FPXSS</preCPResCode>\n" +
                "\t<currentCPResCode>DISTRIBUTOR_8782834</currentCPResCode>\n" +
                "\t<nextCPResCode>DISTRIBUTOR_8782834</nextCPResCode>\n" +
                "\t<interCPResCode>DISTRIBUTOR_8782834</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_STANDARD</bizType>\n" +
                "</request>" ;
        String result = cainiaoService.reportTransaction4Gsdp(xml, "DISTRIBUTOR_8782834");
        System.out.printf(result);

    }
    @Test
    public void testCHILEBatchTransaction4GSDP() {
        String xml = "<request>\n" +
                "\t<logisticsOrderCode>LP00072104160114</logisticsOrderCode>\n" +
                "\t<sender>\n" +
                "\t\t<imID>cn1001475998</imID>\n" +
                "\t\t<name>Max Black</name>\n" +
                "\t\t<phone>***********</phone>\n" +
                "\t\t<zipCode>518114</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>China</country>\n" +
                "\t\t\t<province>guang dong sheng</province>\n" +
                "\t\t\t<city>shen zhen shi</city>\n" +
                "\t\t\t<district>long gang qu</district>\n" +
                "\t\t\t<detailAddress>nan wan jie dao~~~oom B1706, Baolinghuayuan North</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</sender>\n" +
                "\t<receiver>\n" +
                "\t\t<imID>cl1125961049ptrt</imID>\n" +
                "\t\t<name>Gonzalo Nahmias</name>\n" +
                "\t\t<phone>56-2-22254835</phone>\n" +
                "\t\t<mobile>56994696177</mobile>\n" +
                "\t\t<email><EMAIL></email>\n" +
                "\t\t<zipCode>Ninguno</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>CL</country>\n" +
                "\t\t\t<province>Metropolitana</province>\n" +
                "\t\t\t<city>Santiago</city>\n" +
                "\t\t\t<district/>\n" +
                "\t\t\t<detailAddress>Felipe II 4343 - Comuna Las Condes Departamento 701</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</receiver>\n" +
                "\t<parcel>\n" +
                "\t\t<weight>210</weight>\n" +
                "\t\t<weightUnit>g</weightUnit>\n" +
                "\t\t<suggestedWeight>10</suggestedWeight>\n" +
                "\t\t<price>200</price>\n" +
                "\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t<bigBagID>XA01CL01704190401381</bigBagID>\n" +
                "\t\t<parcelQuantity>43</parcelQuantity>\n" +
                "\t\t<bigBagWeight>11221</bigBagWeight>\n" +
                "\t\t<bigBagWeightUnit>g</bigBagWeightUnit>\n" +
                "\t\t<asnID>XA01CL01704190401381</asnID>\n" +
                "\t\t<goodsList>\n" +
                "\t\t\t<goods>\n" +
                "\t\t\t\t<name>Cable</name>\n" +
                "\t\t\t\t<cnName>数据线</cnName>\n" +
                "\t\t\t\t<categoryName>Cable</categoryName>\n" +
                "\t\t\t\t<categoryCNName>数据线</categoryCNName>\n" +
                "\t\t\t\t<categoryFeature>00</categoryFeature>\n" +
                "\t\t\t\t<price>200</price>\n" +
                "\t\t\t\t<itemPrice>0</itemPrice>\n" +
                "\t\t\t\t<priceUnit>CENT</priceUnit>\n" +
                "\t\t\t\t<priceCurrency>USD</priceCurrency>\n" +
                "\t\t\t\t<declarePrice>200</declarePrice>\n" +
                "\t\t\t\t<quantity>1</quantity>\n" +
                "\t\t\t\t<url/>\n" +
                "\t\t\t</goods>\n" +
                "\t\t</goodsList>\n" +
                "\t</parcel>\n" +
                "\t<customs>\n" +
                "\t\t<declarePriceTotal>200</declarePriceTotal>\n" +
                "\t</customs>\n" +
                "\t<returnParcel>\n" +
                "\t\t<imID>cn1001475998</imID>\n" +
                "\t\t<name>金坚桫</name>\n" +
                "\t\t<phone>18620389782</phone>\n" +
                "\t\t<undeliverableOption>2</undeliverableOption>\n" +
                "\t\t<zipCode>51800</zipCode>\n" +
                "\t\t<address>\n" +
                "\t\t\t<country>中国</country>\n" +
                "\t\t\t<province>广东省</province>\n" +
                "\t\t\t<city>深圳市</city>\n" +
                "\t\t\t<district>龙岗区</district>\n" +
                "\t\t\t<detailAddress>南湾街道~~~宝岭花园北区B1706</detailAddress>\n" +
                "\t\t</address>\n" +
                "\t</returnParcel>\n" +
                "\t<trackingNumber>12800000007000201297901001</trackingNumber>\n" +
                "\t<preCPResCode>FPXSS</preCPResCode>\n" +
                "\t<currentCPResCode>DISTRIBUTOR_8782834</currentCPResCode>\n" +
                "\t<nextCPResCode>DISTRIBUTOR_8782834</nextCPResCode>\n" +
                "\t<interCPResCode>DISTRIBUTOR_8782834</interCPResCode>\n" +
                "\t<routingTrial>1</routingTrial>\n" +
                "\t<bizType>AE_4PL_STANDARD</bizType>\n" +
                "</request>";
        String result = cainiaoService.batchTransaction4Gsdp(xml, "DISTRIBUTOR_8782834");
        System.out.printf(result);

    }

    @Test
    public void testCHILEReportTransaction() {
        String xml = "<logisticsEventsRequest>\n" +
                "\t<logisticsEvent>\n" +
                "\t\t<eventHeader>\n" +
                "\t\t\t<eventType>LOGISTICS_REPORT_SEND</eventType>\n" +
                "\t\t\t<eventTime>2017-04-06 09:08:51</eventTime>\n" +
                "\t\t\t<eventSource>taobao</eventSource>\n" +
                "\t\t\t<eventTarget>DISTRIBUTOR_8782834</eventTarget>\n" +
                "\t\t</eventHeader>\n" +
                "\t\t<eventBody>\n" +
                "\t\t\t<OrderInfos>\n" +
                "\t\t\t\t<product>\n" +
                "\t\t\t\t\t<productNameCN>手机壳</productNameCN>\n" +
                "\t\t\t\t\t<productNameEN>Phone case</productNameEN>\n" +
                "\t\t\t\t\t<productQantity>1</productQantity>\n" +
                "\t\t\t\t\t<productCateCN>手机壳</productCateCN>\n" +
                "\t\t\t\t\t<productCateEN>Phone case</productCateEN>\n" +
                "\t\t\t\t\t<producingArea/>\n" +
                "\t\t\t\t\t<productWeight>100</productWeight>\n" +
                "\t\t\t\t\t<productPrice>200</productPrice>\n" +
                "\t\t\t\t\t<extendFields>customCode=</extendFields>\n" +
                "\t\t\t\t</product>\n" +
                "\t\t\t</OrderInfos>\n" +
                "\t\t\t<ecCompanyId>taobao</ecCompanyId>\n" +
                "\t\t\t<serviceItemId>5000000011398</serviceItemId>\n" +
                "\t\t\t<bizCode>AE_4PL_STANDARD</bizCode>\n" +
                "\t\t\t<whCode>FPXSS</whCode>\n" +
                "\t\t\t<logisticsOrderId>72048565015</logisticsOrderId>\n" +
                "\t\t\t<tradeId>3547662765</tradeId>\n" +
                "\t\t\t<mailNo>12883402587000201289843001</mailNo>\n" +
                "\t\t\t<Rcountry>CL</Rcountry>\n" +
                "\t\t\t<Rprovince>Santiago</Rprovince>\n" +
                "\t\t\t<Rcity>Santiago</Rcity>\n" +
                "\t\t\t<Remail><EMAIL></Remail>\n" +
                "\t\t\t<Raddress>Amunategui 630 Dpto 910</Raddress>\n" +
                "\t\t\t<Rpostcode>8340258</Rpostcode>\n" +
                "\t\t\t<Rname>Cristian Fuentes Viera</Rname>\n" +
                "\t\t\t<Rphone>56-9-94373172</Rphone>\n" +
                "\t\t\t<Sname>Selina</Sname>\n" +
                "\t\t\t<Sprovince>guang dong sheng</Sprovince>\n" +
                "\t\t\t<Scity>shen zhen shi</Scity>\n" +
                "\t\t\t<Saddress>~~~Room 933 , Dongming Building Minzhi Street</Saddress>\n" +
                "\t\t\t<Sphone>13424429337</Sphone>\n" +
                "\t\t\t<Spostcode>518100</Spostcode>\n" +
                "\t\t\t<SstoreName>Rock - Store</SstoreName>\n" +
                "\t\t\t<channel>HK</channel>\n" +
                "\t\t\t<Itotleweight>100</Itotleweight>\n" +
                "\t\t\t<Itotlevalue>200</Itotlevalue>\n" +
                "\t\t\t<totleweight>100</totleweight>\n" +
                "\t\t\t<country>CN</country>\n" +
                "\t\t\t<mailKind>1</mailKind>\n" +
                "\t\t\t<mailClass>L</mailClass>\n" +
                "\t\t\t<mailType>SUMAITONG</mailType>\n" +
                "\t\t\t<faceType>1</faceType>\n" +
                "\t\t\t<undeliveryOption>1</undeliveryOption>\n" +
                "\t\t\t<refunderContact>\n" +
                "\t\t\t\t<wangwangId>cn1512326056</wangwangId>\n" +
                "\t\t\t\t<name>Selina</name>\n" +
                "\t\t\t\t<phone>13424429337</phone>\n" +
                "\t\t\t\t<email/>\n" +
                "\t\t\t\t<country>China</country>\n" +
                "\t\t\t\t<province>guang dong sheng</province>\n" +
                "\t\t\t\t<city>shen zhen shi</city>\n" +
                "\t\t\t\t<street>~~~Room 933 , Dongming Building Minzhi Street</street>\n" +
                "\t\t\t\t<zipCode>518100</zipCode>\n" +
                "\t\t\t\t<district>long hua xin qu</district>\n" +
                "\t\t\t</refunderContact>\n" +
                "\t\t\t<hasBattery>false</hasBattery>\n" +
                "\t\t\t<pickUpAddress>中国广东省深圳市罗湖区清水河街道详细地址</pickUpAddress>\n" +
                "\t\t\t<orderCode>LP00072048565015</orderCode>\n" +
                "\t\t</eventBody>\n" +
                "\t</logisticsEvent>\n" +
                "</logisticsEventsRequest>" ;
        String result = cainiaoService.reportTransaction(xml);
        System.out.printf(result);

    }
    @Test
    public void testCHILEBatchTransaction() {
        String xml = "<logisticsEventsRequest>\n" +
                "\t<logisticsEvent>\n" +
                "\t\t<eventHeader>\n" +
                "\t\t\t<eventType>LOGISTICS_BATCH_SEND</eventType>\n" +
                "\t\t\t<eventTime>2017-04-19 12:46:11</eventTime>\n" +
                "\t\t\t<eventSource>taobao</eventSource>\n" +
                "\t\t\t<eventTarget>DISTRIBUTOR_8782834</eventTarget>\n" +
                "\t\t</eventHeader>\n" +
                "\t\t<eventBody>\n" +
                "\t\t\t<OrderInfos>\n" +
                "\t\t\t\t<product>\n" +
                "\t\t\t\t\t<productNameCN>手机壳</productNameCN>\n" +
                "\t\t\t\t\t<productNameEN>Phone case</productNameEN>\n" +
                "\t\t\t\t\t<productQantity>1</productQantity>\n" +
                "\t\t\t\t\t<productCateCN>手机壳</productCateCN>\n" +
                "\t\t\t\t\t<productCateEN>Phone case</productCateEN>\n" +
                "\t\t\t\t\t<producingArea/>\n" +
                "\t\t\t\t\t<productWeight>100</productWeight>\n" +
                "\t\t\t\t\t<productPrice>200</productPrice>\n" +
                "\t\t\t\t\t<extendFields>customCode=</extendFields>\n" +
                "\t\t\t\t</product>\n" +
                "\t\t\t</OrderInfos>\n" +
                "\t\t\t<ecCompanyId>taobao</ecCompanyId>\n" +
                "\t\t\t<serviceItemId>5000000011398</serviceItemId>\n" +
                "\t\t\t<bizCode>AE_4PL_STANDARD</bizCode>\n" +
                "\t\t\t<whCode>FPXSS</whCode>\n" +
                "\t\t\t<logisticsOrderId>72048565015</logisticsOrderId>\n" +
                "\t\t\t<tradeId>3547662765</tradeId>\n" +
                "\t\t\t<mailNo>12883402587000201289843001</mailNo>\n" +
                "\t\t\t<Rcountry>CL</Rcountry>\n" +
                "\t\t\t<Rprovince>Santiago</Rprovince>\n" +
                "\t\t\t<Rcity>Santiago</Rcity>\n" +
                "\t\t\t<Remail><EMAIL></Remail>\n" +
                "\t\t\t<Raddress>Amunategui 630 Dpto 910</Raddress>\n" +
                "\t\t\t<Rpostcode>8340258</Rpostcode>\n" +
                "\t\t\t<Rname>Cristian Fuentes Viera</Rname>\n" +
                "\t\t\t<Rphone>56-9-94373172</Rphone>\n" +
                "\t\t\t<Sname>Selina</Sname>\n" +
                "\t\t\t<SwangwangId>cn1512326056</SwangwangId>\n" +
                "\t\t\t<Sprovince>guang dong sheng</Sprovince>\n" +
                "\t\t\t<Scity>shen zhen shi</Scity>\n" +
                "\t\t\t<Saddress>~~~Room 933 , Dongming Building Minzhi Street</Saddress>\n" +
                "\t\t\t<Sphone>13424429337</Sphone>\n" +
                "\t\t\t<Spostcode>518100</Spostcode>\n" +
                "\t\t\t<SstoreName>Rock - Store</SstoreName>\n" +
                "\t\t\t<channel>HK</channel>\n" +
                "\t\t\t<Itotleweight>104</Itotleweight>\n" +
                "\t\t\t<Itotlevalue>200</Itotlevalue>\n" +
                "\t\t\t<totleweight>104</totleweight>\n" +
                "\t\t\t<country>CN</country>\n" +
                "\t\t\t<mailKind>1</mailKind>\n" +
                "\t\t\t<mailClass>L</mailClass>\n" +
                "\t\t\t<batchNo>XA01CL01704190100661</batchNo>\n" +
                "\t\t\t<mailType>SUMAITONG</mailType>\n" +
                "\t\t\t<faceType>2</faceType>\n" +
                "\t\t\t<undeliveryOption>1</undeliveryOption>\n" +
                "\t\t\t<hasBattery>false</hasBattery>\n" +
                "\t\t\t<refunderContact>\n" +
                "\t\t\t\t<wangwangId>cn1512326056</wangwangId>\n" +
                "\t\t\t\t<name>Selina</name>\n" +
                "\t\t\t\t<phone>13424429337</phone>\n" +
                "\t\t\t\t<email/>\n" +
                "\t\t\t\t<country>China</country>\n" +
                "\t\t\t\t<province>guang dong sheng</province>\n" +
                "\t\t\t\t<city>shen zhen shi</city>\n" +
                "\t\t\t\t<street>~~~Room 933 , Dongming Building Minzhi Street</street>\n" +
                "\t\t\t\t<zipCode>518100</zipCode>\n" +
                "\t\t\t\t<district>long hua xin qu</district>\n" +
                "\t\t\t</refunderContact>\n" +
                "\t\t\t<pickUpAddress>中国广东省深圳市罗湖区清水河街道详细地址</pickUpAddress>\n" +
                "\t\t\t<packageCode>XA01CL01704190100661</packageCode>\n" +
                "\t\t\t<orderCode>LP00072048565015</orderCode>\n" +
                "\t\t\t<packageWeight>9876</packageWeight>\n" +
                "\t\t\t<ordersInPackage>44</ordersInPackage>\n" +
                "\t\t</eventBody>\n" +
                "\t</logisticsEvent>\n" +
                "</logisticsEventsRequest>";
        String result = cainiaoService.batchTransaction(xml);
        System.out.printf(result);

    }


}